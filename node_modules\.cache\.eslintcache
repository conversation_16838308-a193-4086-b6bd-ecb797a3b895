[{"D:\\Code\\FE_Blog\\src\\index.js": "1", "D:\\Code\\FE_Blog\\src\\App.js": "2", "D:\\Code\\FE_Blog\\src\\reportWebVitals.js": "3", "D:\\Code\\FE_Blog\\src\\Stats.js": "4", "D:\\Code\\FE_Blog\\src\\Routes\\AppLayout.js": "5", "D:\\Code\\FE_Blog\\src\\Routes\\ProtectedRoute.js": "6", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\PostLists.js": "7", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Posts.js": "8", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\NewPost.js": "9", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Post.js": "10", "D:\\Code\\FE_Blog\\src\\Pages\\Home\\Home.js": "11", "D:\\Code\\FE_Blog\\src\\Pages\\About\\About.js": "12", "D:\\Code\\FE_Blog\\src\\Pages\\Login\\Login.js": "13", "D:\\Code\\FE_Blog\\src\\Pages\\NotFound\\NoMatch.js": "14", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostDetail\\PostDetail.js": "15", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Header\\Header.js": "16", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostList\\PostList.js": "17", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\UserList.js": "18", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostCard\\PostCard.js": "19", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentForm\\CommentForm.js": "20", "D:\\Code\\FE_Blog\\src\\Components\\Common\\Modal\\Modal.js": "21", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentList\\CommentList.js": "22", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentItem\\CommentItem.js": "23", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\UserItem\\UserItem.js": "24", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\Sidebar.js": "25", "D:\\Code\\FE_Blog\\src\\Pages\\UserProfile\\UserProfile.js": "26", "D:\\Code\\FE_Blog\\src\\Pages\\Register\\Register.js": "27", "D:\\Code\\FE_Blog\\src\\Components\\Post\\CreatePost\\CreatePost.js": "28", "D:\\Code\\FE_Blog\\src\\Components\\Common\\Modal\\CreatePostModal.js": "29"}, {"size": 552, "mtime": 1748937558000, "results": "30", "hashOfConfig": "31"}, {"size": 231, "mtime": 1748964154027, "results": "32", "hashOfConfig": "31"}, {"size": 377, "mtime": 1748937558000, "results": "33", "hashOfConfig": "31"}, {"size": 490, "mtime": 1748937558000, "results": "34", "hashOfConfig": "31"}, {"size": 2248, "mtime": 1748979136135, "results": "35", "hashOfConfig": "31"}, {"size": 217, "mtime": 1748964429418, "results": "36", "hashOfConfig": "31"}, {"size": 1111, "mtime": 1748964750924, "results": "37", "hashOfConfig": "31"}, {"size": 339, "mtime": 1748964682397, "results": "38", "hashOfConfig": "31"}, {"size": 1773, "mtime": 1748964415864, "results": "39", "hashOfConfig": "31"}, {"size": 773, "mtime": 1748964689637, "results": "40", "hashOfConfig": "31"}, {"size": 3744, "mtime": 1748979153921, "results": "41", "hashOfConfig": "31"}, {"size": 401, "mtime": 1748975181894, "results": "42", "hashOfConfig": "31"}, {"size": 4002, "mtime": 1748982148502, "results": "43", "hashOfConfig": "31"}, {"size": 269, "mtime": 1748964417057, "results": "44", "hashOfConfig": "31"}, {"size": 1483, "mtime": 1748976684031, "results": "45", "hashOfConfig": "31"}, {"size": 1682, "mtime": 1748977847457, "results": "46", "hashOfConfig": "31"}, {"size": 591, "mtime": 1748978202008, "results": "47", "hashOfConfig": "31"}, {"size": 1575, "mtime": 1748975490708, "results": "48", "hashOfConfig": "31"}, {"size": 962, "mtime": 1748976585802, "results": "49", "hashOfConfig": "31"}, {"size": 942, "mtime": 1748972827903, "results": "50", "hashOfConfig": "31"}, {"size": 498, "mtime": 1748972818007, "results": "51", "hashOfConfig": "31"}, {"size": 359, "mtime": 1748972832549, "results": "52", "hashOfConfig": "31"}, {"size": 757, "mtime": 1748972829882, "results": "53", "hashOfConfig": "31"}, {"size": 505, "mtime": 1748978225944, "results": "54", "hashOfConfig": "31"}, {"size": 224, "mtime": 1748975415155, "results": "55", "hashOfConfig": "31"}, {"size": 4644, "mtime": 1748978367358, "results": "56", "hashOfConfig": "31"}, {"size": 3880, "mtime": 1748977976052, "results": "57", "hashOfConfig": "31"}, {"size": 1668, "mtime": 1748979235518, "results": "58", "hashOfConfig": "31"}, {"size": 3824, "mtime": 1748979191930, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "65zzvo", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Code\\FE_Blog\\src\\index.js", [], [], "D:\\Code\\FE_Blog\\src\\App.js", [], [], "D:\\Code\\FE_Blog\\src\\reportWebVitals.js", [], [], "D:\\Code\\FE_Blog\\src\\Stats.js", [], [], "D:\\Code\\FE_Blog\\src\\Routes\\AppLayout.js", ["147"], [], "D:\\Code\\FE_Blog\\src\\Routes\\ProtectedRoute.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\PostLists.js", ["148", "149", "150"], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Posts.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\NewPost.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Post.js", ["151"], [], "D:\\Code\\FE_Blog\\src\\Pages\\Home\\Home.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\About\\About.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Login\\Login.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\NotFound\\NoMatch.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostDetail\\PostDetail.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Header\\Header.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostList\\PostList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\UserList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostCard\\PostCard.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentForm\\CommentForm.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Common\\Modal\\Modal.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentList\\CommentList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentItem\\CommentItem.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\UserItem\\UserItem.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\Sidebar.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\UserProfile\\UserProfile.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Register\\Register.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\CreatePost\\CreatePost.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Common\\Modal\\CreatePostModal.js", [], [], {"ruleId": "152", "severity": 1, "message": "153", "line": 2, "column": 38, "nodeType": "154", "messageId": "155", "endLine": 2, "endColumn": 42}, {"ruleId": "152", "severity": 1, "message": "156", "line": 8, "column": 10, "nodeType": "154", "messageId": "155", "endLine": 8, "endColumn": 17}, {"ruleId": "152", "severity": 1, "message": "157", "line": 9, "column": 10, "nodeType": "154", "messageId": "155", "endLine": 9, "endColumn": 15}, {"ruleId": "152", "severity": 1, "message": "158", "line": 11, "column": 11, "nodeType": "154", "messageId": "155", "endLine": 11, "endColumn": 20}, {"ruleId": "159", "severity": 1, "message": "160", "line": 20, "column": 6, "nodeType": "161", "endLine": 20, "endColumn": 8, "suggestions": "162"}, "no-unused-vars", "'Link' is defined but never used.", "Identifier", "unusedVar", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'fetchData' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'slug'. Either include it or remove the dependency array.", "ArrayExpression", ["163"], {"desc": "164", "fix": "165"}, "Update the dependencies array to be: [slug]", {"range": "166", "text": "167"}, [607, 609], "[slug]"]