[{"D:\\Code\\FE_Blog\\src\\index.js": "1", "D:\\Code\\FE_Blog\\src\\App.js": "2", "D:\\Code\\FE_Blog\\src\\reportWebVitals.js": "3", "D:\\Code\\FE_Blog\\src\\Stats.js": "4", "D:\\Code\\FE_Blog\\src\\Routes\\AppLayout.js": "5", "D:\\Code\\FE_Blog\\src\\Routes\\ProtectedRoute.js": "6", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\PostLists.js": "7", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Posts.js": "8", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\NewPost.js": "9", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Post.js": "10", "D:\\Code\\FE_Blog\\src\\Pages\\Home\\Home.js": "11", "D:\\Code\\FE_Blog\\src\\Pages\\About\\About.js": "12", "D:\\Code\\FE_Blog\\src\\Pages\\Login\\Login.js": "13", "D:\\Code\\FE_Blog\\src\\Pages\\NotFound\\NoMatch.js": "14", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostDetail\\PostDetail.js": "15", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Header\\Header.js": "16", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostList\\PostList.js": "17", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\UserList.js": "18", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostCard\\PostCard.js": "19", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentForm\\CommentForm.js": "20", "D:\\Code\\FE_Blog\\src\\Components\\Common\\Modal\\Modal.js": "21", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentList\\CommentList.js": "22", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentItem\\CommentItem.js": "23"}, {"size": 552, "mtime": 1748937558000, "results": "24", "hashOfConfig": "25"}, {"size": 231, "mtime": 1748964154027, "results": "26", "hashOfConfig": "25"}, {"size": 377, "mtime": 1748937558000, "results": "27", "hashOfConfig": "25"}, {"size": 490, "mtime": 1748937558000, "results": "28", "hashOfConfig": "25"}, {"size": 2457, "mtime": 1748964720418, "results": "29", "hashOfConfig": "25"}, {"size": 217, "mtime": 1748964429418, "results": "30", "hashOfConfig": "25"}, {"size": 1111, "mtime": 1748964750924, "results": "31", "hashOfConfig": "25"}, {"size": 339, "mtime": 1748964682397, "results": "32", "hashOfConfig": "25"}, {"size": 1773, "mtime": 1748964415864, "results": "33", "hashOfConfig": "25"}, {"size": 773, "mtime": 1748964689637, "results": "34", "hashOfConfig": "25"}, {"size": 1449, "mtime": 1748972222617, "results": "35", "hashOfConfig": "25"}, {"size": 371, "mtime": 1748964420799, "results": "36", "hashOfConfig": "25"}, {"size": 1496, "mtime": 1748964514653, "results": "37", "hashOfConfig": "25"}, {"size": 269, "mtime": 1748964417057, "results": "38", "hashOfConfig": "25"}, {"size": 1314, "mtime": 1748972489873, "results": "39", "hashOfConfig": "25"}, {"size": 1697, "mtime": 1748972246935, "results": "40", "hashOfConfig": "25"}, {"size": 2756, "mtime": 1748972455101, "results": "41", "hashOfConfig": "25"}, {"size": 1583, "mtime": 1748972300740, "results": "42", "hashOfConfig": "25"}, {"size": 962, "mtime": 1748972469795, "results": "43", "hashOfConfig": "25"}, {"size": 942, "mtime": 1748972525724, "results": "44", "hashOfConfig": "25"}, {"size": 498, "mtime": 1748972539489, "results": "45", "hashOfConfig": "25"}, {"size": 359, "mtime": 1748972503180, "results": "46", "hashOfConfig": "25"}, {"size": 757, "mtime": 1748972511828, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "65zzvo", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Code\\FE_Blog\\src\\index.js", [], [], "D:\\Code\\FE_Blog\\src\\App.js", [], [], "D:\\Code\\FE_Blog\\src\\reportWebVitals.js", [], [], "D:\\Code\\FE_Blog\\src\\Stats.js", [], [], "D:\\Code\\FE_Blog\\src\\Routes\\AppLayout.js", [], [], "D:\\Code\\FE_Blog\\src\\Routes\\ProtectedRoute.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\PostLists.js", ["117", "118", "119"], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Posts.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\NewPost.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Post.js", ["120"], [], "D:\\Code\\FE_Blog\\src\\Pages\\Home\\Home.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\About\\About.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Login\\Login.js", ["121", "122"], [], "D:\\Code\\FE_Blog\\src\\Pages\\NotFound\\NoMatch.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostDetail\\PostDetail.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Header\\Header.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostList\\PostList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\UserList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostCard\\PostCard.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentForm\\CommentForm.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Common\\Modal\\Modal.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentList\\CommentList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentItem\\CommentItem.js", [], [], {"ruleId": "123", "severity": 1, "message": "124", "line": 8, "column": 10, "nodeType": "125", "messageId": "126", "endLine": 8, "endColumn": 17}, {"ruleId": "123", "severity": 1, "message": "127", "line": 9, "column": 10, "nodeType": "125", "messageId": "126", "endLine": 9, "endColumn": 15}, {"ruleId": "123", "severity": 1, "message": "128", "line": 11, "column": 11, "nodeType": "125", "messageId": "126", "endLine": 11, "endColumn": 20}, {"ruleId": "129", "severity": 1, "message": "130", "line": 20, "column": 6, "nodeType": "131", "endLine": 20, "endColumn": 8, "suggestions": "132"}, {"ruleId": "123", "severity": 1, "message": "133", "line": 7, "column": 17, "nodeType": "125", "messageId": "126", "endLine": 7, "endColumn": 25}, {"ruleId": "123", "severity": 1, "message": "134", "line": 8, "column": 9, "nodeType": "125", "messageId": "126", "endLine": 8, "endColumn": 17}, "no-unused-vars", "'loading' is assigned a value but never used.", "Identifier", "unusedVar", "'error' is assigned a value but never used.", "'fetchData' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'slug'. Either include it or remove the dependency array.", "ArrayExpression", ["135"], "'setError' is assigned a value but never used.", "'navigate' is assigned a value but never used.", {"desc": "136", "fix": "137"}, "Update the dependencies array to be: [slug]", {"range": "138", "text": "139"}, [607, 609], "[slug]"]