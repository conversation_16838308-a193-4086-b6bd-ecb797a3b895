[{"D:\\Code\\FE_Blog\\src\\index.js": "1", "D:\\Code\\FE_Blog\\src\\App.js": "2", "D:\\Code\\FE_Blog\\src\\reportWebVitals.js": "3", "D:\\Code\\FE_Blog\\src\\Stats.js": "4", "D:\\Code\\FE_Blog\\src\\Routes\\AppLayout.js": "5", "D:\\Code\\FE_Blog\\src\\Routes\\ProtectedRoute.js": "6", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\PostLists.js": "7", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Posts.js": "8", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\NewPost.js": "9", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Post.js": "10", "D:\\Code\\FE_Blog\\src\\Pages\\Home\\Home.js": "11", "D:\\Code\\FE_Blog\\src\\Pages\\About\\About.js": "12", "D:\\Code\\FE_Blog\\src\\Pages\\Login\\Login.js": "13", "D:\\Code\\FE_Blog\\src\\Pages\\NotFound\\NoMatch.js": "14", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostDetail\\PostDetail.js": "15", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Header\\Header.js": "16", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostList\\PostList.js": "17", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\UserList.js": "18", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostCard\\PostCard.js": "19", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentForm\\CommentForm.js": "20", "D:\\Code\\FE_Blog\\src\\Components\\Common\\Modal\\Modal.js": "21", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentList\\CommentList.js": "22", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentItem\\CommentItem.js": "23", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\UserItem\\UserItem.js": "24"}, {"size": 552, "mtime": 1748937558000, "results": "25", "hashOfConfig": "26"}, {"size": 231, "mtime": 1748964154027, "results": "27", "hashOfConfig": "26"}, {"size": 377, "mtime": 1748937558000, "results": "28", "hashOfConfig": "26"}, {"size": 490, "mtime": 1748937558000, "results": "29", "hashOfConfig": "26"}, {"size": 2471, "mtime": 1748975216691, "results": "30", "hashOfConfig": "26"}, {"size": 217, "mtime": 1748964429418, "results": "31", "hashOfConfig": "26"}, {"size": 1111, "mtime": 1748964750924, "results": "32", "hashOfConfig": "26"}, {"size": 339, "mtime": 1748964682397, "results": "33", "hashOfConfig": "26"}, {"size": 1773, "mtime": 1748964415864, "results": "34", "hashOfConfig": "26"}, {"size": 773, "mtime": 1748964689637, "results": "35", "hashOfConfig": "26"}, {"size": 1449, "mtime": 1748972783890, "results": "36", "hashOfConfig": "26"}, {"size": 401, "mtime": 1748975181894, "results": "37", "hashOfConfig": "26"}, {"size": 1496, "mtime": 1748964514653, "results": "38", "hashOfConfig": "26"}, {"size": 269, "mtime": 1748964417057, "results": "39", "hashOfConfig": "26"}, {"size": 1485, "mtime": 1748975193950, "results": "40", "hashOfConfig": "26"}, {"size": 1776, "mtime": 1748975152561, "results": "41", "hashOfConfig": "26"}, {"size": 2753, "mtime": 1748975136288, "results": "42", "hashOfConfig": "26"}, {"size": 1583, "mtime": 1748975123446, "results": "43", "hashOfConfig": "26"}, {"size": 962, "mtime": 1748972798769, "results": "44", "hashOfConfig": "26"}, {"size": 942, "mtime": 1748972827903, "results": "45", "hashOfConfig": "26"}, {"size": 498, "mtime": 1748972818007, "results": "46", "hashOfConfig": "26"}, {"size": 359, "mtime": 1748972832549, "results": "47", "hashOfConfig": "26"}, {"size": 757, "mtime": 1748972829882, "results": "48", "hashOfConfig": "26"}, {"size": 320, "mtime": 1748972805537, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "65zzvo", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Code\\FE_Blog\\src\\index.js", [], [], "D:\\Code\\FE_Blog\\src\\App.js", [], [], "D:\\Code\\FE_Blog\\src\\reportWebVitals.js", [], [], "D:\\Code\\FE_Blog\\src\\Stats.js", [], [], "D:\\Code\\FE_Blog\\src\\Routes\\AppLayout.js", ["122", "123", "124", "125"], [], "D:\\Code\\FE_Blog\\src\\Routes\\ProtectedRoute.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\PostLists.js", ["126", "127", "128"], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Posts.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\NewPost.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Post.js", ["129"], [], "D:\\Code\\FE_Blog\\src\\Pages\\Home\\Home.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\About\\About.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Login\\Login.js", ["130", "131"], [], "D:\\Code\\FE_Blog\\src\\Pages\\NotFound\\NoMatch.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostDetail\\PostDetail.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Header\\Header.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostList\\PostList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\UserList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostCard\\PostCard.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentForm\\CommentForm.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Common\\Modal\\Modal.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentList\\CommentList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentItem\\CommentItem.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\UserItem\\UserItem.js", [], [], {"ruleId": "132", "severity": 1, "message": "133", "line": 2, "column": 38, "nodeType": "134", "messageId": "135", "endLine": 2, "endColumn": 42}, {"ruleId": "136", "severity": 2, "message": "137", "line": 16, "column": 3, "nodeType": "138", "messageId": "139", "endLine": 16, "endColumn": 5}, {"ruleId": "140", "severity": 2, "message": "141", "line": 16, "column": 3, "nodeType": "134", "messageId": "142", "endLine": 16, "endColumn": 4}, {"ruleId": "132", "severity": 1, "message": "143", "line": 20, "column": 12, "nodeType": "134", "messageId": "135", "endLine": 20, "endColumn": 18}, {"ruleId": "132", "severity": 1, "message": "144", "line": 8, "column": 10, "nodeType": "134", "messageId": "135", "endLine": 8, "endColumn": 17}, {"ruleId": "132", "severity": 1, "message": "145", "line": 9, "column": 10, "nodeType": "134", "messageId": "135", "endLine": 9, "endColumn": 15}, {"ruleId": "132", "severity": 1, "message": "146", "line": 11, "column": 11, "nodeType": "134", "messageId": "135", "endLine": 11, "endColumn": 20}, {"ruleId": "147", "severity": 1, "message": "148", "line": 20, "column": 6, "nodeType": "149", "endLine": 20, "endColumn": 8, "suggestions": "150"}, {"ruleId": "132", "severity": 1, "message": "151", "line": 7, "column": 17, "nodeType": "134", "messageId": "135", "endLine": 7, "endColumn": 25}, {"ruleId": "132", "severity": 1, "message": "152", "line": 8, "column": 9, "nodeType": "134", "messageId": "135", "endLine": 8, "endColumn": 17}, "no-unused-vars", "'Link' is defined but never used.", "Identifier", "unusedVar", "no-unused-expressions", "Expected an assignment or function call and instead saw an expression.", "ExpressionStatement", "unusedExpression", "no-undef", "'c' is not defined.", "undef", "'logOut' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'fetchData' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'slug'. Either include it or remove the dependency array.", "ArrayExpression", ["153"], "'setError' is assigned a value but never used.", "'navigate' is assigned a value but never used.", {"desc": "154", "fix": "155"}, "Update the dependencies array to be: [slug]", {"range": "156", "text": "157"}, [607, 609], "[slug]"]