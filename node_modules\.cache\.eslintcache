[{"D:\\Code\\FE_Blog\\src\\index.js": "1", "D:\\Code\\FE_Blog\\src\\App.js": "2", "D:\\Code\\FE_Blog\\src\\reportWebVitals.js": "3", "D:\\Code\\FE_Blog\\src\\Stats.js": "4", "D:\\Code\\FE_Blog\\src\\Routes\\AppLayout.js": "5", "D:\\Code\\FE_Blog\\src\\Routes\\ProtectedRoute.js": "6", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\PostLists.js": "7", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Posts.js": "8", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\NewPost.js": "9", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Post.js": "10", "D:\\Code\\FE_Blog\\src\\Pages\\Home\\Home.js": "11", "D:\\Code\\FE_Blog\\src\\Pages\\About\\About.js": "12", "D:\\Code\\FE_Blog\\src\\Pages\\Login\\Login.js": "13", "D:\\Code\\FE_Blog\\src\\Pages\\NotFound\\NoMatch.js": "14"}, {"size": 552, "mtime": 1748937558000, "results": "15", "hashOfConfig": "16"}, {"size": 231, "mtime": 1748964154027, "results": "17", "hashOfConfig": "16"}, {"size": 377, "mtime": 1748937558000, "results": "18", "hashOfConfig": "16"}, {"size": 490, "mtime": 1748937558000, "results": "19", "hashOfConfig": "16"}, {"size": 2457, "mtime": 1748964720418, "results": "20", "hashOfConfig": "16"}, {"size": 217, "mtime": 1748964429418, "results": "21", "hashOfConfig": "16"}, {"size": 1111, "mtime": 1748964750924, "results": "22", "hashOfConfig": "16"}, {"size": 339, "mtime": 1748964682397, "results": "23", "hashOfConfig": "16"}, {"size": 1773, "mtime": 1748964415864, "results": "24", "hashOfConfig": "16"}, {"size": 773, "mtime": 1748964689637, "results": "25", "hashOfConfig": "16"}, {"size": 365, "mtime": 1748964419567, "results": "26", "hashOfConfig": "16"}, {"size": 371, "mtime": 1748964420799, "results": "27", "hashOfConfig": "16"}, {"size": 1496, "mtime": 1748964514653, "results": "28", "hashOfConfig": "16"}, {"size": 269, "mtime": 1748964417057, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "65zzvo", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Code\\FE_Blog\\src\\index.js", [], [], "D:\\Code\\FE_Blog\\src\\App.js", [], [], "D:\\Code\\FE_Blog\\src\\reportWebVitals.js", [], [], "D:\\Code\\FE_Blog\\src\\Stats.js", [], [], "D:\\Code\\FE_Blog\\src\\Routes\\AppLayout.js", [], [], "D:\\Code\\FE_Blog\\src\\Routes\\ProtectedRoute.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\PostLists.js", ["72", "73", "74"], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Posts.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\NewPost.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Post.js", ["75"], [], "D:\\Code\\FE_Blog\\src\\Pages\\Home\\Home.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\About\\About.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Login\\Login.js", ["76", "77"], [], "D:\\Code\\FE_Blog\\src\\Pages\\NotFound\\NoMatch.js", [], [], {"ruleId": "78", "severity": 1, "message": "79", "line": 8, "column": 10, "nodeType": "80", "messageId": "81", "endLine": 8, "endColumn": 17}, {"ruleId": "78", "severity": 1, "message": "82", "line": 9, "column": 10, "nodeType": "80", "messageId": "81", "endLine": 9, "endColumn": 15}, {"ruleId": "78", "severity": 1, "message": "83", "line": 11, "column": 11, "nodeType": "80", "messageId": "81", "endLine": 11, "endColumn": 20}, {"ruleId": "84", "severity": 1, "message": "85", "line": 20, "column": 6, "nodeType": "86", "endLine": 20, "endColumn": 8, "suggestions": "87"}, {"ruleId": "78", "severity": 1, "message": "88", "line": 7, "column": 17, "nodeType": "80", "messageId": "81", "endLine": 7, "endColumn": 25}, {"ruleId": "78", "severity": 1, "message": "89", "line": 8, "column": 9, "nodeType": "80", "messageId": "81", "endLine": 8, "endColumn": 17}, "no-unused-vars", "'loading' is assigned a value but never used.", "Identifier", "unusedVar", "'error' is assigned a value but never used.", "'fetchData' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'slug'. Either include it or remove the dependency array.", "ArrayExpression", ["90"], "'setError' is assigned a value but never used.", "'navigate' is assigned a value but never used.", {"desc": "91", "fix": "92"}, "Update the dependencies array to be: [slug]", {"range": "93", "text": "94"}, [607, 609], "[slug]"]