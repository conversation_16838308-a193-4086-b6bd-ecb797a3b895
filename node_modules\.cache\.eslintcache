[{"D:\\Code\\FE_Blog\\src\\index.js": "1", "D:\\Code\\FE_Blog\\src\\App.js": "2", "D:\\Code\\FE_Blog\\src\\reportWebVitals.js": "3", "D:\\Code\\FE_Blog\\src\\Stats.js": "4", "D:\\Code\\FE_Blog\\src\\Routes\\AppLayout.js": "5", "D:\\Code\\FE_Blog\\src\\Routes\\ProtectedRoute.js": "6", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\PostLists.js": "7", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Posts.js": "8", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\NewPost.js": "9", "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Post.js": "10", "D:\\Code\\FE_Blog\\src\\Pages\\Home\\Home.js": "11", "D:\\Code\\FE_Blog\\src\\Pages\\About\\About.js": "12", "D:\\Code\\FE_Blog\\src\\Pages\\Login\\Login.js": "13", "D:\\Code\\FE_Blog\\src\\Pages\\NotFound\\NoMatch.js": "14", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostDetail\\PostDetail.js": "15", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Header\\Header.js": "16", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostList\\PostList.js": "17", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\UserList.js": "18", "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostCard\\PostCard.js": "19", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentForm\\CommentForm.js": "20", "D:\\Code\\FE_Blog\\src\\Components\\Common\\Modal\\Modal.js": "21", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentList\\CommentList.js": "22", "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentItem\\CommentItem.js": "23", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\UserItem\\UserItem.js": "24", "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\Sidebar.js": "25"}, {"size": 552, "mtime": 1748937558000, "results": "26", "hashOfConfig": "27"}, {"size": 231, "mtime": 1748964154027, "results": "28", "hashOfConfig": "27"}, {"size": 377, "mtime": 1748937558000, "results": "29", "hashOfConfig": "27"}, {"size": 490, "mtime": 1748937558000, "results": "30", "hashOfConfig": "27"}, {"size": 2465, "mtime": 1748975499380, "results": "31", "hashOfConfig": "27"}, {"size": 217, "mtime": 1748964429418, "results": "32", "hashOfConfig": "27"}, {"size": 1111, "mtime": 1748964750924, "results": "33", "hashOfConfig": "27"}, {"size": 339, "mtime": 1748964682397, "results": "34", "hashOfConfig": "27"}, {"size": 1773, "mtime": 1748964415864, "results": "35", "hashOfConfig": "27"}, {"size": 773, "mtime": 1748964689637, "results": "36", "hashOfConfig": "27"}, {"size": 1446, "mtime": 1748975444140, "results": "37", "hashOfConfig": "27"}, {"size": 401, "mtime": 1748975181894, "results": "38", "hashOfConfig": "27"}, {"size": 2332, "mtime": 1748977937933, "results": "39", "hashOfConfig": "27"}, {"size": 269, "mtime": 1748964417057, "results": "40", "hashOfConfig": "27"}, {"size": 1483, "mtime": 1748976684031, "results": "41", "hashOfConfig": "27"}, {"size": 1682, "mtime": 1748977847457, "results": "42", "hashOfConfig": "27"}, {"size": 2753, "mtime": 1748975136288, "results": "43", "hashOfConfig": "27"}, {"size": 1575, "mtime": 1748975490708, "results": "44", "hashOfConfig": "27"}, {"size": 962, "mtime": 1748976585802, "results": "45", "hashOfConfig": "27"}, {"size": 942, "mtime": 1748972827903, "results": "46", "hashOfConfig": "27"}, {"size": 498, "mtime": 1748972818007, "results": "47", "hashOfConfig": "27"}, {"size": 359, "mtime": 1748972832549, "results": "48", "hashOfConfig": "27"}, {"size": 757, "mtime": 1748972829882, "results": "49", "hashOfConfig": "27"}, {"size": 320, "mtime": 1748972805537, "results": "50", "hashOfConfig": "27"}, {"size": 224, "mtime": 1748975415155, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "65zzvo", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Code\\FE_Blog\\src\\index.js", [], [], "D:\\Code\\FE_Blog\\src\\App.js", [], [], "D:\\Code\\FE_Blog\\src\\reportWebVitals.js", [], [], "D:\\Code\\FE_Blog\\src\\Stats.js", [], [], "D:\\Code\\FE_Blog\\src\\Routes\\AppLayout.js", ["127", "128"], [], "D:\\Code\\FE_Blog\\src\\Routes\\ProtectedRoute.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\PostLists.js", ["129", "130", "131"], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Posts.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\NewPost.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Post\\Post.js", ["132"], [], "D:\\Code\\FE_Blog\\src\\Pages\\Home\\Home.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\About\\About.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\Login\\Login.js", [], [], "D:\\Code\\FE_Blog\\src\\Pages\\NotFound\\NoMatch.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostDetail\\PostDetail.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Header\\Header.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostList\\PostList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\UserList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Post\\PostCard\\PostCard.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentForm\\CommentForm.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Common\\Modal\\Modal.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentList\\CommentList.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Comment\\CommentItem\\CommentItem.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\UserItem\\UserItem.js", [], [], "D:\\Code\\FE_Blog\\src\\Components\\Layout\\Sidebar\\Sidebar.js", [], [], {"ruleId": "133", "severity": 1, "message": "134", "line": 2, "column": 38, "nodeType": "135", "messageId": "136", "endLine": 2, "endColumn": 42}, {"ruleId": "133", "severity": 1, "message": "137", "line": 19, "column": 12, "nodeType": "135", "messageId": "136", "endLine": 19, "endColumn": 18}, {"ruleId": "133", "severity": 1, "message": "138", "line": 8, "column": 10, "nodeType": "135", "messageId": "136", "endLine": 8, "endColumn": 17}, {"ruleId": "133", "severity": 1, "message": "139", "line": 9, "column": 10, "nodeType": "135", "messageId": "136", "endLine": 9, "endColumn": 15}, {"ruleId": "133", "severity": 1, "message": "140", "line": 11, "column": 11, "nodeType": "135", "messageId": "136", "endLine": 11, "endColumn": 20}, {"ruleId": "141", "severity": 1, "message": "142", "line": 20, "column": 6, "nodeType": "143", "endLine": 20, "endColumn": 8, "suggestions": "144"}, "no-unused-vars", "'Link' is defined but never used.", "Identifier", "unusedVar", "'logOut' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'fetchData' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'slug'. Either include it or remove the dependency array.", "ArrayExpression", ["145"], {"desc": "146", "fix": "147"}, "Update the dependencies array to be: [slug]", {"range": "148", "text": "149"}, [607, 609], "[slug]"]