{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Layout\\\\UserItem\\\\UserItem.js\";\nimport React from \"react\";\nimport \"./UserItem.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction UserItem({\n  user\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-item\",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: user.avatar,\n      alt: user.name,\n      className: \"user-item__avatar\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"user-item__name\",\n      children: user.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = UserItem;\nexport default UserItem;\nvar _c;\n$RefreshReg$(_c, \"UserItem\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "UserItem", "user", "className", "children", "src", "avatar", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Layout/UserItem/UserItem.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./UserItem.css\";\r\n\r\nfunction UserItem({ user }) {\r\n  return (\r\n    <div className=\"user-item\">\r\n      <img src={user.avatar} alt={user.name} className=\"user-item__avatar\" />\r\n      <span className=\"user-item__name\">{user.name}</span>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserItem;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,SAASC,QAAQA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAC1B,oBACEF,OAAA;IAAKG,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBJ,OAAA;MAAKK,GAAG,EAAEH,IAAI,CAACI,MAAO;MAACC,GAAG,EAAEL,IAAI,CAACM,IAAK;MAACL,SAAS,EAAC;IAAmB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvEZ,OAAA;MAAMG,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAAEF,IAAI,CAACM;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjD,CAAC;AAEV;AAACC,EAAA,GAPQZ,QAAQ;AASjB,eAAeA,QAAQ;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}