{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\Post\\\\PostLists.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport \"../../styles.css\";\nimport { Link } from \"react-router-dom\";\nimport { useState, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function PostLists() {\n  _s();\n  const [data, setData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const response = await fetch(\"http://localhost:8080/api/post\");\n        if (!response.ok) {\n          throw new Error(`HTTP error! Status: ${response.status}`);\n        }\n        const result = await response.json();\n        setData(result);\n        setLoading(false);\n      } catch (error) {\n        console.error(\"Error fetching data:\", error);\n        setError(\"An error occurred while fetching the data.\");\n        setLoading(false);\n      }\n    };\n    // fetchData();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"ul\", {\n    children: data.map(d => /*#__PURE__*/_jsxDEV(\"li\", {\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: `/posts/${d.slug}`,\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: d.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 11\n      }, this)\n    }, d.slug, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n}\n_s(PostLists, \"C4fiAW6C7RZgaKDoEXQgZpbuUZg=\");\n_c = PostLists;\nvar _c;\n$RefreshReg$(_c, \"PostLists\");", "map": {"version": 3, "names": ["React", "Link", "useState", "useEffect", "jsxDEV", "_jsxDEV", "PostLists", "_s", "data", "setData", "loading", "setLoading", "error", "setError", "fetchData", "response", "fetch", "ok", "Error", "status", "result", "json", "console", "children", "map", "d", "to", "slug", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/Post/PostLists.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"../../styles.css\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { useState, useEffect } from \"react\";\r\n\r\nexport default function PostLists() {\r\n  const [data, setData] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        const response = await fetch(\"http://localhost:8080/api/post\");\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP error! Status: ${response.status}`);\r\n        }\r\n        const result = await response.json();\r\n        setData(result);\r\n        setLoading(false);\r\n      } catch (error) {\r\n        console.error(\"Error fetching data:\", error);\r\n        setError(\"An error occurred while fetching the data.\");\r\n        setLoading(false);\r\n      }\r\n    };\r\n    // fetchData();\r\n  }, []);\r\n  return (\r\n    <ul>\r\n      {data.map((d) => (\r\n        <li key={d.slug}>\r\n          <Link to={`/posts/${d.slug}`}>\r\n            <h3>{d.title}</h3>\r\n          </Link>\r\n        </li>\r\n      ))}\r\n    </ul>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,kBAAkB;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,eAAe,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACxCC,SAAS,CAAC,MAAM;IACd,MAAMW,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,CAAC;QAC9D,IAAI,CAACD,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;QAC3D;QACA,MAAMC,MAAM,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACpCZ,OAAO,CAACW,MAAM,CAAC;QACfT,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdU,OAAO,CAACV,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CC,QAAQ,CAAC,4CAA4C,CAAC;QACtDF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACD;EACF,CAAC,EAAE,EAAE,CAAC;EACN,oBACEN,OAAA;IAAAkB,QAAA,EACGf,IAAI,CAACgB,GAAG,CAAEC,CAAC,iBACVpB,OAAA;MAAAkB,QAAA,eACElB,OAAA,CAACJ,IAAI;QAACyB,EAAE,EAAE,UAAUD,CAAC,CAACE,IAAI,EAAG;QAAAJ,QAAA,eAC3BlB,OAAA;UAAAkB,QAAA,EAAKE,CAAC,CAACG;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IAAC,GAHAP,CAAC,CAACE,IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIX,CACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAET;AAACzB,EAAA,CAjCuBD,SAAS;AAAA2B,EAAA,GAAT3B,SAAS;AAAA,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}