.comment-form {
  width: 100%;
}

.comment-form__input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.comment-form__input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e4e6ea;
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  background-color: #f0f2f5;
}

.comment-form__input:focus {
  border-color: #1877f2;
  background-color: #ffffff;
}

.comment-form__submit-btn {
  background-color: #1877f2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.comment-form__submit-btn:hover:not(:disabled) {
  background-color: #166fe5;
}

.comment-form__submit-btn:disabled {
  background-color: #e4e6ea;
  color: #bcc0c4;
  cursor: not-allowed;
}