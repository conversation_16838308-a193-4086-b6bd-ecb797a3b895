{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Layout\\\\Sidebar\\\\UserList.js\";\nimport React from \"react\";\nimport \"./UserList.css\";\nimport UserItem from \"../../User/UserItem/UserItem\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction UserList() {\n  const fakeUsers = [{\n    id: 1,\n    name: \"Tiến Dương\",\n    avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n  }, {\n    id: 2,\n    name: \"Meta AI\",\n    avatar: \"https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face\"\n  }, {\n    id: 3,\n    name: \"<PERSON><PERSON><PERSON> b<PERSON>\",\n    avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\"\n  }, {\n    id: 4,\n    name: \"<PERSON><PERSON>\",\n    avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\"\n  }, {\n    id: 5,\n    name: \"Đã lưu\",\n    avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\"\n  }, {\n    id: 6,\n    name: \"Nhóm\",\n    avatar: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-list\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-list__header\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"user-list__title\",\n        children: \"Ng\\u01B0\\u1EDDi li\\xEAn h\\u1EC7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-list__content\",\n      children: fakeUsers.map(user => /*#__PURE__*/_jsxDEV(UserItem, {\n        user: user\n      }, user.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_c = UserList;\nexport default UserList;\nvar _c;\n$RefreshReg$(_c, \"UserList\");", "map": {"version": 3, "names": ["React", "UserItem", "jsxDEV", "_jsxDEV", "UserList", "fakeUsers", "id", "name", "avatar", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "user", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Layout/Sidebar/UserList.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./UserList.css\";\r\nimport UserItem from \"../../User/UserItem/UserItem\";\r\n\r\nfunction UserList() {\r\n  const fakeUsers = [\r\n    {\r\n      id: 1,\r\n      name: \"<PERSON><PERSON><PERSON><PERSON>\",\r\n      avatar:\r\n        \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\r\n    },\r\n    {\r\n      id: 2,\r\n      name: \"Meta AI\",\r\n      avatar:\r\n        \"https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face\",\r\n    },\r\n    {\r\n      id: 3,\r\n      name: \"<PERSON><PERSON><PERSON> b<PERSON>\",\r\n      avatar:\r\n        \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\r\n    },\r\n    {\r\n      id: 4,\r\n      name: \"<PERSON><PERSON> niệ<PERSON>\",\r\n      avatar:\r\n        \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\r\n    },\r\n    {\r\n      id: 5,\r\n      name: \"<PERSON><PERSON> lư<PERSON>\",\r\n      avatar:\r\n        \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\r\n    },\r\n    {\r\n      id: 6,\r\n      name: \"Nhóm\",\r\n      avatar:\r\n        \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"user-list\">\r\n      <div className=\"user-list__header\">\r\n        <h3 className=\"user-list__title\">Người liên hệ</h3>\r\n      </div>\r\n      <div className=\"user-list__content\">\r\n        {fakeUsers.map((user) => (\r\n          <UserItem key={user.id} user={user} />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserList;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,gBAAgB;AACvB,OAAOC,QAAQ,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,QAAQA,CAAA,EAAG;EAClB,MAAMC,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,MAAM,EACJ;EACJ,CAAC,EACD;IACEF,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,SAAS;IACfC,MAAM,EACJ;EACJ,CAAC,EACD;IACEF,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,QAAQ;IACdC,MAAM,EACJ;EACJ,CAAC,EACD;IACEF,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,SAAS;IACfC,MAAM,EACJ;EACJ,CAAC,EACD;IACEF,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,QAAQ;IACdC,MAAM,EACJ;EACJ,CAAC,EACD;IACEF,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,MAAM,EACJ;EACJ,CAAC,CACF;EAED,oBACEL,OAAA;IAAKM,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBP,OAAA;MAAKM,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCP,OAAA;QAAIM,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eACNX,OAAA;MAAKM,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChCL,SAAS,CAACU,GAAG,CAAEC,IAAI,iBAClBb,OAAA,CAACF,QAAQ;QAAee,IAAI,EAAEA;MAAK,GAApBA,IAAI,CAACV,EAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CACtC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACG,EAAA,GApDQb,QAAQ;AAsDjB,eAAeA,QAAQ;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}