{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\Login\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate, Link } from \"react-router-dom\";\nimport \"./Login.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login() {\n  _s();\n  const [email, setEmail] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const navigate = useNavigate();\n  const handleSubmit = e => {\n    e.preventDefault();\n    // Fake login logic\n    if (email && password) {\n      const fakeUser = {\n        id: 1,\n        firstname: \"<PERSON>\",\n        lastname: \"<PERSON><PERSON>\",\n        email: email,\n        avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n      };\n\n      // Store user in localStorage or context\n      localStorage.setItem(\"user\", JSON.stringify(fakeUser));\n\n      // Navigate to home\n      navigate(\"/\");\n      window.location.reload(); // Reload to update user state\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login__container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login__header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"login__title\",\n          children: \"\\u0110\\u0103ng nh\\u1EADp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login__subtitle\",\n          children: \"Ch\\xE0o m\\u1EEBng b\\u1EA1n quay tr\\u1EDF l\\u1EA1i!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"login__form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login__field\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            value: email,\n            onChange: e => setEmail(e.target.value),\n            placeholder: \"Email\",\n            className: \"login__input\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login__field\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            value: password,\n            onChange: e => setPassword(e.target.value),\n            placeholder: \"M\\u1EADt kh\\u1EA9u\",\n            className: \"login__input\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"login__submit\",\n          children: \"\\u0110\\u0103ng nh\\u1EADp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login__footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login__register-text\",\n          children: [\"Ch\\u01B0a c\\xF3 t\\xE0i kho\\u1EA3n?\", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"login__register-link\",\n            children: \"\\u0110\\u0103ng k\\xFD ngay\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"rIjyA2uJXTJXGlQGcq/HSho2Mo8=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Link", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "email", "setEmail", "password", "setPassword", "navigate", "handleSubmit", "e", "preventDefault", "fakeUser", "id", "firstname", "lastname", "avatar", "localStorage", "setItem", "JSON", "stringify", "window", "location", "reload", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "to", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/Login/Login.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { useN<PERSON><PERSON>, <PERSON> } from \"react-router-dom\";\r\nimport \"./Login.css\";\r\n\r\nfunction Login() {\r\n  const [email, setEmail] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const navigate = useNavigate();\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    // Fake login logic\r\n    if (email && password) {\r\n      const fakeUser = {\r\n        id: 1,\r\n        firstname: \"<PERSON>\",\r\n        lastname: \"<PERSON><PERSON>\",\r\n        email: email,\r\n        avatar:\r\n          \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\r\n      };\r\n\r\n      // Store user in localStorage or context\r\n      localStorage.setItem(\"user\", JSON.stringify(fakeUser));\r\n\r\n      // Navigate to home\r\n      navigate(\"/\");\r\n      window.location.reload(); // Reload to update user state\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"login\">\r\n      <div className=\"login__container\">\r\n        <div className=\"login__header\">\r\n          <h1 className=\"login__title\">Đăng nhập</h1>\r\n          <p className=\"login__subtitle\">Chào mừng bạn quay trở lại!</p>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"login__form\">\r\n          <div className=\"login__field\">\r\n            <input\r\n              type=\"email\"\r\n              value={email}\r\n              onChange={(e) => setEmail(e.target.value)}\r\n              placeholder=\"Email\"\r\n              className=\"login__input\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div className=\"login__field\">\r\n            <input\r\n              type=\"password\"\r\n              value={password}\r\n              onChange={(e) => setPassword(e.target.value)}\r\n              placeholder=\"Mật khẩu\"\r\n              className=\"login__input\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <button type=\"submit\" className=\"login__submit\">\r\n            Đăng nhập\r\n          </button>\r\n        </form>\r\n\r\n        <div className=\"login__footer\">\r\n          <p className=\"login__register-text\">\r\n            Chưa có tài khoản?\r\n            <Link to=\"/register\" className=\"login__register-link\">\r\n              Đăng ký ngay\r\n            </Link>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACA,IAAIP,KAAK,IAAIE,QAAQ,EAAE;MACrB,MAAMM,QAAQ,GAAG;QACfC,EAAE,EAAE,CAAC;QACLC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,KAAK;QACfX,KAAK,EAAEA,KAAK;QACZY,MAAM,EACJ;MACJ,CAAC;;MAED;MACAC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACR,QAAQ,CAAC,CAAC;;MAEtD;MACAJ,QAAQ,CAAC,GAAG,CAAC;MACba,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,oBACEtB,OAAA;IAAKuB,SAAS,EAAC,OAAO;IAAAC,QAAA,eACpBxB,OAAA;MAAKuB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BxB,OAAA;QAAKuB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxB,OAAA;UAAIuB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3C5B,OAAA;UAAGuB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAEN5B,OAAA;QAAM6B,QAAQ,EAAErB,YAAa;QAACe,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACnDxB,OAAA;UAAKuB,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BxB,OAAA;YACE8B,IAAI,EAAC,OAAO;YACZC,KAAK,EAAE5B,KAAM;YACb6B,QAAQ,EAAGvB,CAAC,IAAKL,QAAQ,CAACK,CAAC,CAACwB,MAAM,CAACF,KAAK,CAAE;YAC1CG,WAAW,EAAC,OAAO;YACnBX,SAAS,EAAC,cAAc;YACxBY,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5B,OAAA;UAAKuB,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BxB,OAAA;YACE8B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAE1B,QAAS;YAChB2B,QAAQ,EAAGvB,CAAC,IAAKH,WAAW,CAACG,CAAC,CAACwB,MAAM,CAACF,KAAK,CAAE;YAC7CG,WAAW,EAAC,oBAAU;YACtBX,SAAS,EAAC,cAAc;YACxBY,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5B,OAAA;UAAQ8B,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAEhD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP5B,OAAA;QAAKuB,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BxB,OAAA;UAAGuB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,oCAElC,eAAAxB,OAAA,CAACF,IAAI;YAACsC,EAAE,EAAC,WAAW;YAACb,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1B,EAAA,CA1EQD,KAAK;EAAA,QAGKJ,WAAW;AAAA;AAAAwC,EAAA,GAHrBpC,KAAK;AA4Ed,eAAeA,KAAK;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}