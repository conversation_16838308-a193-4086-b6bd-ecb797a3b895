{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Post\\\\PostCard\\\\PostCard.js\";\nimport React from \"react\";\nimport \"./PostCard.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PostCard({\n  post,\n  onOpenPostDetail\n}) {\n  const handleCommentClick = () => {\n    onOpenPostDetail(post);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"post-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"post-card__header\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: post.author.avatar,\n        alt: post.author.name,\n        className: \"post-card__author-avatar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"post-card__author-name\",\n        children: post.author.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"post-card__caption\",\n        children: post.caption\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"post-card__content\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: post.image,\n        alt: \"Post\",\n        className: \"post-card__image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"post-card__actions\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"post-card__comment-btn\",\n        onClick: handleCommentClick,\n        children: [\"\\uD83D\\uDCAC B\\xECnh lu\\u1EADn (\", post.comments.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n}\n_c = PostCard;\nexport default PostCard;\nvar _c;\n$RefreshReg$(_c, \"PostCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "PostCard", "post", "onOpenPostDetail", "handleCommentClick", "className", "children", "src", "author", "avatar", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "caption", "image", "onClick", "comments", "length", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Post/PostCard/PostCard.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./PostCard.css\";\r\n\r\nfunction PostCard({ post, onOpenPostDetail }) {\r\n  const handleCommentClick = () => {\r\n    onOpenPostDetail(post);\r\n  };\r\n\r\n  return (\r\n    <div className=\"post-card\">\r\n      <div className=\"post-card__header\">\r\n        <img\r\n          src={post.author.avatar}\r\n          alt={post.author.name}\r\n          className=\"post-card__author-avatar\"\r\n        />\r\n        <span className=\"post-card__author-name\">{post.author.name}</span>\r\n        <p className=\"post-card__caption\">{post.caption}</p>\r\n      </div>\r\n\r\n      <div className=\"post-card__content\">\r\n        <img src={post.image} alt=\"Post\" className=\"post-card__image\" />\r\n      </div>\r\n\r\n      <div className=\"post-card__actions\">\r\n        <button className=\"post-card__comment-btn\" onClick={handleCommentClick}>\r\n          💬 <PERSON><PERSON><PERSON> luận ({post.comments.length})\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default PostCard;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,SAASC,QAAQA,CAAC;EAAEC,IAAI;EAAEC;AAAiB,CAAC,EAAE;EAC5C,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BD,gBAAgB,CAACD,IAAI,CAAC;EACxB,CAAC;EAED,oBACEF,OAAA;IAAKK,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBN,OAAA;MAAKK,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCN,OAAA;QACEO,GAAG,EAAEL,IAAI,CAACM,MAAM,CAACC,MAAO;QACxBC,GAAG,EAAER,IAAI,CAACM,MAAM,CAACG,IAAK;QACtBN,SAAS,EAAC;MAA0B;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACFf,OAAA;QAAMK,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAEJ,IAAI,CAACM,MAAM,CAACG;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEf,OAAA;QAAGK,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAEJ,IAAI,CAACc;MAAO;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAENf,OAAA;MAAKK,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjCN,OAAA;QAAKO,GAAG,EAAEL,IAAI,CAACe,KAAM;QAACP,GAAG,EAAC,MAAM;QAACL,SAAS,EAAC;MAAkB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,eAENf,OAAA;MAAKK,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjCN,OAAA;QAAQK,SAAS,EAAC,wBAAwB;QAACa,OAAO,EAAEd,kBAAmB;QAAAE,QAAA,GAAC,kCACxD,EAACJ,IAAI,CAACiB,QAAQ,CAACC,MAAM,EAAC,GACtC;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACM,EAAA,GA5BQpB,QAAQ;AA8BjB,eAAeA,QAAQ;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}