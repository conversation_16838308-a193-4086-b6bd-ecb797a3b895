{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\About\\\\About.js\";\nimport React from \"react\";\nimport \"./About.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction About() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"about\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"about__container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"about__title\",\n        children: \"About Page\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"about__content\",\n        children: \"\\u0110\\xE2y l\\xE0 trang About. N\\u1ED9i dung s\\u1EBD \\u0111\\u01B0\\u1EE3c c\\u1EADp nh\\u1EADt sau.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "About", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/About/About.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./About.css\";\r\n\r\nfunction About() {\r\n  return (\r\n    <div className=\"about\">\r\n      <div className=\"about__container\">\r\n        <h1 className=\"about__title\">About Page</h1>\r\n        <p className=\"about__content\">\r\n          <PERSON><PERSON><PERSON> là trang About. Nội dung sẽ được cập nhật sau.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default About;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,SAASC,KAAKA,CAAA,EAAG;EACf,oBACED,OAAA;IAAKE,SAAS,EAAC,OAAO;IAAAC,QAAA,eACpBH,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BH,OAAA;QAAIE,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5CP,OAAA;QAAGE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAE9B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GAXQP,KAAK;AAad,eAAeA,KAAK;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}