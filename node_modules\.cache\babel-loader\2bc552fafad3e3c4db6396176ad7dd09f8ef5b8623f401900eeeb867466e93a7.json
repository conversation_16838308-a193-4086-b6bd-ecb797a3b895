{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Home.js\";\nimport React from \"react\";\nimport \"./styles.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Home() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Home View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home-content\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Lorem ipsum dolor sit amet, consectetur adip.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Home", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Home.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./styles.css\";\r\n\r\nfunction Home() {\r\n  return (\r\n    <div className=\"home-container\">\r\n      <div className=\"home-header\">\r\n        <h2>Home View</h2>\r\n      </div>\r\n      <div className=\"home-content\">\r\n        <p>Lorem ipsum dolor sit amet, consectetur adip.</p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,SAASC,IAAIA,CAAA,EAAG;EACd,oBACED,OAAA;IAAKE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BH,OAAA;MAAKE,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BH,OAAA;QAAAG,QAAA,EAAI;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BH,OAAA;QAAAG,QAAA,EAAG;MAA6C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GAXQP,IAAI;AAab,eAAeA,IAAI;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}