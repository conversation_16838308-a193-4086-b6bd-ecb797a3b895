{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Layout\\\\Sidebar\\\\Sidebar.js\";\nimport React from \"react\";\nimport \"./Sidebar.css\";\nimport UserList from \"./UserList\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Sidebar() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sidebar\",\n    children: /*#__PURE__*/_jsxDEV(UserList, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "UserList", "jsxDEV", "_jsxDEV", "Sidebar", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Layout/Sidebar/Sidebar.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./Sidebar.css\";\r\nimport UserList from \"./UserList\";\r\n\r\nfunction Sidebar() {\r\n  return (\r\n    <div className=\"sidebar\">\r\n      <UserList />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Sidebar;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,eAAe;AACtB,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,OAAOA,CAAA,EAAG;EACjB,oBACED,OAAA;IAAKE,SAAS,EAAC,SAAS;IAAAC,QAAA,eACtBH,OAAA,CAACF,QAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV;AAACC,EAAA,GANQP,OAAO;AAQhB,eAAeA,OAAO;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}