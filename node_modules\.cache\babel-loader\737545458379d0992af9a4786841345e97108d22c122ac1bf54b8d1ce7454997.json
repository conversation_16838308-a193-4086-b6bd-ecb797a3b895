{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Stats.js\";\nimport \"./styles.css\";\nimport React from \"react\";\nimport { Navigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Stats({\n  user\n}) {\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"stats-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Stats View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-content\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Lorem ipsum dolor sit amet, consectetur adip.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_c = Stats;\nexport default Stats;\nvar _c;\n$RefreshReg$(_c, \"Stats\");", "map": {"version": 3, "names": ["React", "Navigate", "jsxDEV", "_jsxDEV", "Stats", "user", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Stats.js"], "sourcesContent": ["import \"./styles.css\";\r\nimport React from \"react\";\r\nimport { Navigate } from \"react-router-dom\";\r\n\r\nfunction Stats({ user }) {\r\n  if (!user) {\r\n    return <Navigate to=\"/login\" replace />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"stats-container\">\r\n      <div className=\"stats-header\">\r\n        <h2>Stats View</h2>\r\n      </div>\r\n      <div className=\"stats-content\">\r\n        <p>Lorem ipsum dolor sit amet, consectetur adip.</p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Stats;\r\n"], "mappings": ";AAAA,OAAO,cAAc;AACrB,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,SAASC,KAAKA,CAAC;EAAEC;AAAK,CAAC,EAAE;EACvB,IAAI,CAACA,IAAI,EAAE;IACT,oBAAOF,OAAA,CAACF,QAAQ;MAACK,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EAEA,oBACER,OAAA;IAAKS,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BV,OAAA;MAAKS,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BV,OAAA;QAAAU,QAAA,EAAI;MAAU;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eACNR,OAAA;MAAKS,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BV,OAAA;QAAAU,QAAA,EAAG;MAA6C;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACG,EAAA,GAfQV,KAAK;AAiBd,eAAeA,KAAK;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}