{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\NotFound\\\\NoMatch.js\";\nimport React from \"react\";\nimport \"../../styles.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction NoMatch() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 20\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"404: Page Not Found\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Lorem ipsum dolor sit amet, consectetur adip.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = NoMatch;\nexport default NoMatch;\nvar _c;\n$RefreshReg$(_c, \"NoMatch\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "NoMatch", "style", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/NotFound/NoMatch.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"../../styles.css\";\r\nfunction NoMatch() {\r\n  return (\r\n    <div style={{ padding: 20 }}>\r\n      <h2>404: Page Not Found</h2>\r\n      <p>Lorem ipsum dolor sit amet, consectetur adip.</p>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default NoMatch;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC1B,SAASC,OAAOA,CAAA,EAAG;EACjB,oBACED,OAAA;IAAKE,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAE;IAAAC,QAAA,gBAC1BJ,OAAA;MAAAI,QAAA,EAAI;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC5BR,OAAA;MAAAI,QAAA,EAAG;IAA6C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjD,CAAC;AAEV;AAACC,EAAA,GAPQR,OAAO;AAShB,eAAeA,OAAO;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}