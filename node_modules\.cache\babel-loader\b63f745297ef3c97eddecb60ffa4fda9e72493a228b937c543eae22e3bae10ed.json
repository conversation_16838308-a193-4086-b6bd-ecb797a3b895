{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./styles.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login({\n  onLogin\n}) {\n  _s();\n  const [creds, setCreds] = useState({});\n  const [error, setError] = useState(\"\");\n  const navigate = useNavigate();\n  const handleLogin = async () => {\n    try {\n      const response = await fetch(\"http://localhost:8080/api/login\", {\n        method: \"post\",\n        headers: {\n          Accept: \"application /json\",\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(creds)\n      });\n      if (response.ok) {\n        onLogin && onLogin({\n          username: creds.username\n        });\n        navigate(\"/stats\");\n      } else setError(\"Invalid username or password!\");\n    } catch (error) {\n      console.error(\"Login error:\", error);\n      setError(\"Login failed!\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: [\" \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"Username:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"text\",\n      onChange: e => setCreds({\n        ...creds,\n        username: e.target.value\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"Password:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"password\",\n      onChange: e => setCreds({\n        ...creds,\n        password: e.target.value\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleLogin,\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"UF1l+TOgkBTLM8Y5HRo+r6/8rac=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "onLogin", "_s", "creds", "setCreds", "error", "setError", "navigate", "handleLogin", "response", "fetch", "method", "headers", "Accept", "body", "JSON", "stringify", "ok", "username", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "e", "target", "value", "password", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Login.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport \"./styles.css\";\r\n\r\nfunction Login({ onLogin }) {\r\n  const [creds, setCreds] = useState({});\r\n  const [error, setError] = useState(\"\");\r\n  const navigate = useNavigate();\r\n  const handleLogin = async () => {\r\n    try {\r\n      const response = await fetch(\"http://localhost:8080/api/login\", {\r\n        method: \"post\",\r\n        headers: {\r\n          Accept: \"application /json\",\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(creds),\r\n      });\r\n      if (response.ok) {\r\n        onLogin && onLogin({ username: creds.username });\r\n        navigate(\"/stats\");\r\n      } else setError(\"Invalid username or password!\");\r\n    } catch (error) {\r\n      console.error(\"Login error:\", error);\r\n      setError(\"Login failed!\");\r\n    }\r\n  };\r\n  return (\r\n    <div className=\"login-container\">\r\n      {\" \"}\r\n      <br />\r\n      <span>Username:</span>\r\n      <br />\r\n      <input\r\n        type=\"text\"\r\n        onChange={(e) => setCreds({ ...creds, username: e.target.value })}\r\n      />\r\n      <br />\r\n      <span>Password:</span>\r\n      <br />\r\n      <input\r\n        type=\"password\"\r\n        onChange={(e) => setCreds({ ...creds, password: e.target.value })}\r\n      />\r\n      <br />\r\n      <br />\r\n      <button onClick={handleLogin}>Login</button>\r\n      <p>{error}</p>\r\n    </div>\r\n  );\r\n}\r\nexport default Login;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,SAASC,KAAKA,CAAC;EAAEC;AAAQ,CAAC,EAAE;EAAAC,EAAA;EAC1B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtC,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,iCAAiC,EAAE;QAC9DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACPC,MAAM,EAAE,mBAAmB;UAC3B,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACb,KAAK;MAC5B,CAAC,CAAC;MACF,IAAIM,QAAQ,CAACQ,EAAE,EAAE;QACfhB,OAAO,IAAIA,OAAO,CAAC;UAAEiB,QAAQ,EAAEf,KAAK,CAACe;QAAS,CAAC,CAAC;QAChDX,QAAQ,CAAC,QAAQ,CAAC;MACpB,CAAC,MAAMD,QAAQ,CAAC,+BAA+B,CAAC;IAClD,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CAAC,eAAe,CAAC;IAC3B;EACF,CAAC;EACD,oBACEP,OAAA;IAAKqB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,GAC7B,GAAG,eACJtB,OAAA;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACN1B,OAAA;MAAAsB,QAAA,EAAM;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACtB1B,OAAA;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACN1B,OAAA;MACE2B,IAAI,EAAC,MAAM;MACXC,QAAQ,EAAGC,CAAC,IAAKxB,QAAQ,CAAC;QAAE,GAAGD,KAAK;QAAEe,QAAQ,EAAEU,CAAC,CAACC,MAAM,CAACC;MAAM,CAAC;IAAE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,eACF1B,OAAA;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACN1B,OAAA;MAAAsB,QAAA,EAAM;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACtB1B,OAAA;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACN1B,OAAA;MACE2B,IAAI,EAAC,UAAU;MACfC,QAAQ,EAAGC,CAAC,IAAKxB,QAAQ,CAAC;QAAE,GAAGD,KAAK;QAAE4B,QAAQ,EAAEH,CAAC,CAACC,MAAM,CAACC;MAAM,CAAC;IAAE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,eACF1B,OAAA;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACN1B,OAAA;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACN1B,OAAA;MAAQiC,OAAO,EAAExB,WAAY;MAAAa,QAAA,EAAC;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAC5C1B,OAAA;MAAAsB,QAAA,EAAIhB;IAAK;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACX,CAAC;AAEV;AAACvB,EAAA,CA9CQF,KAAK;EAAA,QAGKH,WAAW;AAAA;AAAAoC,EAAA,GAHrBjC,KAAK;AA+Cd,eAAeA,KAAK;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}