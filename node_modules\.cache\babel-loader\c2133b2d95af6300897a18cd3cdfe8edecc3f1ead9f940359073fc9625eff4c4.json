{"ast": null, "code": "import React from 'react';\nvar isCheckBoxInput = element => element.type === 'checkbox';\nvar isDateObject = value => value instanceof Date;\nvar isNullOrUndefined = value => value == null;\nconst isObjectType = value => typeof value === 'object';\nvar isObject = value => !isNullOrUndefined(value) && !Array.isArray(value) && isObjectType(value) && !isDateObject(value);\nvar getEventValue = event => isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = name => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\nvar isPlainObject = tempObject => {\n  const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n  return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf');\n};\nvar isWeb = typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined' && typeof document !== 'undefined';\nfunction cloneObject(data) {\n  let copy;\n  const isArray = Array.isArray(data);\n  const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n    copy = isArray ? [] : {};\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n  return copy;\n}\nvar compact = value => Array.isArray(value) ? value.filter(Boolean) : [];\nvar isUndefined = val => val === undefined;\nvar get = (object, path, defaultValue) => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n  const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n  return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = value => typeof value === 'boolean';\nvar isKey = value => /^\\w*$/.test(value);\nvar stringToPath = input => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\nvar set = (object, path, value) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n    }\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n    object[key] = newValue;\n    object = object[key];\n  }\n};\nconst EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change'\n};\nconst VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all'\n};\nconst INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate'\n};\nconst HookFormContext = React.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => React.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = props => {\n  const {\n    children,\n    ...data\n  } = props;\n  return React.createElement(HookFormContext.Provider, {\n    value: data\n  }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n  const result = {\n    defaultValues: control._defaultValues\n  };\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key;\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      }\n    });\n  }\n  return result;\n};\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    disabled,\n    name,\n    exact\n  } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  });\n  const _name = React.useRef(name);\n  _name.current = name;\n  React.useEffect(() => control._subscribe({\n    name: _name.current,\n    formState: _localProxyFormState.current,\n    exact,\n    callback: formState => {\n      !disabled && updateFormState({\n        ...control._formState,\n        ...formState\n      });\n    }\n  }), [control, disabled, exact]);\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n  return React.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\nvar isString = value => typeof value === 'string';\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n  if (Array.isArray(names)) {\n    return names.map(fieldName => (isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n  }\n  isGlobal && (_names.watchAll = true);\n  return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact\n  } = props || {};\n  const _name = React.useRef(name);\n  const _defaultValue = React.useRef(defaultValue);\n  _name.current = name;\n  React.useEffect(() => control._subscribe({\n    name: _name.current,\n    formState: {\n      values: true\n    },\n    exact,\n    callback: formState => !disabled && updateValue(generateWatchOutput(_name.current, control._names, formState.values || control._formValues, false, _defaultValue.current))\n  }), [control, disabled, exact]);\n  const [value, updateValue] = React.useState(control._getWatch(name, defaultValue));\n  React.useEffect(() => control._removeUnmounted());\n  return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n  const methods = useFormContext();\n  const {\n    name,\n    disabled,\n    control = methods.control,\n    shouldUnregister\n  } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n    exact: true\n  });\n  const formState = useFormState({\n    control,\n    name,\n    exact: true\n  });\n  const _props = React.useRef(props);\n  const _registerProps = React.useRef(control.register(name, {\n    ...props.rules,\n    value,\n    ...(isBoolean(props.disabled) ? {\n      disabled: props.disabled\n    } : {})\n  }));\n  const fieldState = React.useMemo(() => Object.defineProperties({}, {\n    invalid: {\n      enumerable: true,\n      get: () => !!get(formState.errors, name)\n    },\n    isDirty: {\n      enumerable: true,\n      get: () => !!get(formState.dirtyFields, name)\n    },\n    isTouched: {\n      enumerable: true,\n      get: () => !!get(formState.touchedFields, name)\n    },\n    isValidating: {\n      enumerable: true,\n      get: () => !!get(formState.validatingFields, name)\n    },\n    error: {\n      enumerable: true,\n      get: () => get(formState.errors, name)\n    }\n  }), [formState, name]);\n  const onChange = React.useCallback(event => _registerProps.current.onChange({\n    target: {\n      value: getEventValue(event),\n      name: name\n    },\n    type: EVENTS.CHANGE\n  }), [name]);\n  const onBlur = React.useCallback(() => _registerProps.current.onBlur({\n    target: {\n      value: get(control._formValues, name),\n      name: name\n    },\n    type: EVENTS.BLUR\n  }), [name, control._formValues]);\n  const ref = React.useCallback(elm => {\n    const field = get(control._fields, name);\n    if (field && elm) {\n      field._f.ref = {\n        focus: () => elm.focus(),\n        select: () => elm.select(),\n        setCustomValidity: message => elm.setCustomValidity(message),\n        reportValidity: () => elm.reportValidity()\n      };\n    }\n  }, [control._fields, name]);\n  const field = React.useMemo(() => ({\n    name,\n    value,\n    ...(isBoolean(disabled) || formState.disabled ? {\n      disabled: formState.disabled || disabled\n    } : {}),\n    onChange,\n    onBlur,\n    ref\n  }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n  React.useEffect(() => {\n    const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled) ? {\n        disabled: _props.current.disabled\n      } : {})\n    });\n    const updateMounted = (name, value) => {\n      const field = get(control._fields, name);\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n    updateMounted(name, true);\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n    !isArrayField && control.register(name);\n    return () => {\n      (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name\n    });\n  }, [disabled, name, control]);\n  return React.useMemo(() => ({\n    field,\n    formState,\n    fieldState\n  }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = props => props.render(useController(props));\nconst flatten = obj => {\n  const output = {};\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n  return output;\n};\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n  const methods = useFormContext();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n  const submit = async event => {\n    let hasError = false;\n    let type = '';\n    await control.handleSubmit(async data => {\n      const formData = new FormData();\n      let formDataJson = '';\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch (_a) {}\n      const flattenFormValues = flatten(control._formValues);\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson\n        });\n      }\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [headers && headers['Content-Type'], encType].some(value => value && value.includes('json'));\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? {\n                'Content-Type': encType\n              } : {})\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData\n          });\n          if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n            hasError = true;\n            onError && onError({\n              response\n            });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({\n              response\n            });\n          }\n        } catch (error) {\n          hasError = true;\n          onError && onError({\n            error\n          });\n        }\n      }\n    })(event);\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false\n      });\n      props.control.setError('root.server', {\n        type\n      });\n    }\n  };\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  return render ? React.createElement(React.Fragment, null, render({\n    submit\n  })) : React.createElement(\"form\", {\n    noValidate: mounted,\n    action: action,\n    method: method,\n    encType: encType,\n    onSubmit: submit,\n    ...rest\n  }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria ? {\n  ...errors[name],\n  types: {\n    ...(errors[name] && errors[name].types ? errors[name].types : {}),\n    [type]: message || true\n  }\n} : {};\nvar convertToArrayPayload = value => Array.isArray(value) ? value : [value];\nvar createSubject = () => {\n  let _observers = [];\n  const next = value => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n  const subscribe = observer => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter(o => o !== observer);\n      }\n    };\n  };\n  const unsubscribe = () => {\n    _observers = [];\n  };\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe\n  };\n};\nvar isPrimitive = value => isNullOrUndefined(value) || !isObjectType(value);\nfunction deepEqual(object1, object2) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n  for (const key of keys1) {\n    const val1 = object1[key];\n    if (!keys2.includes(key)) {\n      return false;\n    }\n    if (key !== 'ref') {\n      const val2 = object2[key];\n      if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\nvar isEmptyObject = value => isObject(value) && !Object.keys(value).length;\nvar isFileInput = element => element.type === 'file';\nvar isFunction = value => typeof value === 'function';\nvar isHTMLElement = value => {\n  if (!isWeb) {\n    return false;\n  }\n  const owner = value ? value.ownerDocument : 0;\n  return value instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = element => element.type === `select-multiple`;\nvar isRadioInput = element => element.type === 'radio';\nvar isRadioOrCheckbox = ref => isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = ref => isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n  return object;\n}\nfunction isEmptyArray(obj) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction unset(object, path) {\n  const paths = Array.isArray(path) ? path : isKey(path) ? [path] : stringToPath(path);\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n  const index = paths.length - 1;\n  const key = paths[index];\n  if (childObject) {\n    delete childObject[key];\n  }\n  if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n    unset(object, paths.slice(0, -1));\n  }\n  return object;\n}\nvar objectHasFunction = data => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n  return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n            ...markFieldsDirty(data[key])\n          };\n        } else {\n          getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n  return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n  value: false,\n  isValid: false\n};\nconst validResult = {\n  value: true,\n  isValid: true\n};\nvar getCheckboxValue = options => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options.filter(option => option && option.checked && !option.disabled).map(option => option.value);\n      return {\n        value: values,\n        isValid: !!values.length\n      };\n    }\n    return options[0].checked && !options[0].disabled ?\n    // @ts-expect-error expected to work in the browser\n    options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === '' ? validResult : {\n      value: options[0].value,\n      isValid: true\n    } : validResult : defaultResult;\n  }\n  return defaultResult;\n};\nvar getFieldValueAs = (value, {\n  valueAsNumber,\n  valueAsDate,\n  setValueAs\n}) => isUndefined(value) ? value : valueAsNumber ? value === '' ? NaN : value ? +value : value : valueAsDate && isString(value) ? new Date(value) : setValueAs ? setValueAs(value) : value;\nconst defaultReturn = {\n  isValid: false,\n  value: null\n};\nvar getRadioValue = options => Array.isArray(options) ? options.reduce((previous, option) => option && option.checked && !option.disabled ? {\n  isValid: true,\n  value: option.value\n} : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n  const ref = _f.ref;\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({\n      value\n    }) => value);\n  }\n  if (isCheckBoxInput(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n  const fields = {};\n  for (const name of fieldsNames) {\n    const field = get(_fields, name);\n    field && set(fields, name, field._f);\n  }\n  return {\n    criteriaMode,\n    names: [...fieldsNames],\n    fields,\n    shouldUseNativeValidation\n  };\n};\nvar isRegex = value => value instanceof RegExp;\nvar getRuleValue = rule => isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = mode => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched\n});\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = fieldReference => !!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find(validateFunction => validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = options => options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent && (_names.watchAll || _names.watch.has(name) || [..._names.watch].some(watchName => name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n    if (field) {\n      const {\n        _f,\n        ...currentField\n      } = field;\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n  const error = get(errors, name);\n  if (error || isKey(name)) {\n    return {\n      error,\n      name\n    };\n  }\n  const names = name.split('.');\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return {\n        name\n      };\n    }\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError\n      };\n    }\n    names.pop();\n  }\n  return {\n    name\n  };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n  updateFormState(formStateData);\n  const {\n    name,\n    ...formState\n  } = formStateData;\n  return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find(key => _proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact) => !name || !signalName || name === signalName || convertToArrayPayload(name).some(currentName => currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name) => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\nvar isMessage = value => isString(value);\nfunction getValidateError(result, ref, type = 'validate') {\n  if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref\n    };\n  }\n}\nvar getValueAndMessage = validationData => isObject(validationData) && !isRegex(validationData) ? validationData : {\n  value: validationData,\n  message: ''\n};\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount\n  } = field._f;\n  const inputValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef = refs ? refs[0] : ref;\n  const setCustomValidity = message => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === '' || inputValue === '' || Array.isArray(inputValue) && !inputValue.length;\n  const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n  const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n    };\n  };\n  if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n    const {\n      value,\n      message\n    } = isMessage(required) ? {\n      value: !!required,\n      message: required\n    } : getValueAndMessage(required);\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n      const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate = ref.valueAsDate || new Date(inputValue);\n      const convertTimeToDate = time => new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n      }\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n      }\n    }\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n    const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const {\n      value: patternValue,\n      message\n    } = getValueAndMessage(pattern);\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {};\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n        const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message)\n          };\n          setCustomValidity(validateError.message);\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n  setCustomValidity(true);\n  return error;\n};\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n  let _options = {\n    ...defaultOptions,\n    ...props\n  };\n  let _formState = {\n    submitCount: 0,\n    isDirty: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false\n  };\n  const _fields = {};\n  let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.values || _options.defaultValues) || {} : {};\n  let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false\n  };\n  let _names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set()\n  };\n  let delayErrorCallback;\n  let timer = 0;\n  const _proxyFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState\n  };\n  const _subjects = {\n    array: createSubject(),\n    state: createSubject()\n  };\n  const validationModeBeforeSubmit = getValidationModes(_options.mode);\n  const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n  const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n  const debounce = callback => wait => {\n    clearTimeout(timer);\n    timer = setTimeout(callback, wait);\n  };\n  const _setValid = async shouldUpdateValid => {\n    if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n      const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid\n        });\n      }\n    }\n  };\n  const _updateIsValidating = (names, isValidating) => {\n    if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n      (names || Array.from(_names.mount)).forEach(name => {\n        if (name) {\n          isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n        }\n      });\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields)\n      });\n    }\n  };\n  const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n        const errors = method(get(_formState.errors, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n      if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n        const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n  const updateErrors = (name, error) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors\n    });\n  };\n  const _setErrors = errors => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false\n    });\n  };\n  const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n    const field = get(_fields, name);\n    if (field) {\n      const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n      isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n      _state.mount && _setValid();\n    }\n  };\n  const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output = {\n      name\n    };\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n        const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n      }\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n        }\n      }\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n    return shouldUpdateField ? output : {};\n  };\n  const shouldRenderByError = (name, isValid, error, fieldState) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n    }\n    if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? {\n          isValid\n        } : {}),\n        errors: _formState.errors,\n        name\n      };\n      _formState = {\n        ..._formState,\n        ...updatedFormState\n      };\n      _subjects.state.next(updatedFormState);\n    }\n  };\n  const _runSchema = async name => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n    _updateIsValidating(name);\n    return result;\n  };\n  const executeSchemaAndUpdateState = async names => {\n    const {\n      errors\n    } = await _runSchema(names);\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n    return errors;\n  };\n  const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n    valid: true\n  }) => {\n    for (const name in fields) {\n      const field = fields[name];\n      if (field) {\n        const {\n          _f,\n          ...fieldValue\n        } = field;\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n          const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n          !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n        }\n        !isEmptyObject(fieldValue) && (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n      }\n    }\n    return context.valid;\n  };\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field = get(_fields, name);\n      field && (field._f.refs ? field._f.refs.every(ref => !live(ref)) : !live(field._f.ref)) && unregister(name);\n    }\n    _names.unMount = new Set();\n  };\n  const _getDirty = (name, data) => !_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n  const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n    ...(_state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n      [names]: defaultValue\n    } : defaultValue)\n  }, isGlobal, defaultValue);\n  const _getFieldArray = name => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n  const setFieldValue = (name, value, options = {}) => {\n    const field = get(_fields, name);\n    let fieldValue = value;\n    if (field) {\n      const fieldReference = field._f;\n      if (fieldReference) {\n        !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value, fieldReference));\n        fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value) ? '' : value;\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(optionRef => optionRef.selected = fieldValue.includes(optionRef.value));\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.length > 1 ? fieldReference.refs.forEach(checkboxRef => (!checkboxRef.defaultChecked || !checkboxRef.disabled) && (checkboxRef.checked = Array.isArray(fieldValue) ? !!fieldValue.find(data => data === checkboxRef.value) : fieldValue === checkboxRef.value)) : fieldReference.refs[0] && (fieldReference.refs[0].checked = !!fieldValue);\n          } else {\n            fieldReference.refs.forEach(radioRef => radioRef.checked = radioRef.value === fieldValue);\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues)\n            });\n          }\n        }\n      }\n    }\n    (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n    options.shouldValidate && trigger(name);\n  };\n  const setValues = (name, value, options) => {\n    for (const fieldKey in value) {\n      const fieldValue = value[fieldKey];\n      const fieldName = `${name}.${fieldKey}`;\n      const field = get(_fields, fieldName);\n      (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n  const setValue = (name, value, options = {}) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n    set(_formValues, name, cloneValue);\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues)\n      });\n      if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue)\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n    }\n    isWatched(name, _names) && _subjects.state.next({\n      ..._formState\n    });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues)\n    });\n  };\n  const onChange = async event => {\n    _state.mount = true;\n    const target = event.target;\n    let name = target.name;\n    let isFieldValueUpdated = true;\n    const field = get(_fields, name);\n    const _updateIsFieldValueUpdated = fieldValue => {\n      isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n      const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n      const watched = isWatched(name, _names, isBlurEvent);\n      set(_formValues, name, fieldValue);\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n      !isBlurEvent && _subjects.state.next({\n        name,\n        type: event.type,\n        values: cloneObject(_formValues)\n      });\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n        return shouldRender && _subjects.state.next({\n          name,\n          ...(watched ? {} : fieldState)\n        });\n      }\n      !isBlurEvent && watched && _subjects.state.next({\n        ..._formState\n      });\n      if (_options.resolver) {\n        const {\n          errors\n        } = await _runSchema([name]);\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n          const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n        _updateIsValidating([name]);\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n      if (isFieldValueUpdated) {\n        field._f.deps && trigger(field._f.deps);\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n  const _focusInput = (ref, key) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n  const trigger = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name);\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n      isValid = isEmptyObject(errors);\n      validationResult = name ? !fieldNames.some(name => get(errors, name)) : isValid;\n    } else if (name) {\n      validationResult = (await Promise.all(fieldNames.map(async fieldName => {\n        const field = get(_fields, fieldName);\n        return await executeBuiltInValidation(field && field._f ? {\n          [fieldName]: field\n        } : field);\n      }))).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n    _subjects.state.next({\n      ...(!isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n        name\n      }),\n      ...(_options.resolver || !name ? {\n        isValid\n      } : {}),\n      errors: _formState.errors\n    });\n    options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n    return validationResult;\n  };\n  const getValues = fieldNames => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues)\n    };\n    return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map(name => get(values, name));\n  };\n  const getFieldState = (name, formState) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name)\n  });\n  const clearErrors = name => {\n    name && convertToArrayPayload(name).forEach(inputName => unset(_formState.errors, inputName));\n    _subjects.state.next({\n      errors: name ? _formState.errors : {}\n    });\n  };\n  const setError = (name, error, options) => {\n    const ref = (get(_fields, name, {\n      _f: {}\n    })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n    // Don't override existing error messages elsewhere in the object tree.\n    const {\n      ref: currentRef,\n      message,\n      type,\n      ...restOfErrorTree\n    } = currentError;\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref\n    });\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false\n    });\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n  const watch = (name, defaultValue) => isFunction(name) ? _subjects.state.subscribe({\n    next: payload => name(_getWatch(undefined, defaultValue), payload)\n  }) : _getWatch(name, defaultValue, true);\n  const _subscribe = props => _subjects.state.subscribe({\n    next: formState => {\n      if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n        props.callback({\n          values: {\n            ..._formValues\n          },\n          ..._formState,\n          ...formState\n        });\n      }\n    }\n  }).unsubscribe;\n  const subscribe = props => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState\n    });\n  };\n  const unregister = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n    }\n    _subjects.state.next({\n      values: cloneObject(_formValues)\n    });\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : {\n        isDirty: _getDirty()\n      })\n    });\n    !options.keepIsValid && _setValid();\n  };\n  const _setDisabledField = ({\n    disabled,\n    name\n  }) => {\n    if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n  const register = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : {\n          ref: {\n            name\n          }\n        }),\n        name,\n        mount: true,\n        ...options\n      }\n    });\n    _names.mount.add(name);\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n        name\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n    return {\n      ...(disabledIsDefined ? {\n        disabled: options.disabled || _options.disabled\n      } : {}),\n      ...(_options.progressive ? {\n        required: !!options.required,\n        min: getRuleValue(options.min),\n        max: getRuleValue(options.max),\n        minLength: getRuleValue(options.minLength),\n        maxLength: getRuleValue(options.maxLength),\n        pattern: getRuleValue(options.pattern)\n      } : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: ref => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n          const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll('input,select,textarea')[0] || ref : ref : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n          if (radioOrCheckbox ? refs.find(option => option === fieldRef) : fieldRef === field._f.ref) {\n            return;\n          }\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox ? {\n                refs: [...refs.filter(live), fieldRef, ...(Array.isArray(get(_defaultValues, name)) ? [{}] : [])],\n                ref: {\n                  type: fieldRef.type,\n                  name\n                }\n              } : {\n                ref: fieldRef\n              })\n            }\n          });\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n          if (field._f) {\n            field._f.mount = false;\n          }\n          (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n        }\n      }\n    };\n  };\n  const _focusError = () => _options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n  const _disableForm = disabled => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({\n        disabled\n      });\n      iterateFieldsByAction(_fields, (ref, name) => {\n        const currentField = get(_fields, name);\n        if (currentField) {\n          ref.disabled = currentField._f.disabled || disabled;\n          if (Array.isArray(currentField._f.refs)) {\n            currentField._f.refs.forEach(inputRef => {\n              inputRef.disabled = currentField._f.disabled || disabled;\n            });\n          }\n        }\n      }, 0, false);\n    }\n  };\n  const handleSubmit = (onValid, onInvalid) => async e => {\n    let onValidError = undefined;\n    if (e) {\n      e.preventDefault && e.preventDefault();\n      e.persist && e.persist();\n    }\n    let fieldValues = cloneObject(_formValues);\n    _subjects.state.next({\n      isSubmitting: true\n    });\n    if (_options.resolver) {\n      const {\n        errors,\n        values\n      } = await _runSchema();\n      _formState.errors = errors;\n      fieldValues = values;\n    } else {\n      await executeBuiltInValidation(_fields);\n    }\n    if (_names.disabled.size) {\n      for (const name of _names.disabled) {\n        set(fieldValues, name, undefined);\n      }\n    }\n    unset(_formState.errors, 'root');\n    if (isEmptyObject(_formState.errors)) {\n      _subjects.state.next({\n        errors: {}\n      });\n      try {\n        await onValid(fieldValues, e);\n      } catch (error) {\n        onValidError = error;\n      }\n    } else {\n      if (onInvalid) {\n        await onInvalid({\n          ..._formState.errors\n        }, e);\n      }\n      _focusError();\n      setTimeout(_focusError);\n    }\n    _subjects.state.next({\n      isSubmitted: true,\n      isSubmitting: false,\n      isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n      submitCount: _formState.submitCount + 1,\n      errors: _formState.errors\n    });\n    if (onValidError) {\n      throw onValidError;\n    }\n  };\n  const resetField = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(name, options.defaultValue);\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n      }\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n      _subjects.state.next({\n        ..._formState\n      });\n    }\n  };\n  const _reset = (formValues, keepStateOptions = {}) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([..._names.mount, ...Object.keys(getDirtyFields(_defaultValues, _formValues))]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n        for (const fieldName of _names.mount) {\n          setValue(fieldName, get(values, fieldName));\n        }\n      }\n      _formValues = cloneObject(values);\n      _subjects.array.next({\n        values: {\n          ...values\n        }\n      });\n      _subjects.state.next({\n        values: {\n          ...values\n        }\n      });\n    }\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: ''\n    };\n    _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n    _state.watch = !!_options.shouldUnregister;\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n      isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n      isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n      dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n      touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n      isSubmitting: false\n    });\n  };\n  const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n  const setFocus = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n      }\n    }\n  };\n  const _setFormState = updatedFormState => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState\n    };\n  };\n  const _resetDefaultValues = () => isFunction(_options.defaultValues) && _options.defaultValues().then(values => {\n    reset(values, _options.resetOptions);\n    _subjects.state.next({\n      isLoading: false\n    });\n  });\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value\n        };\n      }\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState\n  };\n  return {\n    ...methods,\n    formControl: methods\n  };\n}\nvar generateId = () => {\n  const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n    return (c == 'x' ? r : r & 0x3 | 0x8).toString(16);\n  });\n};\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : '';\nvar appendAt = (data, value) => [...data, ...convertToArrayPayload(value)];\nvar fillEmptyArray = value => Array.isArray(value) ? value.map(() => undefined) : undefined;\nfunction insert(data, index, value) {\n  return [...data.slice(0, index), ...convertToArrayPayload(value), ...data.slice(index)];\n}\nvar moveArrayAt = (data, from, to) => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n  return data;\n};\nvar prependAt = (data, value) => [...convertToArrayPayload(value), ...convertToArrayPayload(data)];\nfunction removeAtIndexes(data, indexes) {\n  let i = 0;\n  const temp = [...data];\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n  return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\nvar swapArrayAt = (data, indexA, indexB) => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\nvar updateAt = (fieldValues, index, value) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef(control._getFieldArray(name).map(generateId));\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n  rules && control.register(name, rules);\n  React.useEffect(() => control._subjects.array.subscribe({\n    next: ({\n      values,\n      name: fieldArrayName\n    }) => {\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    }\n  }).unsubscribe, [control]);\n  const updateValues = React.useCallback(updatedFieldArrayValues => {\n    _actioned.current = true;\n    control._setFieldArray(name, updatedFieldArrayValues);\n  }, [control, name]);\n  const append = (value, options) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n    control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const prepend = (value, options) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const remove = index => {\n    const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index\n    });\n  };\n  const insert$1 = (index, value, options) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insert(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insert, {\n      argA: index,\n      argB: fillEmptyArray(value)\n    });\n  };\n  const swap = (indexA, indexB) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n      argA: indexA,\n      argB: indexB\n    }, false);\n  };\n  const move = (from, to) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n      argA: from,\n      argB: to\n    }, false);\n  };\n  const update = (index, value) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n    ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n      argA: index,\n      argB: updateValue\n    }, true, false);\n  };\n  const replace = value => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(name, [...updatedFieldArrayValues], data => data, {}, true, false);\n  };\n  React.useEffect(() => {\n    control._state.action = false;\n    isWatched(name, control._names) && control._subjects.state.next({\n      ...control._formState\n    });\n    if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then(result => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n          if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n            error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors\n            });\n          }\n        });\n      } else {\n        const field = get(control._fields, name);\n        if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n          validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then(error => !isEmptyObject(error) && control._subjects.state.next({\n            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n          }));\n        }\n      }\n    }\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues)\n    });\n    control._names.focus && iterateFieldsByAction(control._fields, (ref, key) => {\n      if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n        ref.focus();\n        return 1;\n      }\n      return;\n    });\n    control._names.focus = '';\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n    return () => {\n      const updateMounted = (name, value) => {\n        const field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n      control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert$1, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(() => fields.map((field, index) => ({\n      ...field,\n      [keyName]: ids.current[index] || generateId()\n    })), [fields, keyName])\n  };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n  const _formControl = React.useRef(undefined);\n  const _values = React.useRef(undefined);\n  const [formState, updateFormState] = React.useState({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n  });\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...(props.formControl ? props.formControl : createFormControl(props)),\n      formState\n    };\n    if (props.formControl && props.defaultValues && !isFunction(props.defaultValues)) {\n      props.formControl.reset(props.defaultValues, props.resetOptions);\n    }\n  }\n  const control = _formControl.current.control;\n  control._options = props;\n  React.useLayoutEffect(() => control._subscribe({\n    formState: control._proxyFormState,\n    callback: () => updateFormState({\n      ...control._formState\n    }),\n    reRenderRoot: true\n  }), [control]);\n  React.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState(state => ({\n        ...state\n      }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [props.values, control]);\n  React.useEffect(() => {\n    if (props.errors && !isEmptyObject(props.errors)) {\n      control._setErrors(props.errors);\n    }\n  }, [props.errors, control]);\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({\n        ...control._formState\n      });\n    }\n    control._removeUnmounted();\n  });\n  React.useEffect(() => {\n    props.shouldUnregister && control._subjects.state.next({\n      values: control._getWatch()\n    });\n  }, [props.shouldUnregister, control]);\n  _formControl.current.formState = getProxyFormState(formState, control);\n  return _formControl.current;\n}\nexport { Controller, Form, FormProvider, appendErrors, createFormControl, get, set, useController, useFieldArray, useForm, useFormContext, useFormState, useWatch };", "map": {"version": 3, "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "getNodeParentName", "name", "substring", "search", "isNameInFieldArray", "names", "has", "isPlainObject", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "isFileListInstance", "FileList", "Set", "Blob", "key", "compact", "filter", "Boolean", "isUndefined", "val", "undefined", "get", "object", "path", "defaultValue", "result", "split", "reduce", "isBoolean", "is<PERSON>ey", "test", "stringToPath", "input", "replace", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "BLUR", "FOCUS_OUT", "CHANGE", "VALIDATION_MODE", "onBlur", "onChange", "onSubmit", "onTouched", "all", "INPUT_VALIDATION_RULES", "max", "min", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "required", "validate", "HookFormContext", "React", "createContext", "useFormContext", "useContext", "FormProvider", "props", "children", "createElement", "Provider", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "useFormState", "methods", "disabled", "exact", "updateFormState", "useState", "_formState", "_localProxyFormState", "useRef", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_name", "current", "useEffect", "_subscribe", "callback", "_setValid", "useMemo", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "useWatch", "_defaultValue", "values", "updateValue", "_formValues", "_getWatch", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_props", "_registerProps", "register", "rules", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "useCallback", "ref", "elm", "field", "_fields", "_f", "focus", "select", "setCustomValidity", "message", "reportValidity", "_shouldUnregisterField", "_options", "updateMounted", "mount", "_state", "action", "unregister", "_setDisabledField", "Controller", "render", "flatten", "obj", "output", "keys", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "Form", "mounted", "setMounted", "method", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "handleSubmit", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "some", "includes", "response", "fetch", "String", "body", "status", "_subjects", "state", "next", "isSubmitSuccessful", "setError", "Fragment", "noValidate", "appendErrors", "validateAllFieldCriteria", "types", "convertToArrayPayload", "createSubject", "_observers", "observer", "subscribe", "push", "unsubscribe", "o", "observers", "isPrimitive", "deepEqual", "object1", "object2", "getTime", "keys1", "keys2", "val1", "val2", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMultipleSelect", "isRadioInput", "isRadioOrCheckbox", "live", "isConnected", "baseGet", "updatePath", "slice", "isEmptyArray", "unset", "paths", "childObject", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "defaultResult", "validResult", "getCheckboxValue", "options", "option", "attributes", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "NaN", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "files", "refs", "selectedOptions", "getResolverOptions", "fieldsNames", "criteriaMode", "shouldUseNativeValidation", "isRegex", "RegExp", "getRuleValue", "rule", "source", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "ASYNC_FUNCTION", "hasPromiseValidation", "fieldReference", "find", "validateFunction", "hasValidation", "isWatched", "isBlurEvent", "watchName", "startsWith", "iterateFieldsByAction", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "pop", "shouldRenderFormState", "formStateData", "shouldSubscribeByName", "signalName", "currentName", "skipValidation", "isSubmitted", "reValidateMode", "unsetEmptyArray", "updateFieldArrayRootError", "fieldArrayErrors", "isMessage", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "disabled<PERSON>ieldN<PERSON>s", "isFieldArray", "inputValue", "inputRef", "isRadio", "isCheckBox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueNumber", "valueDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "defaultOptions", "shouldFocusError", "createFormControl", "submitCount", "isSubmitting", "unMount", "delayError<PERSON><PERSON><PERSON>", "timer", "_proxySubscribeFormState", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldDisplayAllAssociatedErrors", "debounce", "wait", "clearTimeout", "setTimeout", "shouldUpdateValid", "resolver", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "from", "for<PERSON>ach", "_setFieldArray", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "field<PERSON><PERSON><PERSON>", "argA", "argB", "_getDirty", "updateErrors", "_setErrors", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updatedFormState", "context", "executeSchemaAndUpdateState", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldError", "getV<PERSON>ues", "_getFieldArray", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "shouldSkipValidation", "deps", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "Promise", "shouldFocus", "getFieldState", "clearErrors", "inputName", "currentError", "currentRef", "restOfErrorTree", "payload", "_setFormState", "reRenderRoot", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "_disableForm", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "size", "reset<PERSON>ield", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "setFocus", "shouldSelect", "_resetDefaultValues", "then", "resetOptions", "formControl", "generateId", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "focusName", "focusIndex", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "to", "splice", "prependAt", "removeAtIndexes", "indexes", "i", "temp", "removeArrayAt", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "updateAt", "useFieldArray", "keyName", "setFields", "ids", "_fieldIds", "_actioned", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "appendValue", "prepend", "prependValue", "remove", "insert$1", "insertValue", "swap", "move", "update", "item", "existingError", "useForm", "_formControl", "_values", "useLayoutEffect"], "sources": ["D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isCheckBoxInput.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isDateObject.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isNullOrUndefined.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isObject.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getEventValue.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getNodeParentName.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\isNameInFieldArray.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isPlainObject.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isWeb.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\cloneObject.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\compact.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isUndefined.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\get.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isBoolean.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isKey.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\stringToPath.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\set.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\constants.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\useFormContext.tsx", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getProxyFormState.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\useFormState.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isString.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\generateWatchOutput.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\useWatch.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\useController.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\controller.tsx", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\flatten.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\form.tsx", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\appendErrors.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\convertToArrayPayload.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\createSubject.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isPrimitive.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\deepEqual.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isEmptyObject.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isFileInput.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isFunction.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isHTMLElement.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isMultipleSelect.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isRadioInput.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isRadioOrCheckbox.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\live.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\unset.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\objectHasFunction.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getDirtyFields.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getCheckboxValue.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getFieldValueAs.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getRadioValue.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getFieldValue.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getResolverOptions.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isRegex.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getRuleValue.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getValidationModes.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\hasPromiseValidation.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\hasValidation.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\isWatched.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\iterateFieldsByAction.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\schemaErrorLookup.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\shouldRenderFormState.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\shouldSubscribeByName.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\skipValidation.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\unsetEmptyArray.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\updateFieldArrayRootError.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\isMessage.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getValidateError.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getValueAndMessage.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\validateField.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\createFormControl.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\generateId.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\logic\\getFocusFieldName.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\append.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\fillEmptyArray.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\insert.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\move.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\prepend.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\remove.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\swap.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\utils\\update.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\useFieldArray.ts", "D:\\Code\\FE_Blog\\node_modules\\react-hook-form\\src\\useForm.ts"], "sourcesContent": ["import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "import { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "export default (val: unknown): val is undefined => val === undefined;\n", "import compact from './compact';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = compact(path.split(/[,[\\].]+?/)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "export default (value: string) => /^\\w*$/.test(value);\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(): UseFormReturn<TFieldValues, TContext, TTransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TTransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext, TTransformedValues>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport {\n  FieldValues,\n  InternalFieldName,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(\n  props?: UseFormStateProps<TFieldValues, TTransformedValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n  const _name = React.useRef(name);\n\n  _name.current = name;\n\n  React.useEffect(\n    () =>\n      control._subscribe({\n        name: _name.current as InternalFieldName,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n          !disabled &&\n            updateFormState({\n              ...control._formState,\n              ...formState,\n            });\n        },\n      }),\n    [control, disabled, exact],\n  );\n\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n\nexport { useFormState };\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _name = React.useRef(name);\n  const _defaultValue = React.useRef(defaultValue);\n\n  _name.current = name;\n\n  React.useEffect(\n    () =>\n      control._subscribe({\n        name: _name.current as InternalFieldName,\n        formState: {\n          values: true,\n        },\n        exact,\n        callback: (formState) =>\n          !disabled &&\n          updateValue(\n            generateWatchOutput(\n              _name.current as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              _defaultValue.current,\n            ),\n          ),\n      }),\n    [control, disabled, exact],\n  );\n\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      defaultValue as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseControllerProps<TFieldValues, TName, TTransformedValues>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _props = React.useRef(props);\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const onChange = React.useCallback(\n    (event: any) =>\n      _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.CHANGE,\n      }),\n    [name],\n  );\n\n  const onBlur = React.useCallback(\n    () =>\n      _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.BLUR,\n      }),\n    [name, control._formValues],\n  );\n\n  const ref = React.useCallback(\n    (elm: any) => {\n      const field = get(control._fields, name);\n\n      if (field && elm) {\n        field._f.ref = {\n          focus: () => elm.focus(),\n          select: () => elm.select(),\n          setCustomValidity: (message: string) =>\n            elm.setCustomValidity(message),\n          reportValidity: () => elm.reportValidity(),\n        };\n      }\n    },\n    [control._fields, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange,\n      onBlur,\n      ref,\n    }),\n    [name, disabled, formState.disabled, onChange, onBlur, ref, value],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled)\n        ? { disabled: _props.current.disabled }\n        : {}),\n    });\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: ControllerProps<TFieldValues, TName, TTransformedValues>,\n) =>\n  props.render(useController<TFieldValues, TName, TTransformedValues>(props));\n\nexport { Controller };\n", "import { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  TFieldValues extends FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: FormProps<TFieldValues, TTransformedValues>) {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(object1: any, object2: any) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "import { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "import { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { VALIDATION_MODE } from '../constants';\nimport { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { VALIDATION_MODE } from '../constants';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & {\n    name?: InternalFieldName;\n    values?: T;\n  },\n  _proxyFormState: K,\n  updateFormState: (formState: Partial<FormState<T>>) => void,\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import { FieldError, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport {\n  BatchFieldArrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  FromSubscribe,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  UseFromSubscribe,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport shouldRenderFormState from './shouldRenderFormState';\nimport shouldSubscribeByName from './shouldSubscribeByName';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): Omit<\n  UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n  'formState'\n> & {\n  formControl: Omit<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n    'formState'\n  >;\n} {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  const _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.values || _options.defaultValues) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? ({} as TFieldValues)\n    : (cloneObject(_defaultValues) as TFieldValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    array: createSubject(),\n    state: createSubject(),\n  };\n  const validationModeBeforeSubmit = getValidationModes(_options.mode);\n  const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _setValid = async (shouldUpdateValid?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValid ||\n        _proxySubscribeFormState.isValid ||\n        shouldUpdateValid)\n    ) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _runSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating ||\n        _proxyFormState.validatingFields ||\n        _proxySubscribeFormState.isValidating ||\n        _proxySubscribeFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _setFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        (_proxyFormState.touchedFields ||\n          _proxySubscribeFormState.touchedFields) &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _setValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine = deepEqual(\n          get(_defaultValues, name),\n          fieldValue,\n        );\n\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          ((_proxyFormState.dirtyFields ||\n            _proxySubscribeFormState.dirtyFields) &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            ((_proxyFormState.touchedFields ||\n              _proxySubscribeFormState.touchedFields) &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _runSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _runSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.length > 1\n              ? fieldReference.refs.forEach(\n                  (checkboxRef) =>\n                    (!checkboxRef.defaultChecked || !checkboxRef.disabled) &&\n                    (checkboxRef.checked = Array.isArray(fieldValue)\n                      ? !!(fieldValue as []).find(\n                          (data: string) => data === checkboxRef.value,\n                        )\n                      : fieldValue === checkboxRef.value),\n                )\n              : fieldReference.refs[0] &&\n                (fieldReference.refs[0].checked = !!fieldValue);\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues),\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      const fieldValue = value[fieldKey];\n      const fieldName = `${name}.${fieldKey}`;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues),\n      });\n\n      if (\n        (_proxyFormState.isDirty ||\n          _proxyFormState.dirtyFields ||\n          _proxySubscribeFormState.isDirty ||\n          _proxySubscribeFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues),\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name: string = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const _updateIsFieldValueUpdated = (fieldValue: unknown) => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type\n        ? getFieldValue(field._f)\n        : getEventValue(event);\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.state.next({\n          name,\n          type: event.type,\n          values: cloneObject(_formValues),\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _runSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (\n            _proxyFormState.isValid ||\n            _proxySubscribeFormState.isValid\n          ) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n        isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.state.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const _subscribe: FromSubscribe<TFieldValues> = (props) =>\n    _subjects.state.subscribe({\n      next: (\n        formState: Partial<FormState<TFieldValues>> & {\n          name?: InternalFieldName;\n          values?: TFieldValues | undefined;\n        },\n      ) => {\n        if (\n          shouldSubscribeByName(props.name, formState.name, props.exact) &&\n          shouldRenderFormState(\n            formState,\n            (props.formState as ReadFormState) || _proxyFormState,\n            _setFormState,\n            props.reRenderRoot,\n          )\n        ) {\n          props.callback({\n            values: { ..._formValues } as TFieldValues,\n            ..._formState,\n            ...formState,\n          });\n        }\n      },\n    }).unsubscribe;\n\n  const subscribe: UseFromSubscribe<TFieldValues> = (props) => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState,\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState,\n    });\n  };\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.state.next({\n      values: cloneObject(_formValues),\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _setValid();\n  };\n\n  const _setDisabledField: Control<TFieldValues>['_setDisabledField'] = ({\n    disabled,\n    name,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues, TTransformedValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        (e as React.BaseSyntheticEvent).persist &&\n          (e as React.BaseSyntheticEvent).persist();\n      }\n      let fieldValues: TFieldValues | TTransformedValues | {} =\n        cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _runSchema();\n        _formState.errors = errors;\n        fieldValues = values as TFieldValues;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          set(fieldValues, name, undefined);\n        }\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TTransformedValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as Parameters<typeof setValue<typeof name>>[1],\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        for (const fieldName of _names.mount) {\n          setValue(\n            fieldName as FieldPath<TFieldValues>,\n            get(values, fieldName),\n          );\n        }\n      }\n\n      _formValues = cloneObject(values) as TFieldValues;\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.state.next({\n        values: { ...values } as TFieldValues,\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _setFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n\n  return {\n    ...methods,\n    formControl: methods,\n  };\n}\n", "export default () => {\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFieldArrayProps<\n    TFieldValues,\n    TFieldArrayName,\n    TKeyName,\n    TTransformedValues\n  >,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  rules &&\n    (control as Control<TFieldValues, any, TTransformedValues>).register(\n      name as FieldPath<TFieldValues>,\n      rules as RegisterOptions<TFieldValues>,\n    );\n\n  React.useEffect(\n    () =>\n      control._subjects.array.subscribe({\n        next: ({\n          values,\n          name: fieldArrayName,\n        }: {\n          values?: FieldValues;\n          name?: InternalFieldName;\n        }) => {\n          if (fieldArrayName === _name.current || !fieldArrayName) {\n            const fieldValues = get(values, _name.current);\n            if (Array.isArray(fieldValues)) {\n              setFields(fieldValues);\n              ids.current = fieldValues.map(generateId);\n            }\n          }\n        },\n      }).unsubscribe,\n    [control],\n  );\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._setFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted) &&\n      !getValidationModes(control._options.reValidateMode).isOnSubmit\n    ) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues) as TFieldValues,\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n\n    return () => {\n      const updateMounted = (name: InternalFieldName, value: boolean) => {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n\n      control._options.shouldUnregister || shouldUnregister\n        ? control.unregister(name as FieldPath<TFieldValues>)\n        : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport { createFormControl } from './logic/createFormControl';\nimport getProxyFormState from './logic/getProxyFormState';\nimport deepEqual from './utils/deepEqual';\nimport isEmptyObject from './utils/isEmptyObject';\nimport isFunction from './utils/isFunction';\nimport { FieldValues, FormState, UseFormProps, UseFormReturn } from './types';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...(props.formControl ? props.formControl : createFormControl(props)),\n      formState,\n    };\n\n    if (\n      props.formControl &&\n      props.defaultValues &&\n      !isFunction(props.defaultValues)\n    ) {\n      props.formControl.reset(props.defaultValues, props.resetOptions);\n    }\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  React.useLayoutEffect(\n    () =>\n      control._subscribe({\n        formState: control._proxyFormState,\n        callback: () => updateFormState({ ...control._formState }),\n        reRenderRoot: true,\n      }),\n    [control],\n  );\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [props.values, control]);\n\n  React.useEffect(() => {\n    if (props.errors && !isEmptyObject(props.errors)) {\n      control._setErrors(props.errors);\n    }\n  }, [props.errors, control]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.state.next({\n        values: control._getWatch(),\n      });\n  }, [props.shouldUnregister, control]);\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "mappings": ";AAEA,IAAAA,eAAA,GAAgBC,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,UAAU;ACH7B,IAAAC,YAAA,GAAgBC,KAAc,IAAoBA,KAAK,YAAYC,IAAI;ACAvE,IAAAC,iBAAA,GAAgBF,KAAc,IAAgCA,KAAK,IAAI,IAAI;ACGpE,MAAMG,YAAY,GAAIH,KAAc,IACzC,OAAOA,KAAK,KAAK,QAAQ;AAE3B,IAAAI,QAAA,GAAkCJ,KAAc,IAC9C,CAACE,iBAAiB,CAACF,KAAK,CAAC,IACzB,CAACK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,IACrBG,YAAY,CAACH,KAAK,CAAC,IACnB,CAACD,YAAY,CAACC,KAAK,CAAC;ACLtB,IAAAO,aAAA,GAAgBC,KAAc,IAC5BJ,QAAQ,CAACI,KAAK,CAAC,IAAKA,KAAe,CAACC,MAAA,GAChCb,eAAe,CAAEY,KAAe,CAACC,MAAM,IACpCD,KAAe,CAACC,MAAM,CAACC,OAAA,GACvBF,KAAe,CAACC,MAAM,CAACT,KAAA,GAC1BQ,KAAK;ACVX,IAAAG,iBAAA,GAAgBC,IAAY,IAC1BA,IAAI,CAACC,SAAS,CAAC,CAAC,EAAED,IAAI,CAACE,MAAM,CAAC,aAAa,CAAC,CAAC,IAAIF,IAAI;ACGvD,IAAAG,kBAAA,GAAeA,CAACC,KAA6B,EAAEJ,IAAuB,KACpEI,KAAK,CAACC,GAAG,CAACN,iBAAiB,CAACC,IAAI,CAAC,CAAC;ACHpC,IAAAM,aAAA,GAAgBC,UAAkB,IAAI;EACpC,MAAMC,aAAa,GACjBD,UAAU,CAACE,WAAW,IAAIF,UAAU,CAACE,WAAW,CAACC,SAAS;EAE5D,OACElB,QAAQ,CAACgB,aAAa,CAAC,IAAIA,aAAa,CAACG,cAAc,CAAC,eAAe,CAAC;AAE5E,CAAC;ACTD,IAAAC,KAAA,GAAe,OAAOC,MAAM,KAAK,WAAW,IAC1C,OAAOA,MAAM,CAACC,WAAW,KAAK,WAAW,IACzC,OAAOC,QAAQ,KAAK,WAAW;ACET,SAAAC,WAAWA,CAAIC,IAAO;EAC5C,IAAIC,IAAS;EACb,MAAMxB,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EACnC,MAAME,kBAAkB,GACtB,OAAOC,QAAQ,KAAK,WAAW,GAAGH,IAAI,YAAYG,QAAQ,GAAG,KAAK;EAEpE,IAAIH,IAAI,YAAY5B,IAAI,EAAE;IACxB6B,IAAI,GAAG,IAAI7B,IAAI,CAAC4B,IAAI,CAAC;SAChB,IAAIA,IAAI,YAAYI,GAAG,EAAE;IAC9BH,IAAI,GAAG,IAAIG,GAAG,CAACJ,IAAI,CAAC;SACf,IACL,EAAEL,KAAK,KAAKK,IAAI,YAAYK,IAAI,IAAIH,kBAAkB,CAAC,CAAC,KACvDzB,OAAO,IAAIF,QAAQ,CAACyB,IAAI,CAAC,CAAC,EAC3B;IACAC,IAAI,GAAGxB,OAAO,GAAG,EAAE,GAAG,EAAE;IAExB,IAAI,CAACA,OAAO,IAAI,CAACY,aAAa,CAACW,IAAI,CAAC,EAAE;MACpCC,IAAI,GAAGD,IAAI;WACN;MACL,KAAK,MAAMM,GAAG,IAAIN,IAAI,EAAE;QACtB,IAAIA,IAAI,CAACN,cAAc,CAACY,GAAG,CAAC,EAAE;UAC5BL,IAAI,CAACK,GAAG,CAAC,GAAGP,WAAW,CAACC,IAAI,CAACM,GAAG,CAAC,CAAC;;;;SAInC;IACL,OAAON,IAAI;;EAGb,OAAOC,IAAI;AACb;AClCA,IAAAM,OAAA,GAAwBpC,KAAe,IACrCK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,CAACqC,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE;ACDnD,IAAAC,WAAA,GAAgBC,GAAY,IAAuBA,GAAG,KAAKC,SAAS;ACKpE,IAAAC,GAAA,GAAeA,CACbC,MAAS,EACTC,IAAoB,EACpBC,YAAsB,KACf;EACP,IAAI,CAACD,IAAI,IAAI,CAACxC,QAAQ,CAACuC,MAAM,CAAC,EAAE;IAC9B,OAAOE,YAAY;;EAGrB,MAAMC,MAAM,GAAGV,OAAO,CAACQ,IAAI,CAACG,KAAK,CAAC,WAAW,CAAC,CAAC,CAACC,MAAM,CACpD,CAACF,MAAM,EAAEX,GAAG,KACVjC,iBAAiB,CAAC4C,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,CAACX,GAAe,CAAC,EAC9DQ,MAAM,CACP;EAED,OAAOJ,WAAW,CAACO,MAAM,CAAC,IAAIA,MAAM,KAAKH,MAAA,GACrCJ,WAAW,CAACI,MAAM,CAACC,IAAe,CAAC,IACjCC,YAAA,GACAF,MAAM,CAACC,IAAe,IACxBE,MAAM;AACZ,CAAC;ACzBD,IAAAG,SAAA,GAAgBjD,KAAc,IAAuB,OAAOA,KAAK,KAAK,SAAS;ACA/E,IAAAkD,KAAA,GAAgBlD,KAAa,IAAK,OAAO,CAACmD,IAAI,CAACnD,KAAK,CAAC;ACErD,IAAAoD,YAAA,GAAgBC,KAAa,IAC3BjB,OAAO,CAACiB,KAAK,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACP,KAAK,CAAC,OAAO,CAAC,CAAC;ACGxD,IAAAQ,GAAA,GAAeA,CACbZ,MAAmB,EACnBC,IAA4B,EAC5B5C,KAAe,KACb;EACF,IAAIwD,KAAK,GAAG,EAAE;EACd,MAAMC,QAAQ,GAAGP,KAAK,CAACN,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC,GAAGQ,YAAY,CAACR,IAAI,CAAC;EAC1D,MAAMc,MAAM,GAAGD,QAAQ,CAACC,MAAM;EAC9B,MAAMC,SAAS,GAAGD,MAAM,GAAG,CAAC;EAE5B,OAAO,EAAEF,KAAK,GAAGE,MAAM,EAAE;IACvB,MAAMvB,GAAG,GAAGsB,QAAQ,CAACD,KAAK,CAAC;IAC3B,IAAII,QAAQ,GAAG5D,KAAK;IAEpB,IAAIwD,KAAK,KAAKG,SAAS,EAAE;MACvB,MAAME,QAAQ,GAAGlB,MAAM,CAACR,GAAG,CAAC;MAC5ByB,QAAQ,GACNxD,QAAQ,CAACyD,QAAQ,CAAC,IAAIxD,KAAK,CAACC,OAAO,CAACuD,QAAQ,IACxCA,QAAA,GACA,CAACC,KAAK,CAAC,CAACL,QAAQ,CAACD,KAAK,GAAG,CAAC,CAAC,IACzB,KACA,EAAE;;IAGZ,IAAIrB,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,aAAa,IAAIA,GAAG,KAAK,WAAW,EAAE;MACvE;;IAGFQ,MAAM,CAACR,GAAG,CAAC,GAAGyB,QAAQ;IACtBjB,MAAM,GAAGA,MAAM,CAACR,GAAG,CAAC;;AAExB,CAAC;ACrCM,MAAM4B,MAAM,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE;CACA;AAEH,MAAMC,eAAe,GAAG;EAC7BC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,WAAW;EACtBC,GAAG,EAAE;CACG;AAEH,MAAMC,sBAAsB,GAAG;EACpCC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;CACF;AClBV,MAAMC,eAAe,GAAGC,KAAK,CAACC,aAAa,CAAuB,IAAI,CAAC;AAEvE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACI,MAAMC,cAAc,GAAGA,CAAA,KAK5BF,KAAK,CAACG,UAAU,CAACJ,eAAe;AAMlC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACU,MAAAK,YAAY,GAKvBC,KAAoE,IAClE;EACF,MAAM;IAAEC,QAAQ;IAAE,GAAG3D;EAAI,CAAE,GAAG0D,KAAK;EACnC,OACEL,KAAA,CAAAO,aAAA,CAACR,eAAe,CAACS,QAAQ;IAAC1F,KAAK,EAAE6B;EAAgC,GAC9D2D,QAAQ,CACgB;AAE/B;ACvFA,IAAAG,iBAAA,GAAeA,CAKbC,SAAkC,EAClCC,OAA4D,EAC5DC,mBAAmC,EACnCC,MAAM,GAAG,IAAI,KACX;EACF,MAAMjD,MAAM,GAAG;IACbkD,aAAa,EAAEH,OAAO,CAACI;GACJ;EAErB,KAAK,MAAM9D,GAAG,IAAIyD,SAAS,EAAE;IAC3BM,MAAM,CAACC,cAAc,CAACrD,MAAM,EAAEX,GAAG,EAAE;MACjCO,GAAG,EAAEA,CAAA,KAAK;QACR,MAAM0D,IAAI,GAAGjE,GAA0D;QAEvE,IAAI0D,OAAO,CAACQ,eAAe,CAACD,IAAI,CAAC,KAAKjC,eAAe,CAACK,GAAG,EAAE;UACzDqB,OAAO,CAACQ,eAAe,CAACD,IAAI,CAAC,GAAG,CAACL,MAAM,IAAI5B,eAAe,CAACK,GAAG;;QAGhEsB,mBAAmB,KAAKA,mBAAmB,CAACM,IAAI,CAAC,GAAG,IAAI,CAAC;QACzD,OAAOR,SAAS,CAACQ,IAAI,CAAC;;IAEzB,EAAC;;EAGJ,OAAOtD,MAAM;AACf,CAAC;;ACtBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACH,SAASwD,YAAYA,CAInBf,KAA2D;EAE3D,MAAMgB,OAAO,GAAGnB,cAAc,EAAyC;EACvE,MAAM;IAAES,OAAO,GAAGU,OAAO,CAACV,OAAO;IAAEW,QAAQ;IAAE5F,IAAI;IAAE6F;EAAK,CAAE,GAAGlB,KAAK,IAAI,EAAE;EACxE,MAAM,CAACK,SAAS,EAAEc,eAAe,CAAC,GAAGxB,KAAK,CAACyB,QAAQ,CAACd,OAAO,CAACe,UAAU,CAAC;EACvE,MAAMC,oBAAoB,GAAG3B,KAAK,CAAC4B,MAAM,CAAC;IACxCC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,KAAK;IACpBC,gBAAgB,EAAE,KAAK;IACvBC,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;EACT,EAAC;EACF,MAAMC,KAAK,GAAGrC,KAAK,CAAC4B,MAAM,CAAClG,IAAI,CAAC;EAEhC2G,KAAK,CAACC,OAAO,GAAG5G,IAAI;EAEpBsE,KAAK,CAACuC,SAAS,CACb,MACE5B,OAAO,CAAC6B,UAAU,CAAC;IACjB9G,IAAI,EAAE2G,KAAK,CAACC,OAA4B;IACxC5B,SAAS,EAAEiB,oBAAoB,CAACW,OAAO;IACvCf,KAAK;IACLkB,QAAQ,EAAG/B,SAAS,IAAI;MACtB,CAACY,QAAQ,IACPE,eAAe,CAAC;QACd,GAAGb,OAAO,CAACe,UAAU;QACrB,GAAGhB;MACJ,EAAC;;GAEP,CAAC,EACJ,CAACC,OAAO,EAAEW,QAAQ,EAAEC,KAAK,CAAC,CAC3B;EAEDvB,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnBZ,oBAAoB,CAACW,OAAO,CAACH,OAAO,IAAIxB,OAAO,CAAC+B,SAAS,CAAC,IAAI,CAAC;EACjE,CAAC,EAAE,CAAC/B,OAAO,CAAC,CAAC;EAEb,OAAOX,KAAK,CAAC2C,OAAO,CAClB,MACElC,iBAAiB,CACfC,SAAS,EACTC,OAAO,EACPgB,oBAAoB,CAACW,OAAO,EAC5B,KAAK,CACN,EACH,CAAC5B,SAAS,EAAEC,OAAO,CAAC,CACrB;AACH;AC/FA,IAAAiC,QAAA,GAAgB9H,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;ACI7E,IAAA+H,mBAAA,GAAeA,CACb/G,KAAoC,EACpCgH,MAAa,EACbC,UAAwB,EACxBC,QAAkB,EAClBrF,YAAuC,KACrC;EACF,IAAIiF,QAAQ,CAAC9G,KAAK,CAAC,EAAE;IACnBkH,QAAQ,IAAIF,MAAM,CAACG,KAAK,CAACC,GAAG,CAACpH,KAAK,CAAC;IACnC,OAAO0B,GAAG,CAACuF,UAAU,EAAEjH,KAAK,EAAE6B,YAAY,CAAC;;EAG7C,IAAIxC,KAAK,CAACC,OAAO,CAACU,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CAACqH,GAAG,CACbC,SAAS,KACRJ,QAAQ,IAAIF,MAAM,CAACG,KAAK,CAACC,GAAG,CAACE,SAAS,CAAC,EAAE5F,GAAG,CAACuF,UAAU,EAAEK,SAAS,CAAC,CACpE,CACF;;EAGHJ,QAAQ,KAAKF,MAAM,CAACO,QAAQ,GAAG,IAAI,CAAC;EAEpC,OAAON,UAAU;AACnB,CAAC;;ACoGD;;;;;;;;;;;;;;;AAeG;AACG,SAAUO,QAAQA,CACtBjD,KAAmC;EAEnC,MAAMgB,OAAO,GAAGnB,cAAc,EAAE;EAChC,MAAM;IACJS,OAAO,GAAGU,OAAO,CAACV,OAAO;IACzBjF,IAAI;IACJiC,YAAY;IACZ2D,QAAQ;IACRC;EAAK,CACN,GAAGlB,KAAK,IAAI,EAAE;EACf,MAAMgC,KAAK,GAAGrC,KAAK,CAAC4B,MAAM,CAAClG,IAAI,CAAC;EAChC,MAAM6H,aAAa,GAAGvD,KAAK,CAAC4B,MAAM,CAACjE,YAAY,CAAC;EAEhD0E,KAAK,CAACC,OAAO,GAAG5G,IAAI;EAEpBsE,KAAK,CAACuC,SAAS,CACb,MACE5B,OAAO,CAAC6B,UAAU,CAAC;IACjB9G,IAAI,EAAE2G,KAAK,CAACC,OAA4B;IACxC5B,SAAS,EAAE;MACT8C,MAAM,EAAE;IACT;IACDjC,KAAK;IACLkB,QAAQ,EAAG/B,SAAS,IAClB,CAACY,QAAQ,IACTmC,WAAW,CACTZ,mBAAmB,CACjBR,KAAK,CAACC,OAAkD,EACxD3B,OAAO,CAACmC,MAAM,EACdpC,SAAS,CAAC8C,MAAM,IAAI7C,OAAO,CAAC+C,WAAW,EACvC,KAAK,EACLH,aAAa,CAACjB,OAAO,CACtB;GAEN,CAAC,EACJ,CAAC3B,OAAO,EAAEW,QAAQ,EAAEC,KAAK,CAAC,CAC3B;EAED,MAAM,CAACzG,KAAK,EAAE2I,WAAW,CAAC,GAAGzD,KAAK,CAACyB,QAAQ,CACzCd,OAAO,CAACgD,SAAS,CACfjI,IAAyB,EACzBiC,YAAqD,CACtD,CACF;EAEDqC,KAAK,CAACuC,SAAS,CAAC,MAAM5B,OAAO,CAACiD,gBAAgB,EAAE,CAAC;EAEjD,OAAO9I,KAAK;AACd;;ACxKA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACG,SAAU+I,aAAaA,CAK3BxD,KAAkE;EAElE,MAAMgB,OAAO,GAAGnB,cAAc,EAAyC;EACvE,MAAM;IAAExE,IAAI;IAAE4F,QAAQ;IAAEX,OAAO,GAAGU,OAAO,CAACV,OAAO;IAAEmD;EAAgB,CAAE,GAAGzD,KAAK;EAC7E,MAAM0D,YAAY,GAAGlI,kBAAkB,CAAC8E,OAAO,CAACmC,MAAM,CAACkB,KAAK,EAAEtI,IAAI,CAAC;EACnE,MAAMZ,KAAK,GAAGwI,QAAQ,CAAC;IACrB3C,OAAO;IACPjF,IAAI;IACJiC,YAAY,EAAEH,GAAG,CACfmD,OAAO,CAAC+C,WAAW,EACnBhI,IAAI,EACJ8B,GAAG,CAACmD,OAAO,CAACI,cAAc,EAAErF,IAAI,EAAE2E,KAAK,CAAC1C,YAAY,CAAC,CACtD;IACD4D,KAAK,EAAE;EACR,EAAwC;EACzC,MAAMb,SAAS,GAAGU,YAAY,CAAC;IAC7BT,OAAO;IACPjF,IAAI;IACJ6F,KAAK,EAAE;EACR,EAAC;EAEF,MAAM0C,MAAM,GAAGjE,KAAK,CAAC4B,MAAM,CAACvB,KAAK,CAAC;EAClC,MAAM6D,cAAc,GAAGlE,KAAK,CAAC4B,MAAM,CACjCjB,OAAO,CAACwD,QAAQ,CAACzI,IAAI,EAAE;IACrB,GAAG2E,KAAK,CAAC+D,KAAK;IACdtJ,KAAK;IACL,IAAIiD,SAAS,CAACsC,KAAK,CAACiB,QAAQ,CAAC,GAAG;MAAEA,QAAQ,EAAEjB,KAAK,CAACiB;IAAQ,CAAE,GAAG,EAAE;EAClE,EAAC,CACH;EAED,MAAM+C,UAAU,GAAGrE,KAAK,CAAC2C,OAAO,CAC9B,MACE3B,MAAM,CAACsD,gBAAgB,CACrB,EAAE,EACF;IACEC,OAAO,EAAE;MACPC,UAAU,EAAE,IAAI;MAChBhH,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAACkD,SAAS,CAAC0B,MAAM,EAAE1G,IAAI;IACxC;IACDmG,OAAO,EAAE;MACP2C,UAAU,EAAE,IAAI;MAChBhH,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAACkD,SAAS,CAACqB,WAAW,EAAErG,IAAI;IAC7C;IACD+I,SAAS,EAAE;MACTD,UAAU,EAAE,IAAI;MAChBhH,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAACkD,SAAS,CAACsB,aAAa,EAAEtG,IAAI;IAC/C;IACDwG,YAAY,EAAE;MACZsC,UAAU,EAAE,IAAI;MAChBhH,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAACkD,SAAS,CAACuB,gBAAgB,EAAEvG,IAAI;IAClD;IACDgJ,KAAK,EAAE;MACLF,UAAU,EAAE,IAAI;MAChBhH,GAAG,EAAEA,CAAA,KAAMA,GAAG,CAACkD,SAAS,CAAC0B,MAAM,EAAE1G,IAAI;IACtC;EACF,EACsB,EAC3B,CAACgF,SAAS,EAAEhF,IAAI,CAAC,CAClB;EAED,MAAMyD,QAAQ,GAAGa,KAAK,CAAC2E,WAAW,CAC/BrJ,KAAU,IACT4I,cAAc,CAAC5B,OAAO,CAACnD,QAAQ,CAAC;IAC9B5D,MAAM,EAAE;MACNT,KAAK,EAAEO,aAAa,CAACC,KAAK,CAAC;MAC3BI,IAAI,EAAEA;IACP;IACDd,IAAI,EAAEiE,MAAM,CAACG;EACd,EAAC,EACJ,CAACtD,IAAI,CAAC,CACP;EAED,MAAMwD,MAAM,GAAGc,KAAK,CAAC2E,WAAW,CAC9B,MACET,cAAc,CAAC5B,OAAO,CAACpD,MAAM,CAAC;IAC5B3D,MAAM,EAAE;MACNT,KAAK,EAAE0C,GAAG,CAACmD,OAAO,CAAC+C,WAAW,EAAEhI,IAAI,CAAC;MACrCA,IAAI,EAAEA;IACP;IACDd,IAAI,EAAEiE,MAAM,CAACC;GACd,CAAC,EACJ,CAACpD,IAAI,EAAEiF,OAAO,CAAC+C,WAAW,CAAC,CAC5B;EAED,MAAMkB,GAAG,GAAG5E,KAAK,CAAC2E,WAAW,CAC1BE,GAAQ,IAAI;IACX,MAAMC,KAAK,GAAGtH,GAAG,CAACmD,OAAO,CAACoE,OAAO,EAAErJ,IAAI,CAAC;IAExC,IAAIoJ,KAAK,IAAID,GAAG,EAAE;MAChBC,KAAK,CAACE,EAAE,CAACJ,GAAG,GAAG;QACbK,KAAK,EAAEA,CAAA,KAAMJ,GAAG,CAACI,KAAK,EAAE;QACxBC,MAAM,EAAEA,CAAA,KAAML,GAAG,CAACK,MAAM,EAAE;QAC1BC,iBAAiB,EAAGC,OAAe,IACjCP,GAAG,CAACM,iBAAiB,CAACC,OAAO,CAAC;QAChCC,cAAc,EAAEA,CAAA,KAAMR,GAAG,CAACQ,cAAc;OACzC;;GAEJ,EACD,CAAC1E,OAAO,CAACoE,OAAO,EAAErJ,IAAI,CAAC,CACxB;EAED,MAAMoJ,KAAK,GAAG9E,KAAK,CAAC2C,OAAO,CACzB,OAAO;IACLjH,IAAI;IACJZ,KAAK;IACL,IAAIiD,SAAS,CAACuD,QAAQ,CAAC,IAAIZ,SAAS,CAACY,QAAA,GACjC;MAAEA,QAAQ,EAAEZ,SAAS,CAACY,QAAQ,IAAIA;IAAQ,IAC1C,EAAE,CAAC;IACPnC,QAAQ;IACRD,MAAM;IACN0F;EACD,EAAC,EACF,CAAClJ,IAAI,EAAE4F,QAAQ,EAAEZ,SAAS,CAACY,QAAQ,EAAEnC,QAAQ,EAAED,MAAM,EAAE0F,GAAG,EAAE9J,KAAK,CAAC,CACnE;EAEDkF,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,MAAM+C,sBAAsB,GAC1B3E,OAAO,CAAC4E,QAAQ,CAACzB,gBAAgB,IAAIA,gBAAgB;IAEvDnD,OAAO,CAACwD,QAAQ,CAACzI,IAAI,EAAE;MACrB,GAAGuI,MAAM,CAAC3B,OAAO,CAAC8B,KAAK;MACvB,IAAIrG,SAAS,CAACkG,MAAM,CAAC3B,OAAO,CAAChB,QAAQ,IACjC;QAAEA,QAAQ,EAAE2C,MAAM,CAAC3B,OAAO,CAAChB;MAAQ,IACnC,EAAE;IACP,EAAC;IAEF,MAAMkE,aAAa,GAAGA,CAAC9J,IAAuB,EAAEZ,KAAc,KAAI;MAChE,MAAMgK,KAAK,GAAUtH,GAAG,CAACmD,OAAO,CAACoE,OAAO,EAAErJ,IAAI,CAAC;MAE/C,IAAIoJ,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;QACrBF,KAAK,CAACE,EAAE,CAACS,KAAK,GAAG3K,KAAK;;IAE1B,CAAC;IAED0K,aAAa,CAAC9J,IAAI,EAAE,IAAI,CAAC;IAEzB,IAAI4J,sBAAsB,EAAE;MAC1B,MAAMxK,KAAK,GAAG4B,WAAW,CAACc,GAAG,CAACmD,OAAO,CAAC4E,QAAQ,CAACzE,aAAa,EAAEpF,IAAI,CAAC,CAAC;MACpE2C,GAAG,CAACsC,OAAO,CAACI,cAAc,EAAErF,IAAI,EAAEZ,KAAK,CAAC;MACxC,IAAIuC,WAAW,CAACG,GAAG,CAACmD,OAAO,CAAC+C,WAAW,EAAEhI,IAAI,CAAC,CAAC,EAAE;QAC/C2C,GAAG,CAACsC,OAAO,CAAC+C,WAAW,EAAEhI,IAAI,EAAEZ,KAAK,CAAC;;;IAIzC,CAACiJ,YAAY,IAAIpD,OAAO,CAACwD,QAAQ,CAACzI,IAAI,CAAC;IAEvC,OAAO,MAAK;MACV,CACEqI,YAAA,GACIuB,sBAAsB,IAAI,CAAC3E,OAAO,CAAC+E,MAAM,CAACC,MAAA,GAC1CL,sBAAsB,IAExB3E,OAAO,CAACiF,UAAU,CAAClK,IAAI,IACvB8J,aAAa,CAAC9J,IAAI,EAAE,KAAK,CAAC;IAChC,CAAC;GACF,EAAE,CAACA,IAAI,EAAEiF,OAAO,EAAEoD,YAAY,EAAED,gBAAgB,CAAC,CAAC;EAEnD9D,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB5B,OAAO,CAACkF,iBAAiB,CAAC;MACxBvE,QAAQ;MACR5F;IACD,EAAC;GACH,EAAE,CAAC4F,QAAQ,EAAE5F,IAAI,EAAEiF,OAAO,CAAC,CAAC;EAE7B,OAAOX,KAAK,CAAC2C,OAAO,CAClB,OAAO;IACLmC,KAAK;IACLpE,SAAS;IACT2D;GACD,CAAC,EACF,CAACS,KAAK,EAAEpE,SAAS,EAAE2D,UAAU,CAAC,CAC/B;AACH;;AC9NA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCG;AACH,MAAMyB,UAAU,GAKdzF,KAA+D,IAE/DA,KAAK,CAAC0F,MAAM,CAAClC,aAAa,CAA0CxD,KAAK,CAAC;AChDrE,MAAM2F,OAAO,GAAIC,GAAgB,IAAI;EAC1C,MAAMC,MAAM,GAAgB,EAAE;EAE9B,KAAK,MAAMjJ,GAAG,IAAI+D,MAAM,CAACmF,IAAI,CAACF,GAAG,CAAC,EAAE;IAClC,IAAIhL,YAAY,CAACgL,GAAG,CAAChJ,GAAG,CAAC,CAAC,IAAIgJ,GAAG,CAAChJ,GAAG,CAAC,KAAK,IAAI,EAAE;MAC/C,MAAMmJ,MAAM,GAAGJ,OAAO,CAACC,GAAG,CAAChJ,GAAG,CAAC,CAAC;MAEhC,KAAK,MAAMoJ,SAAS,IAAIrF,MAAM,CAACmF,IAAI,CAACC,MAAM,CAAC,EAAE;QAC3CF,MAAM,CAAC,GAAGjJ,GAAG,IAAIoJ,SAAS,EAAE,CAAC,GAAGD,MAAM,CAACC,SAAS,CAAC;;WAE9C;MACLH,MAAM,CAACjJ,GAAG,CAAC,GAAGgJ,GAAG,CAAChJ,GAAG,CAAC;;;EAI1B,OAAOiJ,MAAM;AACf,CAAC;ACdD,MAAMI,YAAY,GAAG,MAAM;AAE3B;;;;;;;;;;;;;;;;;;;;;AAqBG;AACH,SAASC,IAAIA,CAGXlG,KAAkD;EAClD,MAAMgB,OAAO,GAAGnB,cAAc,EAAyC;EACvE,MAAM,CAACsG,OAAO,EAAEC,UAAU,CAAC,GAAGzG,KAAK,CAACyB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM;IACJd,OAAO,GAAGU,OAAO,CAACV,OAAO;IACzBvB,QAAQ;IACRkB,QAAQ;IACRqF,MAAM;IACNe,MAAM,GAAGJ,YAAY;IACrBK,OAAO;IACPC,OAAO;IACPC,OAAO;IACPd,MAAM;IACNe,SAAS;IACTC,cAAc;IACd,GAAGC;EAAI,CACR,GAAG3G,KAAK;EAET,MAAM4G,MAAM,GAAG,MAAO3L,KAAgC,IAAI;IACxD,IAAI4L,QAAQ,GAAG,KAAK;IACpB,IAAItM,IAAI,GAAG,EAAE;IAEb,MAAM+F,OAAO,CAACwG,YAAY,CAAC,MAAOxK,IAAI,IAAI;MACxC,MAAMyK,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAIC,YAAY,GAAG,EAAE;MAErB,IAAI;QACFA,YAAY,GAAGC,IAAI,CAACC,SAAS,CAAC7K,IAAI,CAAC;QACnC,OAAM8K,EAAA;MAER,MAAMC,iBAAiB,GAAG1B,OAAO,CAACrF,OAAO,CAAC+C,WAAW,CAAC;MAEtD,KAAK,MAAMzG,GAAG,IAAIyK,iBAAiB,EAAE;QACnCN,QAAQ,CAACO,MAAM,CAAC1K,GAAG,EAAEyK,iBAAiB,CAACzK,GAAG,CAAC,CAAC;;MAG9C,IAAImC,QAAQ,EAAE;QACZ,MAAMA,QAAQ,CAAC;UACbzC,IAAI;UACJrB,KAAK;UACLoL,MAAM;UACNU,QAAQ;UACRE;QACD,EAAC;;MAGJ,IAAI3B,MAAM,EAAE;QACV,IAAI;UACF,MAAMiC,6BAA6B,GAAG,CACpCjB,OAAO,IAAIA,OAAO,CAAC,cAAc,CAAC,EAClCC,OAAO,CACR,CAACiB,IAAI,CAAE/M,KAAK,IAAKA,KAAK,IAAIA,KAAK,CAACgN,QAAQ,CAAC,MAAM,CAAC,CAAC;UAElD,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACC,MAAM,CAACtC,MAAM,CAAC,EAAE;YAC3Ce,MAAM;YACNC,OAAO,EAAE;cACP,GAAGA,OAAO;cACV,IAAIC,OAAO,GAAG;gBAAE,cAAc,EAAEA;cAAO,CAAE,GAAG,EAAE;YAC/C;YACDsB,IAAI,EAAEN,6BAA6B,GAAGN,YAAY,GAAGF;UACtD,EAAC;UAEF,IACEW,QAAQ,KACPhB,cAAA,GACG,CAACA,cAAc,CAACgB,QAAQ,CAACI,MAAM,IAC/BJ,QAAQ,CAACI,MAAM,GAAG,GAAG,IAAIJ,QAAQ,CAACI,MAAM,IAAI,GAAG,CAAC,EACpD;YACAjB,QAAQ,GAAG,IAAI;YACfL,OAAO,IAAIA,OAAO,CAAC;cAAEkB;YAAQ,CAAE,CAAC;YAChCnN,IAAI,GAAGqN,MAAM,CAACF,QAAQ,CAACI,MAAM,CAAC;iBACzB;YACLrB,SAAS,IAAIA,SAAS,CAAC;cAAEiB;YAAQ,CAAE,CAAC;;UAEtC,OAAOrD,KAAc,EAAE;UACvBwC,QAAQ,GAAG,IAAI;UACfL,OAAO,IAAIA,OAAO,CAAC;YAAEnC;UAAK,CAAE,CAAC;;;IAGnC,CAAC,CAAC,CAACpJ,KAAK,CAAC;IAET,IAAI4L,QAAQ,IAAI7G,KAAK,CAACM,OAAO,EAAE;MAC7BN,KAAK,CAACM,OAAO,CAACyH,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACjCC,kBAAkB,EAAE;MACrB,EAAC;MACFlI,KAAK,CAACM,OAAO,CAAC6H,QAAQ,CAAC,aAAa,EAAE;QACpC5N;MACD,EAAC;;EAEN,CAAC;EAEDoF,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnBkE,UAAU,CAAC,IAAI,CAAC;GACjB,EAAE,EAAE,CAAC;EAEN,OAAOV,MAAM,GACX/F,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAAyI,QAAA,QACG1C,MAAM,CAAC;IACNkB;EACD,EAAC,CACD,GAEHjH,KAAA,CAAAO,aAAA;IACEmI,UAAU,EAAElC,OAAO;IACnBb,MAAM,EAAEA,MAAM;IACde,MAAM,EAAEA,MAAM;IACdE,OAAO,EAAEA,OAAO;IAChBxH,QAAQ,EAAE6H,MAAM;IAAA,GACZD;EAAI,GAEP1G,QAAQ,CAEZ;AACH;AC5IA,IAAAqI,YAAA,GAAeA,CACbjN,IAAuB,EACvBkN,wBAAiC,EACjCxG,MAA2B,EAC3BxH,IAAY,EACZwK,OAAuB,KAEvBwD,wBAAA,GACI;EACE,GAAGxG,MAAM,CAAC1G,IAAI,CAAC;EACfmN,KAAK,EAAE;IACL,IAAIzG,MAAM,CAAC1G,IAAI,CAAC,IAAI0G,MAAM,CAAC1G,IAAI,CAAE,CAACmN,KAAK,GAAGzG,MAAM,CAAC1G,IAAI,CAAE,CAACmN,KAAK,GAAG,EAAE,CAAC;IACnE,CAACjO,IAAI,GAAGwK,OAAO,IAAI;EACpB;AACF,IACD,EAAE;ACrBR,IAAA0D,qBAAA,GAAmBhO,KAAQ,IAAMK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAE;ACgBxE,IAAAiO,aAAA,GAAeA,CAAA,KAAoB;EACjC,IAAIC,UAAU,GAAkB,EAAE;EAElC,MAAMV,IAAI,GAAIxN,KAAQ,IAAI;IACxB,KAAK,MAAMmO,QAAQ,IAAID,UAAU,EAAE;MACjCC,QAAQ,CAACX,IAAI,IAAIW,QAAQ,CAACX,IAAI,CAACxN,KAAK,CAAC;;EAEzC,CAAC;EAED,MAAMoO,SAAS,GAAID,QAAqB,IAAkB;IACxDD,UAAU,CAACG,IAAI,CAACF,QAAQ,CAAC;IACzB,OAAO;MACLG,WAAW,EAAEA,CAAA,KAAK;QAChBJ,UAAU,GAAGA,UAAU,CAAC7L,MAAM,CAAEkM,CAAC,IAAKA,CAAC,KAAKJ,QAAQ,CAAC;;KAExD;EACH,CAAC;EAED,MAAMG,WAAW,GAAGA,CAAA,KAAK;IACvBJ,UAAU,GAAG,EAAE;EACjB,CAAC;EAED,OAAO;IACL,IAAIM,SAASA,CAAA;MACX,OAAON,UAAU;KAClB;IACDV,IAAI;IACJY,SAAS;IACTE;GACD;AACH,CAAC;ACzCD,IAAAG,WAAA,GAAgBzO,KAAc,IAC5BE,iBAAiB,CAACF,KAAK,CAAC,IAAI,CAACG,YAAY,CAACH,KAAK,CAAC;ACDpC,SAAU0O,SAASA,CAACC,OAAY,EAAEC,OAAY;EAC1D,IAAIH,WAAW,CAACE,OAAO,CAAC,IAAIF,WAAW,CAACG,OAAO,CAAC,EAAE;IAChD,OAAOD,OAAO,KAAKC,OAAO;;EAG5B,IAAI7O,YAAY,CAAC4O,OAAO,CAAC,IAAI5O,YAAY,CAAC6O,OAAO,CAAC,EAAE;IAClD,OAAOD,OAAO,CAACE,OAAO,EAAE,KAAKD,OAAO,CAACC,OAAO,EAAE;;EAGhD,MAAMC,KAAK,GAAG5I,MAAM,CAACmF,IAAI,CAACsD,OAAO,CAAC;EAClC,MAAMI,KAAK,GAAG7I,MAAM,CAACmF,IAAI,CAACuD,OAAO,CAAC;EAElC,IAAIE,KAAK,CAACpL,MAAM,KAAKqL,KAAK,CAACrL,MAAM,EAAE;IACjC,OAAO,KAAK;;EAGd,KAAK,MAAMvB,GAAG,IAAI2M,KAAK,EAAE;IACvB,MAAME,IAAI,GAAGL,OAAO,CAACxM,GAAG,CAAC;IAEzB,IAAI,CAAC4M,KAAK,CAAC/B,QAAQ,CAAC7K,GAAG,CAAC,EAAE;MACxB,OAAO,KAAK;;IAGd,IAAIA,GAAG,KAAK,KAAK,EAAE;MACjB,MAAM8M,IAAI,GAAGL,OAAO,CAACzM,GAAG,CAAC;MAEzB,IACGpC,YAAY,CAACiP,IAAI,CAAC,IAAIjP,YAAY,CAACkP,IAAI,CAAC,IACxC7O,QAAQ,CAAC4O,IAAI,CAAC,IAAI5O,QAAQ,CAAC6O,IAAI,CAAE,IACjC5O,KAAK,CAACC,OAAO,CAAC0O,IAAI,CAAC,IAAI3O,KAAK,CAACC,OAAO,CAAC2O,IAAI,CAAC,GACvC,CAACP,SAAS,CAACM,IAAI,EAAEC,IAAI,IACrBD,IAAI,KAAKC,IAAI,EACjB;QACA,OAAO,KAAK;;;;EAKlB,OAAO,IAAI;AACb;ACxCA,IAAAC,aAAA,GAAgBlP,KAAc,IAC5BI,QAAQ,CAACJ,KAAK,CAAC,IAAI,CAACkG,MAAM,CAACmF,IAAI,CAACrL,KAAK,CAAC,CAAC0D,MAAM;ACH/C,IAAAyL,WAAA,GAAgBtP,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,MAAM;ACHzB,IAAAsP,UAAA,GAAgBpP,KAAc,IAC5B,OAAOA,KAAK,KAAK,UAAU;ACC7B,IAAAqP,aAAA,GAAgBrP,KAAc,IAA0B;EACtD,IAAI,CAACwB,KAAK,EAAE;IACV,OAAO,KAAK;;EAGd,MAAM8N,KAAK,GAAGtP,KAAK,GAAKA,KAAqB,CAACuP,aAA0B,GAAG,CAAC;EAC5E,OACEvP,KAAK,aACJsP,KAAK,IAAIA,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAAC9N,WAAW,GAAGA,WAAW,CAAC;AAE9E,CAAC;ACVD,IAAA+N,gBAAA,GAAgB5P,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,iBAAiB;ACDpC,IAAA4P,YAAA,GAAgB7P,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,OAAO;ACE1B,IAAA6P,iBAAA,GAAgB7F,GAAiB,IAC/B4F,YAAY,CAAC5F,GAAG,CAAC,IAAIlK,eAAe,CAACkK,GAAG,CAAC;ACF3C,IAAA8F,IAAA,GAAgB9F,GAAQ,IAAKuF,aAAa,CAACvF,GAAG,CAAC,IAAIA,GAAG,CAAC+F,WAAW;ACElE,SAASC,OAAOA,CAACnN,MAAW,EAAEoN,UAA+B;EAC3D,MAAMrM,MAAM,GAAGqM,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACtM,MAAM;EAC7C,IAAIF,KAAK,GAAG,CAAC;EAEb,OAAOA,KAAK,GAAGE,MAAM,EAAE;IACrBf,MAAM,GAAGJ,WAAW,CAACI,MAAM,CAAC,GAAGa,KAAK,EAAE,GAAGb,MAAM,CAACoN,UAAU,CAACvM,KAAK,EAAE,CAAC,CAAC;;EAGtE,OAAOb,MAAM;AACf;AAEA,SAASsN,YAAYA,CAAC9E,GAAc;EAClC,KAAK,MAAMhJ,GAAG,IAAIgJ,GAAG,EAAE;IACrB,IAAIA,GAAG,CAAC5J,cAAc,CAACY,GAAG,CAAC,IAAI,CAACI,WAAW,CAAC4I,GAAG,CAAChJ,GAAG,CAAC,CAAC,EAAE;MACrD,OAAO,KAAK;;;EAGhB,OAAO,IAAI;AACb;AAEc,SAAU+N,KAAKA,CAACvN,MAAW,EAAEC,IAAkC;EAC3E,MAAMuN,KAAK,GAAG9P,KAAK,CAACC,OAAO,CAACsC,IAAI,IAC5BA,IAAA,GACAM,KAAK,CAACN,IAAI,IACR,CAACA,IAAI,IACLQ,YAAY,CAACR,IAAI,CAAC;EAExB,MAAMwN,WAAW,GAAGD,KAAK,CAACzM,MAAM,KAAK,CAAC,GAAGf,MAAM,GAAGmN,OAAO,CAACnN,MAAM,EAAEwN,KAAK,CAAC;EAExE,MAAM3M,KAAK,GAAG2M,KAAK,CAACzM,MAAM,GAAG,CAAC;EAC9B,MAAMvB,GAAG,GAAGgO,KAAK,CAAC3M,KAAK,CAAC;EAExB,IAAI4M,WAAW,EAAE;IACf,OAAOA,WAAW,CAACjO,GAAG,CAAC;;EAGzB,IACEqB,KAAK,KAAK,CAAC,KACTpD,QAAQ,CAACgQ,WAAW,CAAC,IAAIlB,aAAa,CAACkB,WAAW,CAAC,IAClD/P,KAAK,CAACC,OAAO,CAAC8P,WAAW,CAAC,IAAIH,YAAY,CAACG,WAAW,CAAE,CAAC,EAC5D;IACAF,KAAK,CAACvN,MAAM,EAAEwN,KAAK,CAACH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;EAGnC,OAAOrN,MAAM;AACf;ACjDA,IAAA0N,iBAAA,GAAmBxO,IAAO,IAAa;EACrC,KAAK,MAAMM,GAAG,IAAIN,IAAI,EAAE;IACtB,IAAIuN,UAAU,CAACvN,IAAI,CAACM,GAAG,CAAC,CAAC,EAAE;MACzB,OAAO,IAAI;;;EAGf,OAAO,KAAK;AACd,CAAC;ACFD,SAASmO,eAAeA,CAAIzO,IAAO,EAAE0O,MAAA,GAA8B,EAAE;EACnE,MAAMC,iBAAiB,GAAGnQ,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAE7C,IAAIzB,QAAQ,CAACyB,IAAI,CAAC,IAAI2O,iBAAiB,EAAE;IACvC,KAAK,MAAMrO,GAAG,IAAIN,IAAI,EAAE;MACtB,IACExB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACM,GAAG,CAAC,CAAC,IACvB/B,QAAQ,CAACyB,IAAI,CAACM,GAAG,CAAC,CAAC,IAAI,CAACkO,iBAAiB,CAACxO,IAAI,CAACM,GAAG,CAAC,CAAE,EACtD;QACAoO,MAAM,CAACpO,GAAG,CAAC,GAAG9B,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACM,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;QAChDmO,eAAe,CAACzO,IAAI,CAACM,GAAG,CAAC,EAAEoO,MAAM,CAACpO,GAAG,CAAC,CAAC;aAClC,IAAI,CAACjC,iBAAiB,CAAC2B,IAAI,CAACM,GAAG,CAAC,CAAC,EAAE;QACxCoO,MAAM,CAACpO,GAAG,CAAC,GAAG,IAAI;;;;EAKxB,OAAOoO,MAAM;AACf;AAEA,SAASE,+BAA+BA,CACtC5O,IAAO,EACPoG,UAAa,EACbyI,qBAGC;EAED,MAAMF,iBAAiB,GAAGnQ,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAE7C,IAAIzB,QAAQ,CAACyB,IAAI,CAAC,IAAI2O,iBAAiB,EAAE;IACvC,KAAK,MAAMrO,GAAG,IAAIN,IAAI,EAAE;MACtB,IACExB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACM,GAAG,CAAC,CAAC,IACvB/B,QAAQ,CAACyB,IAAI,CAACM,GAAG,CAAC,CAAC,IAAI,CAACkO,iBAAiB,CAACxO,IAAI,CAACM,GAAG,CAAC,CAAE,EACtD;QACA,IACEI,WAAW,CAAC0F,UAAU,CAAC,IACvBwG,WAAW,CAACiC,qBAAqB,CAACvO,GAAG,CAAC,CAAC,EACvC;UACAuO,qBAAqB,CAACvO,GAAG,CAAC,GAAG9B,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACM,GAAG,CAAC,IAChDmO,eAAe,CAACzO,IAAI,CAACM,GAAG,CAAC,EAAE,EAAE,IAC7B;YAAE,GAAGmO,eAAe,CAACzO,IAAI,CAACM,GAAG,CAAC;UAAC,CAAE;eAChC;UACLsO,+BAA+B,CAC7B5O,IAAI,CAACM,GAAG,CAAC,EACTjC,iBAAiB,CAAC+H,UAAU,CAAC,GAAG,EAAE,GAAGA,UAAU,CAAC9F,GAAG,CAAC,EACpDuO,qBAAqB,CAACvO,GAAG,CAAC,CAC3B;;aAEE;QACLuO,qBAAqB,CAACvO,GAAG,CAAC,GAAG,CAACuM,SAAS,CAAC7M,IAAI,CAACM,GAAG,CAAC,EAAE8F,UAAU,CAAC9F,GAAG,CAAC,CAAC;;;;EAKzE,OAAOuO,qBAAqB;AAC9B;AAEA,IAAAC,cAAA,GAAeA,CAAI3K,aAAgB,EAAEiC,UAAa,KAChDwI,+BAA+B,CAC7BzK,aAAa,EACbiC,UAAU,EACVqI,eAAe,CAACrI,UAAU,CAAC,CAC5B;AChEH,MAAM2I,aAAa,GAAwB;EACzC5Q,KAAK,EAAE,KAAK;EACZqH,OAAO,EAAE;CACV;AAED,MAAMwJ,WAAW,GAAG;EAAE7Q,KAAK,EAAE,IAAI;EAAEqH,OAAO,EAAE;AAAI,CAAE;AAElD,IAAAyJ,gBAAA,GAAgBC,OAA4B,IAAyB;EACnE,IAAI1Q,KAAK,CAACC,OAAO,CAACyQ,OAAO,CAAC,EAAE;IAC1B,IAAIA,OAAO,CAACrN,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMgF,MAAM,GAAGqI,OAAA,CACZ1O,MAAM,CAAE2O,MAAM,IAAKA,MAAM,IAAIA,MAAM,CAACtQ,OAAO,IAAI,CAACsQ,MAAM,CAACxK,QAAQ,EAC/D6B,GAAG,CAAE2I,MAAM,IAAKA,MAAM,CAAChR,KAAK,CAAC;MAChC,OAAO;QAAEA,KAAK,EAAE0I,MAAM;QAAErB,OAAO,EAAE,CAAC,CAACqB,MAAM,CAAChF;MAAM,CAAE;;IAGpD,OAAOqN,OAAO,CAAC,CAAC,CAAC,CAACrQ,OAAO,IAAI,CAACqQ,OAAO,CAAC,CAAC,CAAC,CAACvK,QAAA;IACvC;IACEuK,OAAO,CAAC,CAAC,CAAC,CAACE,UAAU,IAAI,CAAC1O,WAAW,CAACwO,OAAO,CAAC,CAAC,CAAC,CAACE,UAAU,CAACjR,KAAK,IAC/DuC,WAAW,CAACwO,OAAO,CAAC,CAAC,CAAC,CAAC/Q,KAAK,CAAC,IAAI+Q,OAAO,CAAC,CAAC,CAAC,CAAC/Q,KAAK,KAAK,KACpD6Q,WAAA,GACA;MAAE7Q,KAAK,EAAE+Q,OAAO,CAAC,CAAC,CAAC,CAAC/Q,KAAK;MAAEqH,OAAO,EAAE;IAAI,IAC1CwJ,WAAA,GACFD,aAAa;;EAGnB,OAAOA,aAAa;AACtB,CAAC;AC9BD,IAAAM,eAAA,GAAeA,CACblR,KAAQ,EACR;EAAEmR,aAAa;EAAEC,WAAW;EAAEC;AAAU,CAAe,KAEvD9O,WAAW,CAACvC,KAAK,IACbA,KAAA,GACAmR,aAAA,GACEnR,KAAK,KAAK,KACRsR,GAAA,GACAtR,KAAA,GACE,CAACA,KAAA,GACDA,KAAA,GACJoR,WAAW,IAAItJ,QAAQ,CAAC9H,KAAK,IAC3B,IAAIC,IAAI,CAACD,KAAK,IACdqR,UAAA,GACEA,UAAU,CAACrR,KAAK,IAChBA,KAAK;ACfjB,MAAMuR,aAAa,GAAqB;EACtClK,OAAO,EAAE,KAAK;EACdrH,KAAK,EAAE;CACR;AAED,IAAAwR,aAAA,GAAgBT,OAA4B,IAC1C1Q,KAAK,CAACC,OAAO,CAACyQ,OAAO,IACjBA,OAAO,CAAC/N,MAAM,CACZ,CAACyO,QAAQ,EAAET,MAAM,KACfA,MAAM,IAAIA,MAAM,CAACtQ,OAAO,IAAI,CAACsQ,MAAM,CAACxK,QAAA,GAChC;EACEa,OAAO,EAAE,IAAI;EACbrH,KAAK,EAAEgR,MAAM,CAAChR;AACf,IACDyR,QAAQ,EACdF,aAAa,IAEfA,aAAa;ACXK,SAAAG,aAAaA,CAACxH,EAAe;EACnD,MAAMJ,GAAG,GAAGI,EAAE,CAACJ,GAAG;EAElB,IAAIqF,WAAW,CAACrF,GAAG,CAAC,EAAE;IACpB,OAAOA,GAAG,CAAC6H,KAAK;;EAGlB,IAAIjC,YAAY,CAAC5F,GAAG,CAAC,EAAE;IACrB,OAAO0H,aAAa,CAACtH,EAAE,CAAC0H,IAAI,CAAC,CAAC5R,KAAK;;EAGrC,IAAIyP,gBAAgB,CAAC3F,GAAG,CAAC,EAAE;IACzB,OAAO,CAAC,GAAGA,GAAG,CAAC+H,eAAe,CAAC,CAACxJ,GAAG,CAAC,CAAC;MAAErI;IAAK,CAAE,KAAKA,KAAK,CAAC;;EAG3D,IAAIJ,eAAU,CAACkK,GAAG,CAAC,EAAE;IACnB,OAAOgH,gBAAgB,CAAC5G,EAAE,CAAC0H,IAAI,CAAC,CAAC5R,KAAK;;EAGxC,OAAOkR,eAAe,CAAC3O,WAAW,CAACuH,GAAG,CAAC9J,KAAK,CAAC,GAAGkK,EAAE,CAACJ,GAAG,CAAC9J,KAAK,GAAG8J,GAAG,CAAC9J,KAAK,EAAEkK,EAAE,CAAC;AAC/E;ACpBA,IAAA4H,kBAAA,GAAeA,CACbC,WAAyD,EACzD9H,OAAkB,EAClB+H,YAA2B,EAC3BC,yBAA+C,KAC7C;EACF,MAAM1B,MAAM,GAA2C,EAAE;EAEzD,KAAK,MAAM3P,IAAI,IAAImR,WAAW,EAAE;IAC9B,MAAM/H,KAAK,GAAUtH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC;IAEvCoJ,KAAK,IAAIzG,GAAG,CAACgN,MAAM,EAAE3P,IAAI,EAAEoJ,KAAK,CAACE,EAAE,CAAC;;EAGtC,OAAO;IACL8H,YAAY;IACZhR,KAAK,EAAE,CAAC,GAAG+Q,WAAW,CAA8B;IACpDxB,MAAM;IACN0B;GACD;AACH,CAAC;AC/BD,IAAAC,OAAA,GAAgBlS,KAAc,IAAsBA,KAAK,YAAYmS,MAAM;ACS3E,IAAAC,YAAA,GACEC,IAAoD,IAEpD9P,WAAW,CAAC8P,IAAI,IACZA,IAAA,GACAH,OAAO,CAACG,IAAI,IACVA,IAAI,CAACC,MAAA,GACLlS,QAAQ,CAACiS,IAAI,IACXH,OAAO,CAACG,IAAI,CAACrS,KAAK,IAChBqS,IAAI,CAACrS,KAAK,CAACsS,MAAA,GACXD,IAAI,CAACrS,KAAA,GACPqS,IAAI;ACjBd,IAAAE,kBAAA,GAAgBC,IAAW,KAA2B;EACpDC,UAAU,EAAE,CAACD,IAAI,IAAIA,IAAI,KAAKrO,eAAe,CAACG,QAAQ;EACtDoO,QAAQ,EAAEF,IAAI,KAAKrO,eAAe,CAACC,MAAM;EACzCuO,UAAU,EAAEH,IAAI,KAAKrO,eAAe,CAACE,QAAQ;EAC7CuO,OAAO,EAAEJ,IAAI,KAAKrO,eAAe,CAACK,GAAG;EACrCqO,SAAS,EAAEL,IAAI,KAAKrO,eAAe,CAACI;AACrC,EAAC;ACLF,MAAMuO,cAAc,GAAG,eAAe;AAEtC,IAAAC,oBAAA,GAAgBC,cAA2B,IACzC,CAAC,CAACA,cAAc,IAChB,CAAC,CAACA,cAAc,CAAChO,QAAQ,IACzB,CAAC,EACEoK,UAAU,CAAC4D,cAAc,CAAChO,QAAQ,CAAC,IAClCgO,cAAc,CAAChO,QAAQ,CAAC3D,WAAW,CAACT,IAAI,KAAKkS,cAAc,IAC5D1S,QAAQ,CAAC4S,cAAc,CAAChO,QAAQ,CAAC,IAChCkB,MAAM,CAACwC,MAAM,CAACsK,cAAc,CAAChO,QAAQ,CAAC,CAACiO,IAAI,CACxCC,gBAA4C,IAC3CA,gBAAgB,CAAC7R,WAAW,CAACT,IAAI,KAAKkS,cAAc,CACtD,CACL;ACfH,IAAAK,aAAA,GAAgBpC,OAAoB,IAClCA,OAAO,CAACpG,KAAK,KACZoG,OAAO,CAAChM,QAAQ,IACfgM,OAAO,CAACpM,GAAG,IACXoM,OAAO,CAACrM,GAAG,IACXqM,OAAO,CAACnM,SAAS,IACjBmM,OAAO,CAAClM,SAAS,IACjBkM,OAAO,CAACjM,OAAO,IACfiM,OAAO,CAAC/L,QAAQ,CAAC;ACRrB,IAAAoO,SAAA,GAAeA,CACbxS,IAAuB,EACvBoH,MAAa,EACbqL,WAAqB,KAErB,CAACA,WAAW,KACXrL,MAAM,CAACO,QAAQ,IACdP,MAAM,CAACG,KAAK,CAAClH,GAAG,CAACL,IAAI,CAAC,IACtB,CAAC,GAAGoH,MAAM,CAACG,KAAK,CAAC,CAAC4E,IAAI,CACnBuG,SAAS,IACR1S,IAAI,CAAC2S,UAAU,CAACD,SAAS,CAAC,IAC1B,QAAQ,CAACnQ,IAAI,CAACvC,IAAI,CAACoP,KAAK,CAACsD,SAAS,CAAC5P,MAAM,CAAC,CAAC,CAC9C,CAAC;ACVN,MAAM8P,qBAAqB,GAAGA,CAC5BjD,MAAiB,EACjB1F,MAAwD,EACxDkH,WAA8D,EAC9D0B,UAAoB,KAClB;EACF,KAAK,MAAMtR,GAAG,IAAI4P,WAAW,IAAI7L,MAAM,CAACmF,IAAI,CAACkF,MAAM,CAAC,EAAE;IACpD,MAAMvG,KAAK,GAAGtH,GAAG,CAAC6N,MAAM,EAAEpO,GAAG,CAAC;IAE9B,IAAI6H,KAAK,EAAE;MACT,MAAM;QAAEE,EAAE;QAAE,GAAGwJ;MAAY,CAAE,GAAG1J,KAAK;MAErC,IAAIE,EAAE,EAAE;QACN,IAAIA,EAAE,CAAC0H,IAAI,IAAI1H,EAAE,CAAC0H,IAAI,CAAC,CAAC,CAAC,IAAI/G,MAAM,CAACX,EAAE,CAAC0H,IAAI,CAAC,CAAC,CAAC,EAAEzP,GAAG,CAAC,IAAI,CAACsR,UAAU,EAAE;UACnE,OAAO,IAAI;eACN,IAAIvJ,EAAE,CAACJ,GAAG,IAAIe,MAAM,CAACX,EAAE,CAACJ,GAAG,EAAEI,EAAE,CAACtJ,IAAI,CAAC,IAAI,CAAC6S,UAAU,EAAE;UAC3D,OAAO,IAAI;eACN;UACL,IAAID,qBAAqB,CAACE,YAAY,EAAE7I,MAAM,CAAC,EAAE;YAC/C;;;aAGC,IAAIzK,QAAQ,CAACsT,YAAY,CAAC,EAAE;QACjC,IAAIF,qBAAqB,CAACE,YAAyB,EAAE7I,MAAM,CAAC,EAAE;UAC5D;;;;;EAKR;AACF,CAAC;AC9BuB,SAAA8I,iBAAiBA,CACvCrM,MAAsB,EACtB2C,OAAoB,EACpBrJ,IAAY;EAKZ,MAAMgJ,KAAK,GAAGlH,GAAG,CAAC4E,MAAM,EAAE1G,IAAI,CAAC;EAE/B,IAAIgJ,KAAK,IAAI1G,KAAK,CAACtC,IAAI,CAAC,EAAE;IACxB,OAAO;MACLgJ,KAAK;MACLhJ;KACD;;EAGH,MAAMI,KAAK,GAAGJ,IAAI,CAACmC,KAAK,CAAC,GAAG,CAAC;EAE7B,OAAO/B,KAAK,CAAC0C,MAAM,EAAE;IACnB,MAAM4E,SAAS,GAAGtH,KAAK,CAAC4S,IAAI,CAAC,GAAG,CAAC;IACjC,MAAM5J,KAAK,GAAGtH,GAAG,CAACuH,OAAO,EAAE3B,SAAS,CAAC;IACrC,MAAMuL,UAAU,GAAGnR,GAAG,CAAC4E,MAAM,EAAEgB,SAAS,CAAC;IAEzC,IAAI0B,KAAK,IAAI,CAAC3J,KAAK,CAACC,OAAO,CAAC0J,KAAK,CAAC,IAAIpJ,IAAI,KAAK0H,SAAS,EAAE;MACxD,OAAO;QAAE1H;MAAI,CAAE;;IAGjB,IAAIiT,UAAU,IAAIA,UAAU,CAAC/T,IAAI,EAAE;MACjC,OAAO;QACLc,IAAI,EAAE0H,SAAS;QACfsB,KAAK,EAAEiK;OACR;;IAGH7S,KAAK,CAAC8S,GAAG,EAAE;;EAGb,OAAO;IACLlT;GACD;AACH;ACpCA,IAAAmT,qBAAA,GAAeA,CACbC,aAGC,EACD3N,eAAkB,EAClBK,eAA2D,EAC3DX,MAAgB,KACd;EACFW,eAAe,CAACsN,aAAa,CAAC;EAC9B,MAAM;IAAEpT,IAAI;IAAE,GAAGgF;EAAS,CAAE,GAAGoO,aAAa;EAE5C,OACE9E,aAAa,CAACtJ,SAAS,CAAC,IACxBM,MAAM,CAACmF,IAAI,CAACzF,SAAS,CAAC,CAAClC,MAAM,IAAIwC,MAAM,CAACmF,IAAI,CAAChF,eAAe,CAAC,CAAC3C,MAAM,IACpEwC,MAAM,CAACmF,IAAI,CAACzF,SAAS,CAAC,CAACqN,IAAI,CACxB9Q,GAAG,IACFkE,eAAe,CAAClE,GAA0B,CAAC,MAC1C,CAAC4D,MAAM,IAAI5B,eAAe,CAACK,GAAG,CAAC,CACnC;AAEL,CAAC;AC5BD,IAAAyP,qBAAA,GAAeA,CACbrT,IAAQ,EACRsT,UAAmB,EACnBzN,KAAe,KAEf,CAAC7F,IAAI,IACL,CAACsT,UAAU,IACXtT,IAAI,KAAKsT,UAAU,IACnBlG,qBAAqB,CAACpN,IAAI,CAAC,CAACmM,IAAI,CAC7BoH,WAAW,IACVA,WAAW,KACV1N,KAAA,GACG0N,WAAW,KAAKD,UAAA,GAChBC,WAAW,CAACZ,UAAU,CAACW,UAAU,CAAC,IAClCA,UAAU,CAACX,UAAU,CAACY,WAAW,CAAC,CAAC,CAC1C;ACfH,IAAAC,cAAA,GAAeA,CACbf,WAAoB,EACpB1J,SAAkB,EAClB0K,WAAoB,EACpBC,cAGC,EACD9B,IAAkC,KAChC;EACF,IAAIA,IAAI,CAACI,OAAO,EAAE;IAChB,OAAO,KAAK;SACP,IAAI,CAACyB,WAAW,IAAI7B,IAAI,CAACK,SAAS,EAAE;IACzC,OAAO,EAAElJ,SAAS,IAAI0J,WAAW,CAAC;SAC7B,IAAIgB,WAAW,GAAGC,cAAc,CAAC5B,QAAQ,GAAGF,IAAI,CAACE,QAAQ,EAAE;IAChE,OAAO,CAACW,WAAW;SACd,IAAIgB,WAAW,GAAGC,cAAc,CAAC3B,UAAU,GAAGH,IAAI,CAACG,UAAU,EAAE;IACpE,OAAOU,WAAW;;EAEpB,OAAO,IAAI;AACb,CAAC;AClBD,IAAAkB,eAAA,GAAeA,CAAIzK,GAAM,EAAElJ,IAAY,KACrC,CAACwB,OAAO,CAACM,GAAG,CAACoH,GAAG,EAAElJ,IAAI,CAAC,CAAC,CAAC8C,MAAM,IAAIwM,KAAK,CAACpG,GAAG,EAAElJ,IAAI,CAAC;ACKrD,IAAA4T,yBAAA,GAAeA,CACblN,MAAsB,EACtBsC,KAA0C,EAC1ChJ,IAAuB,KACL;EAClB,MAAM6T,gBAAgB,GAAGzG,qBAAqB,CAACtL,GAAG,CAAC4E,MAAM,EAAE1G,IAAI,CAAC,CAAC;EACjE2C,GAAG,CAACkR,gBAAgB,EAAE,MAAM,EAAE7K,KAAK,CAAChJ,IAAI,CAAC,CAAC;EAC1C2C,GAAG,CAAC+D,MAAM,EAAE1G,IAAI,EAAE6T,gBAAgB,CAAC;EACnC,OAAOnN,MAAM;AACf,CAAC;AChBD,IAAAoN,SAAA,GAAgB1U,KAAc,IAAuB8H,QAAQ,CAAC9H,KAAK,CAAC;ACCtD,SAAU2U,gBAAgBA,CACtC7R,MAAsB,EACtBgH,GAAQ,EACRhK,IAAI,GAAG,UAAU;EAEjB,IACE4U,SAAS,CAAC5R,MAAM,CAAC,IAChBzC,KAAK,CAACC,OAAO,CAACwC,MAAM,CAAC,IAAIA,MAAM,CAAC8R,KAAK,CAACF,SAAS,CAAE,IACjDzR,SAAS,CAACH,MAAM,CAAC,IAAI,CAACA,MAAO,EAC9B;IACA,OAAO;MACLhD,IAAI;MACJwK,OAAO,EAAEoK,SAAS,CAAC5R,MAAM,CAAC,GAAGA,MAAM,GAAG,EAAE;MACxCgH;KACD;;AAEL;AChBA,IAAA+K,kBAAA,GAAgBC,cAA+B,IAC7C1U,QAAQ,CAAC0U,cAAc,CAAC,IAAI,CAAC5C,OAAO,CAAC4C,cAAc,IAC/CA,cAAA,GACA;EACE9U,KAAK,EAAE8U,cAAc;EACrBxK,OAAO,EAAE;CACV;ACuBP,IAAAyK,aAAA,GAAe,MAAAA,CACb/K,KAAY,EACZgL,kBAAmC,EACnC/M,UAAa,EACb6F,wBAAiC,EACjCmE,yBAAmC,EACnCgD,YAAsB,KACU;EAChC,MAAM;IACJnL,GAAG;IACH8H,IAAI;IACJ7M,QAAQ;IACRH,SAAS;IACTC,SAAS;IACTF,GAAG;IACHD,GAAG;IACHI,OAAO;IACPE,QAAQ;IACRpE,IAAI;IACJuQ,aAAa;IACbxG;EAAK,CACN,GAAGX,KAAK,CAACE,EAAE;EACZ,MAAMgL,UAAU,GAAqBxS,GAAG,CAACuF,UAAU,EAAErH,IAAI,CAAC;EAC1D,IAAI,CAAC+J,KAAK,IAAIqK,kBAAkB,CAAC/T,GAAG,CAACL,IAAI,CAAC,EAAE;IAC1C,OAAO,EAAE;;EAEX,MAAMuU,QAAQ,GAAqBvD,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAI9H,GAAwB;EAC7E,MAAMO,iBAAiB,GAAIC,OAA0B,IAAI;IACvD,IAAI2H,yBAAyB,IAAIkD,QAAQ,CAAC5K,cAAc,EAAE;MACxD4K,QAAQ,CAAC9K,iBAAiB,CAACpH,SAAS,CAACqH,OAAO,CAAC,GAAG,EAAE,GAAGA,OAAO,IAAI,EAAE,CAAC;MACnE6K,QAAQ,CAAC5K,cAAc,EAAE;;EAE7B,CAAC;EACD,MAAMX,KAAK,GAAwB,EAAE;EACrC,MAAMwL,OAAO,GAAG1F,YAAY,CAAC5F,GAAG,CAAC;EACjC,MAAMuL,UAAU,GAAGzV,eAAe,CAACkK,GAAG,CAAC;EACvC,MAAM6F,iBAAiB,GAAGyF,OAAO,IAAIC,UAAU;EAC/C,MAAMC,OAAO,GACV,CAACnE,aAAa,IAAIhC,WAAW,CAACrF,GAAG,CAAC,KACjCvH,WAAW,CAACuH,GAAG,CAAC9J,KAAK,CAAC,IACtBuC,WAAW,CAAC2S,UAAU,CAAC,IACxB7F,aAAa,CAACvF,GAAG,CAAC,IAAIA,GAAG,CAAC9J,KAAK,KAAK,EAAG,IACxCkV,UAAU,KAAK,EAAE,IAChB7U,KAAK,CAACC,OAAO,CAAC4U,UAAU,CAAC,IAAI,CAACA,UAAU,CAACxR,MAAO;EACnD,MAAM6R,iBAAiB,GAAG1H,YAAY,CAAC2H,IAAI,CACzC,IAAI,EACJ5U,IAAI,EACJkN,wBAAwB,EACxBlE,KAAK,CACN;EACD,MAAM6L,gBAAgB,GAAGA,CACvBC,SAAkB,EAClBC,gBAAyB,EACzBC,gBAAyB,EACzBC,OAAA,GAAmBpR,sBAAsB,CAACG,SAAS,EACnDkR,OAAA,GAAmBrR,sBAAsB,CAACI,SAAS,KACjD;IACF,MAAMyF,OAAO,GAAGoL,SAAS,GAAGC,gBAAgB,GAAGC,gBAAgB;IAC/DhM,KAAK,CAAChJ,IAAI,CAAC,GAAG;MACZd,IAAI,EAAE4V,SAAS,GAAGG,OAAO,GAAGC,OAAO;MACnCxL,OAAO;MACPR,GAAG;MACH,GAAGyL,iBAAiB,CAACG,SAAS,GAAGG,OAAO,GAAGC,OAAO,EAAExL,OAAO;KAC5D;EACH,CAAC;EAED,IACE2K,YAAA,GACI,CAAC5U,KAAK,CAACC,OAAO,CAAC4U,UAAU,CAAC,IAAI,CAACA,UAAU,CAACxR,MAAA,GAC1CqB,QAAQ,KACN,CAAC4K,iBAAiB,KAAK2F,OAAO,IAAIpV,iBAAiB,CAACgV,UAAU,CAAC,CAAC,IAC/DjS,SAAS,CAACiS,UAAU,CAAC,IAAI,CAACA,UAAW,IACrCG,UAAU,IAAI,CAACvE,gBAAgB,CAACc,IAAI,CAAC,CAACvK,OAAQ,IAC9C+N,OAAO,IAAI,CAAC5D,aAAa,CAACI,IAAI,CAAC,CAACvK,OAAQ,CAAC,EAChD;IACA,MAAM;MAAErH,KAAK;MAAEsK;IAAO,CAAE,GAAGoK,SAAS,CAAC3P,QAAQ,IACzC;MAAE/E,KAAK,EAAE,CAAC,CAAC+E,QAAQ;MAAEuF,OAAO,EAAEvF;IAAQ,IACtC8P,kBAAkB,CAAC9P,QAAQ,CAAC;IAEhC,IAAI/E,KAAK,EAAE;MACT4J,KAAK,CAAChJ,IAAI,CAAC,GAAG;QACZd,IAAI,EAAE2E,sBAAsB,CAACM,QAAQ;QACrCuF,OAAO;QACPR,GAAG,EAAEqL,QAAQ;QACb,GAAGI,iBAAiB,CAAC9Q,sBAAsB,CAACM,QAAQ,EAAEuF,OAAO;OAC9D;MACD,IAAI,CAACwD,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACC,OAAO,CAAC;QAC1B,OAAOV,KAAK;;;;EAKlB,IAAI,CAAC0L,OAAO,KAAK,CAACpV,iBAAiB,CAACyE,GAAG,CAAC,IAAI,CAACzE,iBAAiB,CAACwE,GAAG,CAAC,CAAC,EAAE;IACpE,IAAIgR,SAAS;IACb,IAAIK,SAAS;IACb,MAAMC,SAAS,GAAGnB,kBAAkB,CAACnQ,GAAG,CAAC;IACzC,MAAMuR,SAAS,GAAGpB,kBAAkB,CAAClQ,GAAG,CAAC;IAEzC,IAAI,CAACzE,iBAAiB,CAACgV,UAAU,CAAC,IAAI,CAACpR,KAAK,CAACoR,UAAoB,CAAC,EAAE;MAClE,MAAMgB,WAAW,GACdpM,GAAwB,CAACqH,aAAa,KACtC+D,UAAU,GAAG,CAACA,UAAU,GAAGA,UAAU,CAAC;MACzC,IAAI,CAAChV,iBAAiB,CAAC8V,SAAS,CAAChW,KAAK,CAAC,EAAE;QACvC0V,SAAS,GAAGQ,WAAW,GAAGF,SAAS,CAAChW,KAAK;;MAE3C,IAAI,CAACE,iBAAiB,CAAC+V,SAAS,CAACjW,KAAK,CAAC,EAAE;QACvC+V,SAAS,GAAGG,WAAW,GAAGD,SAAS,CAACjW,KAAK;;WAEtC;MACL,MAAMmW,SAAS,GACZrM,GAAwB,CAACsH,WAAW,IAAI,IAAInR,IAAI,CAACiV,UAAoB,CAAC;MACzE,MAAMkB,iBAAiB,GAAIC,IAAa,IACtC,IAAIpW,IAAI,CAAC,IAAIA,IAAI,EAAE,CAACqW,YAAY,EAAE,GAAG,GAAG,GAAGD,IAAI,CAAC;MAClD,MAAME,MAAM,GAAGzM,GAAG,CAAChK,IAAI,IAAI,MAAM;MACjC,MAAM0W,MAAM,GAAG1M,GAAG,CAAChK,IAAI,IAAI,MAAM;MAEjC,IAAIgI,QAAQ,CAACkO,SAAS,CAAChW,KAAK,CAAC,IAAIkV,UAAU,EAAE;QAC3CQ,SAAS,GAAGa,MAAA,GACRH,iBAAiB,CAAClB,UAAU,CAAC,GAAGkB,iBAAiB,CAACJ,SAAS,CAAChW,KAAK,IACjEwW,MAAA,GACEtB,UAAU,GAAGc,SAAS,CAAChW,KAAA,GACvBmW,SAAS,GAAG,IAAIlW,IAAI,CAAC+V,SAAS,CAAChW,KAAK,CAAC;;MAG7C,IAAI8H,QAAQ,CAACmO,SAAS,CAACjW,KAAK,CAAC,IAAIkV,UAAU,EAAE;QAC3Ca,SAAS,GAAGQ,MAAA,GACRH,iBAAiB,CAAClB,UAAU,CAAC,GAAGkB,iBAAiB,CAACH,SAAS,CAACjW,KAAK,IACjEwW,MAAA,GACEtB,UAAU,GAAGe,SAAS,CAACjW,KAAA,GACvBmW,SAAS,GAAG,IAAIlW,IAAI,CAACgW,SAAS,CAACjW,KAAK,CAAC;;;IAI/C,IAAI0V,SAAS,IAAIK,SAAS,EAAE;MAC1BN,gBAAgB,CACd,CAAC,CAACC,SAAS,EACXM,SAAS,CAAC1L,OAAO,EACjB2L,SAAS,CAAC3L,OAAO,EACjB7F,sBAAsB,CAACC,GAAG,EAC1BD,sBAAsB,CAACE,GAAG,CAC3B;MACD,IAAI,CAACmJ,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACT,KAAK,CAAChJ,IAAI,CAAE,CAAC0J,OAAO,CAAC;QACvC,OAAOV,KAAK;;;;EAKlB,IACE,CAAChF,SAAS,IAAIC,SAAS,KACvB,CAACyQ,OAAO,KACPxN,QAAQ,CAACoN,UAAU,CAAC,IAAKD,YAAY,IAAI5U,KAAK,CAACC,OAAO,CAAC4U,UAAU,CAAE,CAAC,EACrE;IACA,MAAMuB,eAAe,GAAG5B,kBAAkB,CAACjQ,SAAS,CAAC;IACrD,MAAM8R,eAAe,GAAG7B,kBAAkB,CAAChQ,SAAS,CAAC;IACrD,MAAM6Q,SAAS,GACb,CAACxV,iBAAiB,CAACuW,eAAe,CAACzW,KAAK,CAAC,IACzCkV,UAAU,CAACxR,MAAM,GAAG,CAAC+S,eAAe,CAACzW,KAAK;IAC5C,MAAM+V,SAAS,GACb,CAAC7V,iBAAiB,CAACwW,eAAe,CAAC1W,KAAK,CAAC,IACzCkV,UAAU,CAACxR,MAAM,GAAG,CAACgT,eAAe,CAAC1W,KAAK;IAE5C,IAAI0V,SAAS,IAAIK,SAAS,EAAE;MAC1BN,gBAAgB,CACdC,SAAS,EACTe,eAAe,CAACnM,OAAO,EACvBoM,eAAe,CAACpM,OAAO,CACxB;MACD,IAAI,CAACwD,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACT,KAAK,CAAChJ,IAAI,CAAE,CAAC0J,OAAO,CAAC;QACvC,OAAOV,KAAK;;;;EAKlB,IAAI9E,OAAO,IAAI,CAACwQ,OAAO,IAAIxN,QAAQ,CAACoN,UAAU,CAAC,EAAE;IAC/C,MAAM;MAAElV,KAAK,EAAE2W,YAAY;MAAErM;IAAO,CAAE,GAAGuK,kBAAkB,CAAC/P,OAAO,CAAC;IAEpE,IAAIoN,OAAO,CAACyE,YAAY,CAAC,IAAI,CAACzB,UAAU,CAAC0B,KAAK,CAACD,YAAY,CAAC,EAAE;MAC5D/M,KAAK,CAAChJ,IAAI,CAAC,GAAG;QACZd,IAAI,EAAE2E,sBAAsB,CAACK,OAAO;QACpCwF,OAAO;QACPR,GAAG;QACH,GAAGyL,iBAAiB,CAAC9Q,sBAAsB,CAACK,OAAO,EAAEwF,OAAO;OAC7D;MACD,IAAI,CAACwD,wBAAwB,EAAE;QAC7BzD,iBAAiB,CAACC,OAAO,CAAC;QAC1B,OAAOV,KAAK;;;;EAKlB,IAAI5E,QAAQ,EAAE;IACZ,IAAIoK,UAAU,CAACpK,QAAQ,CAAC,EAAE;MACxB,MAAMlC,MAAM,GAAG,MAAMkC,QAAQ,CAACkQ,UAAU,EAAEjN,UAAU,CAAC;MACrD,MAAM4O,aAAa,GAAGlC,gBAAgB,CAAC7R,MAAM,EAAEqS,QAAQ,CAAC;MAExD,IAAI0B,aAAa,EAAE;QACjBjN,KAAK,CAAChJ,IAAI,CAAC,GAAG;UACZ,GAAGiW,aAAa;UAChB,GAAGtB,iBAAiB,CAClB9Q,sBAAsB,CAACO,QAAQ,EAC/B6R,aAAa,CAACvM,OAAO;SAExB;QACD,IAAI,CAACwD,wBAAwB,EAAE;UAC7BzD,iBAAiB,CAACwM,aAAa,CAACvM,OAAO,CAAC;UACxC,OAAOV,KAAK;;;WAGX,IAAIxJ,QAAQ,CAAC4E,QAAQ,CAAC,EAAE;MAC7B,IAAI8R,gBAAgB,GAAG,EAAgB;MAEvC,KAAK,MAAM3U,GAAG,IAAI6C,QAAQ,EAAE;QAC1B,IAAI,CAACkK,aAAa,CAAC4H,gBAAgB,CAAC,IAAI,CAAChJ,wBAAwB,EAAE;UACjE;;QAGF,MAAM+I,aAAa,GAAGlC,gBAAgB,CACpC,MAAM3P,QAAQ,CAAC7C,GAAG,CAAC,CAAC+S,UAAU,EAAEjN,UAAU,CAAC,EAC3CkN,QAAQ,EACRhT,GAAG,CACJ;QAED,IAAI0U,aAAa,EAAE;UACjBC,gBAAgB,GAAG;YACjB,GAAGD,aAAa;YAChB,GAAGtB,iBAAiB,CAACpT,GAAG,EAAE0U,aAAa,CAACvM,OAAO;WAChD;UAEDD,iBAAiB,CAACwM,aAAa,CAACvM,OAAO,CAAC;UAExC,IAAIwD,wBAAwB,EAAE;YAC5BlE,KAAK,CAAChJ,IAAI,CAAC,GAAGkW,gBAAgB;;;;MAKpC,IAAI,CAAC5H,aAAa,CAAC4H,gBAAgB,CAAC,EAAE;QACpClN,KAAK,CAAChJ,IAAI,CAAC,GAAG;UACZkJ,GAAG,EAAEqL,QAAQ;UACb,GAAG2B;SACJ;QACD,IAAI,CAAChJ,wBAAwB,EAAE;UAC7B,OAAOlE,KAAK;;;;;EAMpBS,iBAAiB,CAAC,IAAI,CAAC;EACvB,OAAOT,KAAK;AACd,CAAC;ACpMD,MAAMmN,cAAc,GAAG;EACrBvE,IAAI,EAAErO,eAAe,CAACG,QAAQ;EAC9BgQ,cAAc,EAAEnQ,eAAe,CAACE,QAAQ;EACxC2S,gBAAgB,EAAE;CACV;AAEM,SAAAC,iBAAiBA,CAK/B1R,KAAA,GAAkE,EAAE;EAUpE,IAAIkF,QAAQ,GAAG;IACb,GAAGsM,cAAc;IACjB,GAAGxR;GACJ;EACD,IAAIqB,UAAU,GAA4B;IACxCsQ,WAAW,EAAE,CAAC;IACdnQ,OAAO,EAAE,KAAK;IACdC,SAAS,EAAEoI,UAAU,CAAC3E,QAAQ,CAACzE,aAAa,CAAC;IAC7CoB,YAAY,EAAE,KAAK;IACnBiN,WAAW,EAAE,KAAK;IAClB8C,YAAY,EAAE,KAAK;IACnB1J,kBAAkB,EAAE,KAAK;IACzBpG,OAAO,EAAE,KAAK;IACdH,aAAa,EAAE,EAAE;IACjBD,WAAW,EAAE,EAAE;IACfE,gBAAgB,EAAE,EAAE;IACpBG,MAAM,EAAEmD,QAAQ,CAACnD,MAAM,IAAI,EAAE;IAC7Bd,QAAQ,EAAEiE,QAAQ,CAACjE,QAAQ,IAAI;GAChC;EACD,MAAMyD,OAAO,GAAc,EAAE;EAC7B,IAAIhE,cAAc,GAChB7F,QAAQ,CAACqK,QAAQ,CAACzE,aAAa,CAAC,IAAI5F,QAAQ,CAACqK,QAAQ,CAAC/B,MAAM,IACxD9G,WAAW,CAAC6I,QAAQ,CAAC/B,MAAM,IAAI+B,QAAQ,CAACzE,aAAa,CAAC,IAAI,KAC1D,EAAE;EACR,IAAI4C,WAAW,GAAG6B,QAAQ,CAACzB,gBAAA,GACtB,KACApH,WAAW,CAACqE,cAAc,CAAkB;EACjD,IAAI2E,MAAM,GAAG;IACXC,MAAM,EAAE,KAAK;IACbF,KAAK,EAAE,KAAK;IACZxC,KAAK,EAAE;GACR;EACD,IAAIH,MAAM,GAAU;IAClB2C,KAAK,EAAE,IAAI1I,GAAG,EAAE;IAChBuE,QAAQ,EAAE,IAAIvE,GAAG,EAAE;IACnBmV,OAAO,EAAE,IAAInV,GAAG,EAAE;IAClBiH,KAAK,EAAE,IAAIjH,GAAG,EAAE;IAChBkG,KAAK,EAAE,IAAIlG,GAAG;GACf;EACD,IAAIoV,kBAAwC;EAC5C,IAAIC,KAAK,GAAG,CAAC;EACb,MAAMjR,eAAe,GAAkB;IACrCU,OAAO,EAAE,KAAK;IACdE,WAAW,EAAE,KAAK;IAClBE,gBAAgB,EAAE,KAAK;IACvBD,aAAa,EAAE,KAAK;IACpBE,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;GACT;EACD,IAAIiQ,wBAAwB,GAAG;IAC7B,GAAGlR;GACJ;EACD,MAAMiH,SAAS,GAA2B;IACxCpE,KAAK,EAAE+E,aAAa,EAAE;IACtBV,KAAK,EAAEU,aAAa;GACrB;EACD,MAAMuJ,0BAA0B,GAAGjF,kBAAkB,CAAC9H,QAAQ,CAAC+H,IAAI,CAAC;EACpE,MAAMiF,yBAAyB,GAAGlF,kBAAkB,CAAC9H,QAAQ,CAAC6J,cAAc,CAAC;EAC7E,MAAMoD,gCAAgC,GACpCjN,QAAQ,CAACuH,YAAY,KAAK7N,eAAe,CAACK,GAAG;EAE/C,MAAMmT,QAAQ,GACShQ,QAAW,IAC/BiQ,IAAY,IAAI;IACfC,YAAY,CAACP,KAAK,CAAC;IACnBA,KAAK,GAAGQ,UAAU,CAACnQ,QAAQ,EAAEiQ,IAAI,CAAC;EACpC,CAAC;EAEH,MAAMhQ,SAAS,GAAG,MAAOmQ,iBAA2B,IAAI;IACtD,IACE,CAACtN,QAAQ,CAACjE,QAAQ,KACjBH,eAAe,CAACgB,OAAO,IACtBkQ,wBAAwB,CAAClQ,OAAO,IAChC0Q,iBAAiB,CAAC,EACpB;MACA,MAAM1Q,OAAO,GAAGoD,QAAQ,CAACuN,QAAA,GACrB9I,aAAa,CAAC,CAAC,MAAM+I,UAAU,EAAE,EAAE3Q,MAAM,IACzC,MAAM4Q,wBAAwB,CAACjO,OAAO,EAAE,IAAI,CAAC;MAEjD,IAAI5C,OAAO,KAAKT,UAAU,CAACS,OAAO,EAAE;QAClCiG,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UACnBnG;QACD,EAAC;;;EAGR,CAAC;EAED,MAAM8Q,mBAAmB,GAAGA,CAACnX,KAAgB,EAAEoG,YAAsB,KAAI;IACvE,IACE,CAACqD,QAAQ,CAACjE,QAAQ,KACjBH,eAAe,CAACe,YAAY,IAC3Bf,eAAe,CAACc,gBAAgB,IAChCoQ,wBAAwB,CAACnQ,YAAY,IACrCmQ,wBAAwB,CAACpQ,gBAAgB,CAAC,EAC5C;MACA,CAACnG,KAAK,IAAIX,KAAK,CAAC+X,IAAI,CAACpQ,MAAM,CAAC2C,KAAK,CAAC,EAAE0N,OAAO,CAAEzX,IAAI,IAAI;QACnD,IAAIA,IAAI,EAAE;UACRwG,YAAA,GACI7D,GAAG,CAACqD,UAAU,CAACO,gBAAgB,EAAEvG,IAAI,EAAEwG,YAAY,IACnD8I,KAAK,CAACtJ,UAAU,CAACO,gBAAgB,EAAEvG,IAAI,CAAC;;MAEhD,CAAC,CAAC;MAEF0M,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnBrG,gBAAgB,EAAEP,UAAU,CAACO,gBAAgB;QAC7CC,YAAY,EAAE,CAAC8H,aAAa,CAACtI,UAAU,CAACO,gBAAgB;MACzD,EAAC;;EAEN,CAAC;EAED,MAAMmR,cAAc,GAA0BA,CAC5C1X,IAAI,EACJ8H,MAAM,GAAG,EAAE,EACXkD,MAAM,EACN2M,IAAI,EACJC,eAAe,GAAG,IAAI,EACtBC,0BAA0B,GAAG,IAAI,KAC/B;IACF,IAAIF,IAAI,IAAI3M,MAAM,IAAI,CAACnB,QAAQ,CAACjE,QAAQ,EAAE;MACxCoE,MAAM,CAACC,MAAM,GAAG,IAAI;MACpB,IAAI4N,0BAA0B,IAAIpY,KAAK,CAACC,OAAO,CAACoC,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC,CAAC,EAAE;QACnE,MAAM8X,WAAW,GAAG9M,MAAM,CAAClJ,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC,EAAE2X,IAAI,CAACI,IAAI,EAAEJ,IAAI,CAACK,IAAI,CAAC;QACpEJ,eAAe,IAAIjV,GAAG,CAAC0G,OAAO,EAAErJ,IAAI,EAAE8X,WAAW,CAAC;;MAGpD,IACED,0BAA0B,IAC1BpY,KAAK,CAACC,OAAO,CAACoC,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAE1G,IAAI,CAAC,CAAC,EAC3C;QACA,MAAM0G,MAAM,GAAGsE,MAAM,CACnBlJ,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAE1G,IAAI,CAAC,EAC5B2X,IAAI,CAACI,IAAI,EACTJ,IAAI,CAACK,IAAI,CACV;QACDJ,eAAe,IAAIjV,GAAG,CAACqD,UAAU,CAACU,MAAM,EAAE1G,IAAI,EAAE0G,MAAM,CAAC;QACvDiN,eAAe,CAAC3N,UAAU,CAACU,MAAM,EAAE1G,IAAI,CAAC;;MAG1C,IACE,CAACyF,eAAe,CAACa,aAAa,IAC5BqQ,wBAAwB,CAACrQ,aAAa,KACxCuR,0BAA0B,IAC1BpY,KAAK,CAACC,OAAO,CAACoC,GAAG,CAACkE,UAAU,CAACM,aAAa,EAAEtG,IAAI,CAAC,CAAC,EAClD;QACA,MAAMsG,aAAa,GAAG0E,MAAM,CAC1BlJ,GAAG,CAACkE,UAAU,CAACM,aAAa,EAAEtG,IAAI,CAAC,EACnC2X,IAAI,CAACI,IAAI,EACTJ,IAAI,CAACK,IAAI,CACV;QACDJ,eAAe,IAAIjV,GAAG,CAACqD,UAAU,CAACM,aAAa,EAAEtG,IAAI,EAAEsG,aAAa,CAAC;;MAGvE,IAAIb,eAAe,CAACY,WAAW,IAAIsQ,wBAAwB,CAACtQ,WAAW,EAAE;QACvEL,UAAU,CAACK,WAAW,GAAG0J,cAAc,CAAC1K,cAAc,EAAE2C,WAAW,CAAC;;MAGtE0E,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnB5M,IAAI;QACJmG,OAAO,EAAE8R,SAAS,CAACjY,IAAI,EAAE8H,MAAM,CAAC;QAChCzB,WAAW,EAAEL,UAAU,CAACK,WAAW;QACnCK,MAAM,EAAEV,UAAU,CAACU,MAAM;QACzBD,OAAO,EAAET,UAAU,CAACS;MACrB,EAAC;WACG;MACL9D,GAAG,CAACqF,WAAW,EAAEhI,IAAI,EAAE8H,MAAM,CAAC;;EAElC,CAAC;EAED,MAAMoQ,YAAY,GAAGA,CAAClY,IAAuB,EAAEgJ,KAAiB,KAAI;IAClErG,GAAG,CAACqD,UAAU,CAACU,MAAM,EAAE1G,IAAI,EAAEgJ,KAAK,CAAC;IACnC0D,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBlG,MAAM,EAAEV,UAAU,CAACU;IACpB,EAAC;EACJ,CAAC;EAED,MAAMyR,UAAU,GAAIzR,MAAiC,IAAI;IACvDV,UAAU,CAACU,MAAM,GAAGA,MAAM;IAC1BgG,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBlG,MAAM,EAAEV,UAAU,CAACU,MAAM;MACzBD,OAAO,EAAE;IACV,EAAC;EACJ,CAAC;EAED,MAAM2R,mBAAmB,GAAGA,CAC1BpY,IAAuB,EACvBqY,oBAA6B,EAC7BjZ,KAAe,EACf8J,GAAS,KACP;IACF,MAAME,KAAK,GAAUtH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC;IAEvC,IAAIoJ,KAAK,EAAE;MACT,MAAMnH,YAAY,GAAGH,GAAG,CACtBkG,WAAW,EACXhI,IAAI,EACJ2B,WAAW,CAACvC,KAAK,CAAC,GAAG0C,GAAG,CAACuD,cAAc,EAAErF,IAAI,CAAC,GAAGZ,KAAK,CACvD;MAEDuC,WAAW,CAACM,YAAY,CAAC,IACxBiH,GAAG,IAAKA,GAAwB,CAACoP,cAAe,IACjDD,oBAAA,GACI1V,GAAG,CACDqF,WAAW,EACXhI,IAAI,EACJqY,oBAAoB,GAAGpW,YAAY,GAAG6O,aAAa,CAAC1H,KAAK,CAACE,EAAE,CAAC,IAE/DiP,aAAa,CAACvY,IAAI,EAAEiC,YAAY,CAAC;MAErC+H,MAAM,CAACD,KAAK,IAAI/C,SAAS,EAAE;;EAE/B,CAAC;EAED,MAAMwR,mBAAmB,GAAGA,CAC1BxY,IAAuB,EACvByY,UAAmB,EACnBhG,WAAqB,EACrBiG,WAAqB,EACrBC,YAAsB,KAGpB;IACF,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,eAAe,GAAG,KAAK;IAC3B,MAAMrO,MAAM,GAAwD;MAClExK;KACD;IAED,IAAI,CAAC6J,QAAQ,CAACjE,QAAQ,EAAE;MACtB,IAAI,CAAC6M,WAAW,IAAIiG,WAAW,EAAE;QAC/B,IAAIjT,eAAe,CAACU,OAAO,IAAIwQ,wBAAwB,CAACxQ,OAAO,EAAE;UAC/D0S,eAAe,GAAG7S,UAAU,CAACG,OAAO;UACpCH,UAAU,CAACG,OAAO,GAAGqE,MAAM,CAACrE,OAAO,GAAG8R,SAAS,EAAE;UACjDW,iBAAiB,GAAGC,eAAe,KAAKrO,MAAM,CAACrE,OAAO;;QAGxD,MAAM2S,sBAAsB,GAAGhL,SAAS,CACtChM,GAAG,CAACuD,cAAc,EAAErF,IAAI,CAAC,EACzByY,UAAU,CACX;QAEDI,eAAe,GAAG,CAAC,CAAC/W,GAAG,CAACkE,UAAU,CAACK,WAAW,EAAErG,IAAI,CAAC;QACrD8Y,sBAAA,GACIxJ,KAAK,CAACtJ,UAAU,CAACK,WAAW,EAAErG,IAAI,IAClC2C,GAAG,CAACqD,UAAU,CAACK,WAAW,EAAErG,IAAI,EAAE,IAAI,CAAC;QAC3CwK,MAAM,CAACnE,WAAW,GAAGL,UAAU,CAACK,WAAW;QAC3CuS,iBAAiB,GACfA,iBAAiB,IAChB,CAACnT,eAAe,CAACY,WAAW,IAC3BsQ,wBAAwB,CAACtQ,WAAW,KACpCwS,eAAe,KAAK,CAACC,sBAAuB;;MAGlD,IAAIrG,WAAW,EAAE;QACf,MAAMsG,sBAAsB,GAAGjX,GAAG,CAACkE,UAAU,CAACM,aAAa,EAAEtG,IAAI,CAAC;QAElE,IAAI,CAAC+Y,sBAAsB,EAAE;UAC3BpW,GAAG,CAACqD,UAAU,CAACM,aAAa,EAAEtG,IAAI,EAAEyS,WAAW,CAAC;UAChDjI,MAAM,CAAClE,aAAa,GAAGN,UAAU,CAACM,aAAa;UAC/CsS,iBAAiB,GACfA,iBAAiB,IAChB,CAACnT,eAAe,CAACa,aAAa,IAC7BqQ,wBAAwB,CAACrQ,aAAa,KACtCyS,sBAAsB,KAAKtG,WAAY;;;MAI/CmG,iBAAiB,IAAID,YAAY,IAAIjM,SAAS,CAACC,KAAK,CAACC,IAAI,CAACpC,MAAM,CAAC;;IAGnE,OAAOoO,iBAAiB,GAAGpO,MAAM,GAAG,EAAE;EACxC,CAAC;EAED,MAAMwO,mBAAmB,GAAGA,CAC1BhZ,IAAuB,EACvByG,OAAiB,EACjBuC,KAAkB,EAClBL,UAIC,KACC;IACF,MAAMsQ,kBAAkB,GAAGnX,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAE1G,IAAI,CAAC;IACvD,MAAMmX,iBAAiB,GACrB,CAAC1R,eAAe,CAACgB,OAAO,IAAIkQ,wBAAwB,CAAClQ,OAAO,KAC5DpE,SAAS,CAACoE,OAAO,CAAC,IAClBT,UAAU,CAACS,OAAO,KAAKA,OAAO;IAEhC,IAAIoD,QAAQ,CAACqP,UAAU,IAAIlQ,KAAK,EAAE;MAChCyN,kBAAkB,GAAGM,QAAQ,CAAC,MAAMmB,YAAY,CAAClY,IAAI,EAAEgJ,KAAK,CAAC,CAAC;MAC9DyN,kBAAkB,CAAC5M,QAAQ,CAACqP,UAAU,CAAC;WAClC;MACLjC,YAAY,CAACP,KAAK,CAAC;MACnBD,kBAAkB,GAAG,IAAI;MACzBzN,KAAA,GACIrG,GAAG,CAACqD,UAAU,CAACU,MAAM,EAAE1G,IAAI,EAAEgJ,KAAK,IAClCsG,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE1G,IAAI,CAAC;;IAGpC,IACE,CAACgJ,KAAK,GAAG,CAAC8E,SAAS,CAACmL,kBAAkB,EAAEjQ,KAAK,CAAC,GAAGiQ,kBAAkB,KACnE,CAAC3K,aAAa,CAAC3F,UAAU,CAAC,IAC1BwO,iBAAiB,EACjB;MACA,MAAMgC,gBAAgB,GAAG;QACvB,GAAGxQ,UAAU;QACb,IAAIwO,iBAAiB,IAAI9U,SAAS,CAACoE,OAAO,CAAC,GAAG;UAAEA;QAAO,CAAE,GAAG,EAAE,CAAC;QAC/DC,MAAM,EAAEV,UAAU,CAACU,MAAM;QACzB1G;OACD;MAEDgG,UAAU,GAAG;QACX,GAAGA,UAAU;QACb,GAAGmT;OACJ;MAEDzM,SAAS,CAACC,KAAK,CAACC,IAAI,CAACuM,gBAAgB,CAAC;;EAE1C,CAAC;EAED,MAAM9B,UAAU,GAAG,MAAOrX,IAA0B,IAAI;IACtDuX,mBAAmB,CAACvX,IAAI,EAAE,IAAI,CAAC;IAC/B,MAAMkC,MAAM,GAAG,MAAM2H,QAAQ,CAACuN,QAAS,CACrCpP,WAA2B,EAC3B6B,QAAQ,CAACuP,OAAO,EAChBlI,kBAAkB,CAChBlR,IAAI,IAAIoH,MAAM,CAAC2C,KAAK,EACpBV,OAAO,EACPQ,QAAQ,CAACuH,YAAY,EACrBvH,QAAQ,CAACwH,yBAAyB,CACnC,CACF;IACDkG,mBAAmB,CAACvX,IAAI,CAAC;IACzB,OAAOkC,MAAM;EACf,CAAC;EAED,MAAMmX,2BAA2B,GAAG,MAAOjZ,KAA2B,IAAI;IACxE,MAAM;MAAEsG;IAAM,CAAE,GAAG,MAAM2Q,UAAU,CAACjX,KAAK,CAAC;IAE1C,IAAIA,KAAK,EAAE;MACT,KAAK,MAAMJ,IAAI,IAAII,KAAK,EAAE;QACxB,MAAM4I,KAAK,GAAGlH,GAAG,CAAC4E,MAAM,EAAE1G,IAAI,CAAC;QAC/BgJ,KAAA,GACIrG,GAAG,CAACqD,UAAU,CAACU,MAAM,EAAE1G,IAAI,EAAEgJ,KAAK,IAClCsG,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE1G,IAAI,CAAC;;WAE/B;MACLgG,UAAU,CAACU,MAAM,GAAGA,MAAM;;IAG5B,OAAOA,MAAM;EACf,CAAC;EAED,MAAM4Q,wBAAwB,GAAG,MAAAA,CAC/B3H,MAAiB,EACjB2J,oBAA8B,EAC9BF,OAEI;IACFG,KAAK,EAAE;EACR,MACC;IACF,KAAK,MAAMvZ,IAAI,IAAI2P,MAAM,EAAE;MACzB,MAAMvG,KAAK,GAAGuG,MAAM,CAAC3P,IAAI,CAAC;MAE1B,IAAIoJ,KAAK,EAAE;QACT,MAAM;UAAEE,EAAE;UAAE,GAAGmP;QAAU,CAAE,GAAGrP,KAAc;QAE5C,IAAIE,EAAE,EAAE;UACN,MAAMkQ,gBAAgB,GAAGpS,MAAM,CAACkB,KAAK,CAACjI,GAAG,CAACiJ,EAAE,CAACtJ,IAAI,CAAC;UAClD,MAAMyZ,iBAAiB,GACrBrQ,KAAK,CAACE,EAAE,IAAI6I,oBAAoB,CAAE/I,KAAe,CAACE,EAAE,CAAC;UAEvD,IAAImQ,iBAAiB,IAAIhU,eAAe,CAACc,gBAAgB,EAAE;YACzDgR,mBAAmB,CAAC,CAACvX,IAAI,CAAC,EAAE,IAAI,CAAC;;UAGnC,MAAM0Z,UAAU,GAAG,MAAMvF,aAAa,CACpC/K,KAAc,EACdhC,MAAM,CAACxB,QAAQ,EACfoC,WAAW,EACX8O,gCAAgC,EAChCjN,QAAQ,CAACwH,yBAAyB,IAAI,CAACiI,oBAAoB,EAC3DE,gBAAgB,CACjB;UAED,IAAIC,iBAAiB,IAAIhU,eAAe,CAACc,gBAAgB,EAAE;YACzDgR,mBAAmB,CAAC,CAACvX,IAAI,CAAC,CAAC;;UAG7B,IAAI0Z,UAAU,CAACpQ,EAAE,CAACtJ,IAAI,CAAC,EAAE;YACvBoZ,OAAO,CAACG,KAAK,GAAG,KAAK;YACrB,IAAID,oBAAoB,EAAE;cACxB;;;UAIJ,CAACA,oBAAoB,KAClBxX,GAAG,CAAC4X,UAAU,EAAEpQ,EAAE,CAACtJ,IAAI,IACpBwZ,gBAAA,GACE5F,yBAAyB,CACvB5N,UAAU,CAACU,MAAM,EACjBgT,UAAU,EACVpQ,EAAE,CAACtJ,IAAI,IAET2C,GAAG,CAACqD,UAAU,CAACU,MAAM,EAAE4C,EAAE,CAACtJ,IAAI,EAAE0Z,UAAU,CAACpQ,EAAE,CAACtJ,IAAI,CAAC,IACrDsP,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE4C,EAAE,CAACtJ,IAAI,CAAC,CAAC;;QAG1C,CAACsO,aAAa,CAACmK,UAAU,CAAC,KACvB,MAAMnB,wBAAwB,CAC7BmB,UAAU,EACVa,oBAAoB,EACpBF,OAAO,CACR,CAAC;;;IAIR,OAAOA,OAAO,CAACG,KAAK;EACtB,CAAC;EAED,MAAMrR,gBAAgB,GAAGA,CAAA,KAAK;IAC5B,KAAK,MAAMlI,IAAI,IAAIoH,MAAM,CAACoP,OAAO,EAAE;MACjC,MAAMpN,KAAK,GAAUtH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC;MAEvCoJ,KAAK,KACFA,KAAK,CAACE,EAAE,CAAC0H,IAAA,GACN5H,KAAK,CAACE,EAAE,CAAC0H,IAAI,CAACgD,KAAK,CAAE9K,GAAG,IAAK,CAAC8F,IAAI,CAAC9F,GAAG,CAAC,IACvC,CAAC8F,IAAI,CAAC5F,KAAK,CAACE,EAAE,CAACJ,GAAG,CAAC,CAAC,IACxBgB,UAAU,CAAClK,IAA+B,CAAC;;IAG/CoH,MAAM,CAACoP,OAAO,GAAG,IAAInV,GAAG,EAAE;EAC5B,CAAC;EAED,MAAM4W,SAAS,GAAeA,CAACjY,IAAI,EAAEiB,IAAI,KACvC,CAAC4I,QAAQ,CAACjE,QAAQ,KACjB5F,IAAI,IAAIiB,IAAI,IAAI0B,GAAG,CAACqF,WAAW,EAAEhI,IAAI,EAAEiB,IAAI,CAAC,EAC7C,CAAC6M,SAAS,CAAC6L,SAAS,EAAE,EAAEtU,cAAc,CAAC,CAAC;EAE1C,MAAM4C,SAAS,GAAgCA,CAC7C7H,KAAK,EACL6B,YAAY,EACZqF,QAAQ,KAERH,mBAAmB,CACjB/G,KAAK,EACLgH,MAAM,EACN;IACE,IAAI4C,MAAM,CAACD,KAAA,GACP/B,WAAA,GACArG,WAAW,CAACM,YAAY,IACtBoD,cAAA,GACA6B,QAAQ,CAAC9G,KAAK,IACZ;MAAE,CAACA,KAAK,GAAG6B;IAAY,IACvBA,YAAY;EACrB,GACDqF,QAAQ,EACRrF,YAAY,CACb;EAEH,MAAM2X,cAAc,GAClB5Z,IAAuB,IAEvBwB,OAAO,CACLM,GAAG,CACDkI,MAAM,CAACD,KAAK,GAAG/B,WAAW,GAAG3C,cAAc,EAC3CrF,IAAI,EACJ6J,QAAQ,CAACzB,gBAAgB,GAAGtG,GAAG,CAACuD,cAAc,EAAErF,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,CAC/D,CACF;EAEH,MAAMuY,aAAa,GAAGA,CACpBvY,IAAuB,EACvBZ,KAAkC,EAClC+Q,OAAA,GAA0B,EAAE,KAC1B;IACF,MAAM/G,KAAK,GAAUtH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC;IACvC,IAAIyY,UAAU,GAAYrZ,KAAK;IAE/B,IAAIgK,KAAK,EAAE;MACT,MAAMgJ,cAAc,GAAGhJ,KAAK,CAACE,EAAE;MAE/B,IAAI8I,cAAc,EAAE;QAClB,CAACA,cAAc,CAACxM,QAAQ,IACtBjD,GAAG,CAACqF,WAAW,EAAEhI,IAAI,EAAEsQ,eAAe,CAAClR,KAAK,EAAEgT,cAAc,CAAC,CAAC;QAEhEqG,UAAU,GACRhK,aAAa,CAAC2D,cAAc,CAAClJ,GAAG,CAAC,IAAI5J,iBAAiB,CAACF,KAAK,IACxD,KACAA,KAAK;QAEX,IAAIyP,gBAAgB,CAACuD,cAAc,CAAClJ,GAAG,CAAC,EAAE;UACxC,CAAC,GAAGkJ,cAAc,CAAClJ,GAAG,CAACiH,OAAO,CAAC,CAACsH,OAAO,CACpCoC,SAAS,IACPA,SAAS,CAACC,QAAQ,GACjBrB,UACD,CAACrM,QAAQ,CAACyN,SAAS,CAACza,KAAK,CAAE,CAC/B;eACI,IAAIgT,cAAc,CAACpB,IAAI,EAAE;UAC9B,IAAIhS,eAAe,CAACoT,cAAc,CAAClJ,GAAG,CAAC,EAAE;YACvCkJ,cAAc,CAACpB,IAAI,CAAClO,MAAM,GAAG,IACzBsP,cAAc,CAACpB,IAAI,CAACyG,OAAO,CACxBsC,WAAW,IACV,CAAC,CAACA,WAAW,CAACzB,cAAc,IAAI,CAACyB,WAAW,CAACnU,QAAQ,MACpDmU,WAAW,CAACja,OAAO,GAAGL,KAAK,CAACC,OAAO,CAAC+Y,UAAU,IAC3C,CAAC,CAAEA,UAAiB,CAACpG,IAAI,CACtBpR,IAAY,IAAKA,IAAI,KAAK8Y,WAAW,CAAC3a,KAAK,IAE9CqZ,UAAU,KAAKsB,WAAW,CAAC3a,KAAK,CAAC,IAEzCgT,cAAc,CAACpB,IAAI,CAAC,CAAC,CAAC,KACrBoB,cAAc,CAACpB,IAAI,CAAC,CAAC,CAAC,CAAClR,OAAO,GAAG,CAAC,CAAC2Y,UAAU,CAAC;iBAC9C;YACLrG,cAAc,CAACpB,IAAI,CAACyG,OAAO,CACxBuC,QAA0B,IACxBA,QAAQ,CAACla,OAAO,GAAGka,QAAQ,CAAC5a,KAAK,KAAKqZ,UAAW,CACrD;;eAEE,IAAIlK,WAAW,CAAC6D,cAAc,CAAClJ,GAAG,CAAC,EAAE;UAC1CkJ,cAAc,CAAClJ,GAAG,CAAC9J,KAAK,GAAG,EAAE;eACxB;UACLgT,cAAc,CAAClJ,GAAG,CAAC9J,KAAK,GAAGqZ,UAAU;UAErC,IAAI,CAACrG,cAAc,CAAClJ,GAAG,CAAChK,IAAI,EAAE;YAC5BwN,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;cACnB5M,IAAI;cACJ8H,MAAM,EAAE9G,WAAW,CAACgH,WAAW;YAChC,EAAC;;;;;IAMV,CAACmI,OAAO,CAACuI,WAAW,IAAIvI,OAAO,CAAC8J,WAAW,KACzCzB,mBAAmB,CACjBxY,IAAI,EACJyY,UAAU,EACVtI,OAAO,CAAC8J,WAAW,EACnB9J,OAAO,CAACuI,WAAW,EACnB,IAAI,CACL;IAEHvI,OAAO,CAAC+J,cAAc,IAAIC,OAAO,CAACna,IAA0B,CAAC;EAC/D,CAAC;EAED,MAAMoa,SAAS,GAAGA,CAKhBpa,IAAO,EACPZ,KAAQ,EACR+Q,OAAU,KACR;IACF,KAAK,MAAMkK,QAAQ,IAAIjb,KAAK,EAAE;MAC5B,MAAMqZ,UAAU,GAAGrZ,KAAK,CAACib,QAAQ,CAAC;MAClC,MAAM3S,SAAS,GAAG,GAAG1H,IAAI,IAAIqa,QAAQ,EAAE;MACvC,MAAMjR,KAAK,GAAGtH,GAAG,CAACuH,OAAO,EAAE3B,SAAS,CAAC;MAErC,CAACN,MAAM,CAACkB,KAAK,CAACjI,GAAG,CAACL,IAAI,CAAC,IACrBR,QAAQ,CAACiZ,UAAU,CAAC,IACnBrP,KAAK,IAAI,CAACA,KAAK,CAACE,EAAG,KACtB,CAACnK,YAAY,CAACsZ,UAAU,IACpB2B,SAAS,CAAC1S,SAAS,EAAE+Q,UAAU,EAAEtI,OAAO,IACxCoI,aAAa,CAAC7Q,SAAS,EAAE+Q,UAAU,EAAEtI,OAAO,CAAC;;EAErD,CAAC;EAED,MAAMmK,QAAQ,GAAkCA,CAC9Cta,IAAI,EACJZ,KAAK,EACL+Q,OAAO,GAAG,EAAE,KACV;IACF,MAAM/G,KAAK,GAAGtH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC;IAChC,MAAMqU,YAAY,GAAGjN,MAAM,CAACkB,KAAK,CAACjI,GAAG,CAACL,IAAI,CAAC;IAC3C,MAAMua,UAAU,GAAGvZ,WAAW,CAAC5B,KAAK,CAAC;IAErCuD,GAAG,CAACqF,WAAW,EAAEhI,IAAI,EAAEua,UAAU,CAAC;IAElC,IAAIlG,YAAY,EAAE;MAChB3H,SAAS,CAACpE,KAAK,CAACsE,IAAI,CAAC;QACnB5M,IAAI;QACJ8H,MAAM,EAAE9G,WAAW,CAACgH,WAAW;MAChC,EAAC;MAEF,IACE,CAACvC,eAAe,CAACU,OAAO,IACtBV,eAAe,CAACY,WAAW,IAC3BsQ,wBAAwB,CAACxQ,OAAO,IAChCwQ,wBAAwB,CAACtQ,WAAW,KACtC8J,OAAO,CAACuI,WAAW,EACnB;QACAhM,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UACnB5M,IAAI;UACJqG,WAAW,EAAE0J,cAAc,CAAC1K,cAAc,EAAE2C,WAAW,CAAC;UACxD7B,OAAO,EAAE8R,SAAS,CAACjY,IAAI,EAAEua,UAAU;QACpC,EAAC;;WAEC;MACLnR,KAAK,IAAI,CAACA,KAAK,CAACE,EAAE,IAAI,CAAChK,iBAAiB,CAACib,UAAU,IAC/CH,SAAS,CAACpa,IAAI,EAAEua,UAAU,EAAEpK,OAAO,IACnCoI,aAAa,CAACvY,IAAI,EAAEua,UAAU,EAAEpK,OAAO,CAAC;;IAG9CqC,SAAS,CAACxS,IAAI,EAAEoH,MAAM,CAAC,IAAIsF,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAAE,GAAG5G;IAAU,CAAE,CAAC;IAClE0G,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB5M,IAAI,EAAEgK,MAAM,CAACD,KAAK,GAAG/J,IAAI,GAAG6B,SAAS;MACrCiG,MAAM,EAAE9G,WAAW,CAACgH,WAAW;IAChC,EAAC;EACJ,CAAC;EAED,MAAMvE,QAAQ,GAAkB,MAAO7D,KAAK,IAAI;IAC9CoK,MAAM,CAACD,KAAK,GAAG,IAAI;IACnB,MAAMlK,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,IAAIG,IAAI,GAAWH,MAAM,CAACG,IAAI;IAC9B,IAAIwa,mBAAmB,GAAG,IAAI;IAC9B,MAAMpR,KAAK,GAAUtH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC;IACvC,MAAMya,0BAA0B,GAAIhC,UAAmB,IAAI;MACzD+B,mBAAmB,GACjBE,MAAM,CAACxX,KAAK,CAACuV,UAAU,CAAC,IACvBtZ,YAAY,CAACsZ,UAAU,CAAC,IAAIvV,KAAK,CAACuV,UAAU,CAACxK,OAAO,EAAE,CAAE,IACzDH,SAAS,CAAC2K,UAAU,EAAE3W,GAAG,CAACkG,WAAW,EAAEhI,IAAI,EAAEyY,UAAU,CAAC,CAAC;IAC7D,CAAC;IAED,IAAIrP,KAAK,EAAE;MACT,IAAIJ,KAAK;MACT,IAAIvC,OAAO;MACX,MAAMgS,UAAU,GAAG5Y,MAAM,CAACX,IAAA,GACtB4R,aAAa,CAAC1H,KAAK,CAACE,EAAE,IACtB3J,aAAa,CAACC,KAAK,CAAC;MACxB,MAAM6S,WAAW,GACf7S,KAAK,CAACV,IAAI,KAAKiE,MAAM,CAACC,IAAI,IAAIxD,KAAK,CAACV,IAAI,KAAKiE,MAAM,CAACE,SAAS;MAC/D,MAAMsX,oBAAoB,GACvB,CAACpI,aAAa,CAACnJ,KAAK,CAACE,EAAE,CAAC,IACvB,CAACO,QAAQ,CAACuN,QAAQ,IAClB,CAACtV,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAE1G,IAAI,CAAC,IAC7B,CAACoJ,KAAK,CAACE,EAAE,CAACsR,IAAI,IAChBpH,cAAc,CACZf,WAAW,EACX3Q,GAAG,CAACkE,UAAU,CAACM,aAAa,EAAEtG,IAAI,CAAC,EACnCgG,UAAU,CAACyN,WAAW,EACtBoD,yBAAyB,EACzBD,0BAA0B,CAC3B;MACH,MAAMiE,OAAO,GAAGrI,SAAS,CAACxS,IAAI,EAAEoH,MAAM,EAAEqL,WAAW,CAAC;MAEpD9P,GAAG,CAACqF,WAAW,EAAEhI,IAAI,EAAEyY,UAAU,CAAC;MAElC,IAAIhG,WAAW,EAAE;QACfrJ,KAAK,CAACE,EAAE,CAAC9F,MAAM,IAAI4F,KAAK,CAACE,EAAE,CAAC9F,MAAM,CAAC5D,KAAK,CAAC;QACzC6W,kBAAkB,IAAIA,kBAAkB,CAAC,CAAC,CAAC;aACtC,IAAIrN,KAAK,CAACE,EAAE,CAAC7F,QAAQ,EAAE;QAC5B2F,KAAK,CAACE,EAAE,CAAC7F,QAAQ,CAAC7D,KAAK,CAAC;;MAG1B,MAAM+I,UAAU,GAAG6P,mBAAmB,CAACxY,IAAI,EAAEyY,UAAU,EAAEhG,WAAW,CAAC;MAErE,MAAMkG,YAAY,GAAG,CAACrK,aAAa,CAAC3F,UAAU,CAAC,IAAIkS,OAAO;MAE1D,CAACpI,WAAW,IACV/F,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnB5M,IAAI;QACJd,IAAI,EAAEU,KAAK,CAACV,IAAI;QAChB4I,MAAM,EAAE9G,WAAW,CAACgH,WAAW;MAChC,EAAC;MAEJ,IAAI2S,oBAAoB,EAAE;QACxB,IAAIlV,eAAe,CAACgB,OAAO,IAAIkQ,wBAAwB,CAAClQ,OAAO,EAAE;UAC/D,IAAIoD,QAAQ,CAAC+H,IAAI,KAAK,QAAQ,EAAE;YAC9B,IAAIa,WAAW,EAAE;cACfzL,SAAS,EAAE;;iBAER,IAAI,CAACyL,WAAW,EAAE;YACvBzL,SAAS,EAAE;;;QAIf,OACE2R,YAAY,IACZjM,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UAAE5M,IAAI;UAAE,IAAI6a,OAAO,GAAG,EAAE,GAAGlS,UAAU;QAAC,CAAE,CAAC;;MAIlE,CAAC8J,WAAW,IAAIoI,OAAO,IAAInO,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAE,GAAG5G;MAAU,CAAE,CAAC;MAElE,IAAI6D,QAAQ,CAACuN,QAAQ,EAAE;QACrB,MAAM;UAAE1Q;QAAM,CAAE,GAAG,MAAM2Q,UAAU,CAAC,CAACrX,IAAI,CAAC,CAAC;QAE3Cya,0BAA0B,CAAChC,UAAU,CAAC;QAEtC,IAAI+B,mBAAmB,EAAE;UACvB,MAAMM,yBAAyB,GAAG/H,iBAAiB,CACjD/M,UAAU,CAACU,MAAM,EACjB2C,OAAO,EACPrJ,IAAI,CACL;UACD,MAAM+a,iBAAiB,GAAGhI,iBAAiB,CACzCrM,MAAM,EACN2C,OAAO,EACPyR,yBAAyB,CAAC9a,IAAI,IAAIA,IAAI,CACvC;UAEDgJ,KAAK,GAAG+R,iBAAiB,CAAC/R,KAAK;UAC/BhJ,IAAI,GAAG+a,iBAAiB,CAAC/a,IAAI;UAE7ByG,OAAO,GAAG6H,aAAa,CAAC5H,MAAM,CAAC;;aAE5B;QACL6Q,mBAAmB,CAAC,CAACvX,IAAI,CAAC,EAAE,IAAI,CAAC;QACjCgJ,KAAK,GAAG,CACN,MAAMmL,aAAa,CACjB/K,KAAK,EACLhC,MAAM,CAACxB,QAAQ,EACfoC,WAAW,EACX8O,gCAAgC,EAChCjN,QAAQ,CAACwH,yBAAyB,CACnC,EACDrR,IAAI,CAAC;QACPuX,mBAAmB,CAAC,CAACvX,IAAI,CAAC,CAAC;QAE3Bya,0BAA0B,CAAChC,UAAU,CAAC;QAEtC,IAAI+B,mBAAmB,EAAE;UACvB,IAAIxR,KAAK,EAAE;YACTvC,OAAO,GAAG,KAAK;iBACV,IACLhB,eAAe,CAACgB,OAAO,IACvBkQ,wBAAwB,CAAClQ,OAAO,EAChC;YACAA,OAAO,GAAG,MAAM6Q,wBAAwB,CAACjO,OAAO,EAAE,IAAI,CAAC;;;;MAK7D,IAAImR,mBAAmB,EAAE;QACvBpR,KAAK,CAACE,EAAE,CAACsR,IAAI,IACXT,OAAO,CACL/Q,KAAK,CAACE,EAAE,CAACsR,IAEoB,CAC9B;QACH5B,mBAAmB,CAAChZ,IAAI,EAAEyG,OAAO,EAAEuC,KAAK,EAAEL,UAAU,CAAC;;;EAG3D,CAAC;EAED,MAAMqS,WAAW,GAAGA,CAAC9R,GAAQ,EAAE3H,GAAW,KAAI;IAC5C,IAAIO,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAEnF,GAAG,CAAC,IAAI2H,GAAG,CAACK,KAAK,EAAE;MAC5CL,GAAG,CAACK,KAAK,EAAE;MACX,OAAO,CAAC;;IAEV;EACF,CAAC;EAED,MAAM4Q,OAAO,GAAiC,MAAAA,CAAOna,IAAI,EAAEmQ,OAAO,GAAG,EAAE,KAAI;IACzE,IAAI1J,OAAO;IACX,IAAIyP,gBAAgB;IACpB,MAAM+E,UAAU,GAAG7N,qBAAqB,CAACpN,IAAI,CAAwB;IAErE,IAAI6J,QAAQ,CAACuN,QAAQ,EAAE;MACrB,MAAM1Q,MAAM,GAAG,MAAM2S,2BAA2B,CAC9C1X,WAAW,CAAC3B,IAAI,CAAC,GAAGA,IAAI,GAAGib,UAAU,CACtC;MAEDxU,OAAO,GAAG6H,aAAa,CAAC5H,MAAM,CAAC;MAC/BwP,gBAAgB,GAAGlW,IAAA,GACf,CAACib,UAAU,CAAC9O,IAAI,CAAEnM,IAAI,IAAK8B,GAAG,CAAC4E,MAAM,EAAE1G,IAAI,CAAC,IAC5CyG,OAAO;WACN,IAAIzG,IAAI,EAAE;MACfkW,gBAAgB,GAAG,CACjB,MAAMgF,OAAO,CAACtX,GAAG,CACfqX,UAAU,CAACxT,GAAG,CAAC,MAAOC,SAAS,IAAI;QACjC,MAAM0B,KAAK,GAAGtH,GAAG,CAACuH,OAAO,EAAE3B,SAAS,CAAC;QACrC,OAAO,MAAM4P,wBAAwB,CACnClO,KAAK,IAAIA,KAAK,CAACE,EAAE,GAAG;UAAE,CAAC5B,SAAS,GAAG0B;QAAK,CAAE,GAAGA,KAAK,CACnD;OACF,CAAC,CACH,EACD4K,KAAK,CAACtS,OAAO,CAAC;MAChB,EAAE,CAACwU,gBAAgB,IAAI,CAAClQ,UAAU,CAACS,OAAO,CAAC,IAAIO,SAAS,EAAE;WACrD;MACLkP,gBAAgB,GAAGzP,OAAO,GAAG,MAAM6Q,wBAAwB,CAACjO,OAAO,CAAC;;IAGtEqD,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB,IAAI,CAAC1F,QAAQ,CAAClH,IAAI,CAAC,IAClB,CAACyF,eAAe,CAACgB,OAAO,IAAIkQ,wBAAwB,CAAClQ,OAAO,KAC3DA,OAAO,KAAKT,UAAU,CAACS,OAAO,GAC5B,KACA;QAAEzG;MAAI,CAAE,CAAC;MACb,IAAI6J,QAAQ,CAACuN,QAAQ,IAAI,CAACpX,IAAI,GAAG;QAAEyG;MAAO,CAAE,GAAG,EAAE,CAAC;MAClDC,MAAM,EAAEV,UAAU,CAACU;IACpB,EAAC;IAEFyJ,OAAO,CAACgL,WAAW,IACjB,CAACjF,gBAAgB,IACjBtD,qBAAqB,CACnBvJ,OAAO,EACP2R,WAAW,EACXhb,IAAI,GAAGib,UAAU,GAAG7T,MAAM,CAAC2C,KAAK,CACjC;IAEH,OAAOmM,gBAAgB;EACzB,CAAC;EAED,MAAMyD,SAAS,GACbsB,UAE0C,IACxC;IACF,MAAMnT,MAAM,GAAG;MACb,IAAIkC,MAAM,CAACD,KAAK,GAAG/B,WAAW,GAAG3C,cAAc;KAChD;IAED,OAAO1D,WAAW,CAACsZ,UAAU,IACzBnT,MAAA,GACAZ,QAAQ,CAAC+T,UAAU,IACjBnZ,GAAG,CAACgG,MAAM,EAAEmT,UAAU,IACtBA,UAAU,CAACxT,GAAG,CAAEzH,IAAI,IAAK8B,GAAG,CAACgG,MAAM,EAAE9H,IAAI,CAAC,CAAC;EACnD,CAAC;EAED,MAAMob,aAAa,GAAuCA,CACxDpb,IAAI,EACJgF,SAAS,MACL;IACJ6D,OAAO,EAAE,CAAC,CAAC/G,GAAG,CAAC,CAACkD,SAAS,IAAIgB,UAAU,EAAEU,MAAM,EAAE1G,IAAI,CAAC;IACtDmG,OAAO,EAAE,CAAC,CAACrE,GAAG,CAAC,CAACkD,SAAS,IAAIgB,UAAU,EAAEK,WAAW,EAAErG,IAAI,CAAC;IAC3DgJ,KAAK,EAAElH,GAAG,CAAC,CAACkD,SAAS,IAAIgB,UAAU,EAAEU,MAAM,EAAE1G,IAAI,CAAC;IAClDwG,YAAY,EAAE,CAAC,CAAC1E,GAAG,CAACkE,UAAU,CAACO,gBAAgB,EAAEvG,IAAI,CAAC;IACtD+I,SAAS,EAAE,CAAC,CAACjH,GAAG,CAAC,CAACkD,SAAS,IAAIgB,UAAU,EAAEM,aAAa,EAAEtG,IAAI;EAC/D,EAAC;EAEF,MAAMqb,WAAW,GAAsCrb,IAAI,IAAI;IAC7DA,IAAI,IACFoN,qBAAqB,CAACpN,IAAI,CAAC,CAACyX,OAAO,CAAE6D,SAAS,IAC5ChM,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE4U,SAAS,CAAC,CACpC;IAEH5O,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBlG,MAAM,EAAE1G,IAAI,GAAGgG,UAAU,CAACU,MAAM,GAAG;IACpC,EAAC;EACJ,CAAC;EAED,MAAMoG,QAAQ,GAAkCA,CAAC9M,IAAI,EAAEgJ,KAAK,EAAEmH,OAAO,KAAI;IACvE,MAAMjH,GAAG,GAAG,CAACpH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,EAAE;MAAEsJ,EAAE,EAAE;IAAE,CAAE,CAAC,CAACA,EAAE,IAAI,EAAE,EAAEJ,GAAG;IACzD,MAAMqS,YAAY,GAAGzZ,GAAG,CAACkE,UAAU,CAACU,MAAM,EAAE1G,IAAI,CAAC,IAAI,EAAE;;IAGvD,MAAM;MAAEkJ,GAAG,EAAEsS,UAAU;MAAE9R,OAAO;MAAExK,IAAI;MAAE,GAAGuc;IAAe,CAAE,GAAGF,YAAY;IAE3E5Y,GAAG,CAACqD,UAAU,CAACU,MAAM,EAAE1G,IAAI,EAAE;MAC3B,GAAGyb,eAAe;MAClB,GAAGzS,KAAK;MACRE;IACD,EAAC;IAEFwD,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB5M,IAAI;MACJ0G,MAAM,EAAEV,UAAU,CAACU,MAAM;MACzBD,OAAO,EAAE;IACV,EAAC;IAEF0J,OAAO,IAAIA,OAAO,CAACgL,WAAW,IAAIjS,GAAG,IAAIA,GAAG,CAACK,KAAK,IAAIL,GAAG,CAACK,KAAK,EAAE;EACnE,CAAC;EAED,MAAMhC,KAAK,GAA+BA,CACxCvH,IAG+B,EAC/BiC,YAAwC,KAExCuM,UAAU,CAACxO,IAAI,IACX0M,SAAS,CAACC,KAAK,CAACa,SAAS,CAAC;IACxBZ,IAAI,EAAG8O,OAAO,IACZ1b,IAAI,CACFiI,SAAS,CAACpG,SAAS,EAAEI,YAAY,CAAC,EAClCyZ,OAIC;GAEN,IACDzT,SAAS,CACPjI,IAA+C,EAC/CiC,YAAY,EACZ,IAAI,CACL;EAEP,MAAM6E,UAAU,GAAiCnC,KAAK,IACpD+H,SAAS,CAACC,KAAK,CAACa,SAAS,CAAC;IACxBZ,IAAI,EACF5H,SAGC,IACC;MACF,IACEqO,qBAAqB,CAAC1O,KAAK,CAAC3E,IAAI,EAAEgF,SAAS,CAAChF,IAAI,EAAE2E,KAAK,CAACkB,KAAK,CAAC,IAC9DsN,qBAAqB,CACnBnO,SAAS,EACRL,KAAK,CAACK,SAA2B,IAAIS,eAAe,EACrDkW,aAAa,EACbhX,KAAK,CAACiX,YAAY,CACnB,EACD;QACAjX,KAAK,CAACoC,QAAQ,CAAC;UACbe,MAAM,EAAE;YAAE,GAAGE;UAAW,CAAkB;UAC1C,GAAGhC,UAAU;UACb,GAAGhB;QACJ,EAAC;;;GAGP,CAAC,CAAC0I,WAAW;EAEhB,MAAMF,SAAS,GAAoC7I,KAAK,IAAI;IAC1DqF,MAAM,CAACD,KAAK,GAAG,IAAI;IACnB4M,wBAAwB,GAAG;MACzB,GAAGA,wBAAwB;MAC3B,GAAGhS,KAAK,CAACK;KACV;IACD,OAAO8B,UAAU,CAAC;MAChB,GAAGnC,KAAK;MACRK,SAAS,EAAE2R;IACZ,EAAC;EACJ,CAAC;EAED,MAAMzM,UAAU,GAAoCA,CAAClK,IAAI,EAAEmQ,OAAO,GAAG,EAAE,KAAI;IACzE,KAAK,MAAMzI,SAAS,IAAI1H,IAAI,GAAGoN,qBAAqB,CAACpN,IAAI,CAAC,GAAGoH,MAAM,CAAC2C,KAAK,EAAE;MACzE3C,MAAM,CAAC2C,KAAK,CAAC8R,MAAM,CAACnU,SAAS,CAAC;MAC9BN,MAAM,CAACkB,KAAK,CAACuT,MAAM,CAACnU,SAAS,CAAC;MAE9B,IAAI,CAACyI,OAAO,CAAC2L,SAAS,EAAE;QACtBxM,KAAK,CAACjG,OAAO,EAAE3B,SAAS,CAAC;QACzB4H,KAAK,CAACtH,WAAW,EAAEN,SAAS,CAAC;;MAG/B,CAACyI,OAAO,CAAC4L,SAAS,IAAIzM,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAEgB,SAAS,CAAC;MACzD,CAACyI,OAAO,CAAC6L,SAAS,IAAI1M,KAAK,CAACtJ,UAAU,CAACK,WAAW,EAAEqB,SAAS,CAAC;MAC9D,CAACyI,OAAO,CAAC8L,WAAW,IAAI3M,KAAK,CAACtJ,UAAU,CAACM,aAAa,EAAEoB,SAAS,CAAC;MAClE,CAACyI,OAAO,CAAC+L,gBAAgB,IACvB5M,KAAK,CAACtJ,UAAU,CAACO,gBAAgB,EAAEmB,SAAS,CAAC;MAC/C,CAACmC,QAAQ,CAACzB,gBAAgB,IACxB,CAAC+H,OAAO,CAACgM,gBAAgB,IACzB7M,KAAK,CAACjK,cAAc,EAAEqC,SAAS,CAAC;;IAGpCgF,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB9E,MAAM,EAAE9G,WAAW,CAACgH,WAAW;IAChC,EAAC;IAEF0E,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB,GAAG5G,UAAU;MACb,IAAI,CAACmK,OAAO,CAAC6L,SAAS,GAAG,EAAE,GAAG;QAAE7V,OAAO,EAAE8R,SAAS;MAAE,CAAE;IACvD,EAAC;IAEF,CAAC9H,OAAO,CAACiM,WAAW,IAAIpV,SAAS,EAAE;EACrC,CAAC;EAED,MAAMmD,iBAAiB,GAA+CA,CAAC;IACrEvE,QAAQ;IACR5F;EAAI,CACL,KAAI;IACH,IACGqC,SAAS,CAACuD,QAAQ,CAAC,IAAIoE,MAAM,CAACD,KAAK,IACpC,CAAC,CAACnE,QAAQ,IACVwB,MAAM,CAACxB,QAAQ,CAACvF,GAAG,CAACL,IAAI,CAAC,EACzB;MACA4F,QAAQ,GAAGwB,MAAM,CAACxB,QAAQ,CAAC4B,GAAG,CAACxH,IAAI,CAAC,GAAGoH,MAAM,CAACxB,QAAQ,CAACiW,MAAM,CAAC7b,IAAI,CAAC;;EAEvE,CAAC;EAED,MAAMyI,QAAQ,GAAkCA,CAACzI,IAAI,EAAEmQ,OAAO,GAAG,EAAE,KAAI;IACrE,IAAI/G,KAAK,GAAGtH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC;IAC9B,MAAMqc,iBAAiB,GACrBha,SAAS,CAAC8N,OAAO,CAACvK,QAAQ,CAAC,IAAIvD,SAAS,CAACwH,QAAQ,CAACjE,QAAQ,CAAC;IAE7DjD,GAAG,CAAC0G,OAAO,EAAErJ,IAAI,EAAE;MACjB,IAAIoJ,KAAK,IAAI,EAAE,CAAC;MAChBE,EAAE,EAAE;QACF,IAAIF,KAAK,IAAIA,KAAK,CAACE,EAAE,GAAGF,KAAK,CAACE,EAAE,GAAG;UAAEJ,GAAG,EAAE;YAAElJ;UAAI;QAAE,CAAE,CAAC;QACrDA,IAAI;QACJ+J,KAAK,EAAE,IAAI;QACX,GAAGoG;MACJ;IACF,EAAC;IACF/I,MAAM,CAAC2C,KAAK,CAACvC,GAAG,CAACxH,IAAI,CAAC;IAEtB,IAAIoJ,KAAK,EAAE;MACTe,iBAAiB,CAAC;QAChBvE,QAAQ,EAAEvD,SAAS,CAAC8N,OAAO,CAACvK,QAAQ,IAChCuK,OAAO,CAACvK,QAAA,GACRiE,QAAQ,CAACjE,QAAQ;QACrB5F;MACD,EAAC;WACG;MACLoY,mBAAmB,CAACpY,IAAI,EAAE,IAAI,EAAEmQ,OAAO,CAAC/Q,KAAK,CAAC;;IAGhD,OAAO;MACL,IAAIid,iBAAA,GACA;QAAEzW,QAAQ,EAAEuK,OAAO,CAACvK,QAAQ,IAAIiE,QAAQ,CAACjE;MAAQ,IACjD,EAAE,CAAC;MACP,IAAIiE,QAAQ,CAACyS,WAAA,GACT;QACEnY,QAAQ,EAAE,CAAC,CAACgM,OAAO,CAAChM,QAAQ;QAC5BJ,GAAG,EAAEyN,YAAY,CAACrB,OAAO,CAACpM,GAAG,CAAC;QAC9BD,GAAG,EAAE0N,YAAY,CAACrB,OAAO,CAACrM,GAAG,CAAC;QAC9BG,SAAS,EAAEuN,YAAY,CAASrB,OAAO,CAAClM,SAAS,CAAW;QAC5DD,SAAS,EAAEwN,YAAY,CAACrB,OAAO,CAACnM,SAAS,CAAW;QACpDE,OAAO,EAAEsN,YAAY,CAACrB,OAAO,CAACjM,OAAO;MACtC,IACD,EAAE,CAAC;MACPlE,IAAI;MACJyD,QAAQ;MACRD,MAAM,EAAEC,QAAQ;MAChByF,GAAG,EAAGA,GAA4B,IAAU;QAC1C,IAAIA,GAAG,EAAE;UACPT,QAAQ,CAACzI,IAAI,EAAEmQ,OAAO,CAAC;UACvB/G,KAAK,GAAGtH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC;UAE1B,MAAMuc,QAAQ,GAAG5a,WAAW,CAACuH,GAAG,CAAC9J,KAAK,IAClC8J,GAAG,CAACsT,gBAAA,GACDtT,GAAG,CAACsT,gBAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAS,IAAItT,GAAA,GAC7DA,GAAA,GACFA,GAAG;UACP,MAAMuT,eAAe,GAAG1N,iBAAiB,CAACwN,QAAQ,CAAC;UACnD,MAAMvL,IAAI,GAAG5H,KAAK,CAACE,EAAE,CAAC0H,IAAI,IAAI,EAAE;UAEhC,IACEyL,eAAA,GACIzL,IAAI,CAACqB,IAAI,CAAEjC,MAAW,IAAKA,MAAM,KAAKmM,QAAQ,IAC9CA,QAAQ,KAAKnT,KAAK,CAACE,EAAE,CAACJ,GAAG,EAC7B;YACA;;UAGFvG,GAAG,CAAC0G,OAAO,EAAErJ,IAAI,EAAE;YACjBsJ,EAAE,EAAE;cACF,GAAGF,KAAK,CAACE,EAAE;cACX,IAAImT,eAAA,GACA;gBACEzL,IAAI,EAAE,CACJ,GAAGA,IAAI,CAACvP,MAAM,CAACuN,IAAI,CAAC,EACpBuN,QAAQ,EACR,IAAI9c,KAAK,CAACC,OAAO,CAACoC,GAAG,CAACuD,cAAc,EAAErF,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAC1D;gBACDkJ,GAAG,EAAE;kBAAEhK,IAAI,EAAEqd,QAAQ,CAACrd,IAAI;kBAAEc;gBAAI;cACjC,IACD;gBAAEkJ,GAAG,EAAEqT;cAAQ,CAAE;YACtB;UACF,EAAC;UAEFnE,mBAAmB,CAACpY,IAAI,EAAE,KAAK,EAAE6B,SAAS,EAAE0a,QAAQ,CAAC;eAChD;UACLnT,KAAK,GAAGtH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,EAAE,EAAE,CAAC;UAE9B,IAAIoJ,KAAK,CAACE,EAAE,EAAE;YACZF,KAAK,CAACE,EAAE,CAACS,KAAK,GAAG,KAAK;;UAGxB,CAACF,QAAQ,CAACzB,gBAAgB,IAAI+H,OAAO,CAAC/H,gBAAgB,KACpD,EAAEjI,kBAAkB,CAACiH,MAAM,CAACkB,KAAK,EAAEtI,IAAI,CAAC,IAAIgK,MAAM,CAACC,MAAM,CAAC,IAC1D7C,MAAM,CAACoP,OAAO,CAAChP,GAAG,CAACxH,IAAI,CAAC;;;KAG/B;EACH,CAAC;EAED,MAAM0c,WAAW,GAAGA,CAAA,KAClB7S,QAAQ,CAACuM,gBAAgB,IACzBxD,qBAAqB,CAACvJ,OAAO,EAAE2R,WAAW,EAAE5T,MAAM,CAAC2C,KAAK,CAAC;EAE3D,MAAM4S,YAAY,GAAI/W,QAAkB,IAAI;IAC1C,IAAIvD,SAAS,CAACuD,QAAQ,CAAC,EAAE;MACvB8G,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAEhH;MAAQ,CAAE,CAAC;MAClCgN,qBAAqB,CACnBvJ,OAAO,EACP,CAACH,GAAG,EAAElJ,IAAI,KAAI;QACZ,MAAM8S,YAAY,GAAUhR,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC;QAC9C,IAAI8S,YAAY,EAAE;UAChB5J,GAAG,CAACtD,QAAQ,GAAGkN,YAAY,CAACxJ,EAAE,CAAC1D,QAAQ,IAAIA,QAAQ;UAEnD,IAAInG,KAAK,CAACC,OAAO,CAACoT,YAAY,CAACxJ,EAAE,CAAC0H,IAAI,CAAC,EAAE;YACvC8B,YAAY,CAACxJ,EAAE,CAAC0H,IAAI,CAACyG,OAAO,CAAElD,QAAQ,IAAI;cACxCA,QAAQ,CAAC3O,QAAQ,GAAGkN,YAAY,CAACxJ,EAAE,CAAC1D,QAAQ,IAAIA,QAAQ;YAC1D,CAAC,CAAC;;;MAGR,CAAC,EACD,CAAC,EACD,KAAK,CACN;;EAEL,CAAC;EAED,MAAM6F,YAAY,GAChBA,CAACmR,OAAO,EAAEC,SAAS,KAAK,MAAOC,CAAC,IAAI;IAClC,IAAIC,YAAY,GAAGlb,SAAS;IAC5B,IAAIib,CAAC,EAAE;MACLA,CAAC,CAACE,cAAc,IAAIF,CAAC,CAACE,cAAc,EAAE;MACrCF,CAA8B,CAACG,OAAO,IACpCH,CAA8B,CAACG,OAAO,EAAE;;IAE7C,IAAInF,WAAW,GACb9W,WAAW,CAACgH,WAAW,CAAC;IAE1B0E,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB2J,YAAY,EAAE;IACf,EAAC;IAEF,IAAI1M,QAAQ,CAACuN,QAAQ,EAAE;MACrB,MAAM;QAAE1Q,MAAM;QAAEoB;MAAM,CAAE,GAAG,MAAMuP,UAAU,EAAE;MAC7CrR,UAAU,CAACU,MAAM,GAAGA,MAAM;MAC1BoR,WAAW,GAAGhQ,MAAsB;WAC/B;MACL,MAAMwP,wBAAwB,CAACjO,OAAO,CAAC;;IAGzC,IAAIjC,MAAM,CAACxB,QAAQ,CAACsX,IAAI,EAAE;MACxB,KAAK,MAAMld,IAAI,IAAIoH,MAAM,CAACxB,QAAQ,EAAE;QAClCjD,GAAG,CAACmV,WAAW,EAAE9X,IAAI,EAAE6B,SAAS,CAAC;;;IAIrCyN,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE,MAAM,CAAC;IAEhC,IAAI4H,aAAa,CAACtI,UAAU,CAACU,MAAM,CAAC,EAAE;MACpCgG,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnBlG,MAAM,EAAE;MACT,EAAC;MACF,IAAI;QACF,MAAMkW,OAAO,CAAC9E,WAAiC,EAAEgF,CAAC,CAAC;QACnD,OAAO9T,KAAK,EAAE;QACd+T,YAAY,GAAG/T,KAAK;;WAEjB;MACL,IAAI6T,SAAS,EAAE;QACb,MAAMA,SAAS,CAAC;UAAE,GAAG7W,UAAU,CAACU;QAAM,CAAE,EAAEoW,CAAC,CAAC;;MAE9CJ,WAAW,EAAE;MACbxF,UAAU,CAACwF,WAAW,CAAC;;IAGzBhQ,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB6G,WAAW,EAAE,IAAI;MACjB8C,YAAY,EAAE,KAAK;MACnB1J,kBAAkB,EAAEyB,aAAa,CAACtI,UAAU,CAACU,MAAM,CAAC,IAAI,CAACqW,YAAY;MACrEzG,WAAW,EAAEtQ,UAAU,CAACsQ,WAAW,GAAG,CAAC;MACvC5P,MAAM,EAAEV,UAAU,CAACU;IACpB,EAAC;IACF,IAAIqW,YAAY,EAAE;MAChB,MAAMA,YAAY;;EAEtB,CAAC;EAEH,MAAMI,UAAU,GAAoCA,CAACnd,IAAI,EAAEmQ,OAAO,GAAG,EAAE,KAAI;IACzE,IAAIrO,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC,EAAE;MACtB,IAAI2B,WAAW,CAACwO,OAAO,CAAClO,YAAY,CAAC,EAAE;QACrCqY,QAAQ,CAACta,IAAI,EAAEgB,WAAW,CAACc,GAAG,CAACuD,cAAc,EAAErF,IAAI,CAAC,CAAC,CAAC;aACjD;QACLsa,QAAQ,CACNta,IAAI,EACJmQ,OAAO,CAAClO,YAA2D,CACpE;QACDU,GAAG,CAAC0C,cAAc,EAAErF,IAAI,EAAEgB,WAAW,CAACmP,OAAO,CAAClO,YAAY,CAAC,CAAC;;MAG9D,IAAI,CAACkO,OAAO,CAAC8L,WAAW,EAAE;QACxB3M,KAAK,CAACtJ,UAAU,CAACM,aAAa,EAAEtG,IAAI,CAAC;;MAGvC,IAAI,CAACmQ,OAAO,CAAC6L,SAAS,EAAE;QACtB1M,KAAK,CAACtJ,UAAU,CAACK,WAAW,EAAErG,IAAI,CAAC;QACnCgG,UAAU,CAACG,OAAO,GAAGgK,OAAO,CAAClO,YAAA,GACzBgW,SAAS,CAACjY,IAAI,EAAEgB,WAAW,CAACc,GAAG,CAACuD,cAAc,EAAErF,IAAI,CAAC,CAAC,IACtDiY,SAAS,EAAE;;MAGjB,IAAI,CAAC9H,OAAO,CAAC4L,SAAS,EAAE;QACtBzM,KAAK,CAACtJ,UAAU,CAACU,MAAM,EAAE1G,IAAI,CAAC;QAC9ByF,eAAe,CAACgB,OAAO,IAAIO,SAAS,EAAE;;MAGxC0F,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAE,GAAG5G;MAAU,CAAE,CAAC;;EAE3C,CAAC;EAED,MAAMoX,MAAM,GAA+BA,CACzC/V,UAAU,EACVgW,gBAAgB,GAAG,EAAE,KACnB;IACF,MAAMC,aAAa,GAAGjW,UAAU,GAAGrG,WAAW,CAACqG,UAAU,CAAC,GAAGhC,cAAc;IAC3E,MAAMkY,kBAAkB,GAAGvc,WAAW,CAACsc,aAAa,CAAC;IACrD,MAAME,kBAAkB,GAAGlP,aAAa,CAACjH,UAAU,CAAC;IACpD,MAAMS,MAAM,GAAG0V,kBAAkB,GAAGnY,cAAc,GAAGkY,kBAAkB;IAEvE,IAAI,CAACF,gBAAgB,CAACI,iBAAiB,EAAE;MACvCpY,cAAc,GAAGiY,aAAa;;IAGhC,IAAI,CAACD,gBAAgB,CAACK,UAAU,EAAE;MAChC,IAAIL,gBAAgB,CAACM,eAAe,EAAE;QACpC,MAAMC,aAAa,GAAG,IAAIvc,GAAG,CAAC,CAC5B,GAAG+F,MAAM,CAAC2C,KAAK,EACf,GAAGzE,MAAM,CAACmF,IAAI,CAACsF,cAAc,CAAC1K,cAAc,EAAE2C,WAAW,CAAC,CAAC,CAC5D,CAAC;QACF,KAAK,MAAMN,SAAS,IAAIjI,KAAK,CAAC+X,IAAI,CAACoG,aAAa,CAAC,EAAE;UACjD9b,GAAG,CAACkE,UAAU,CAACK,WAAW,EAAEqB,SAAS,IACjC/E,GAAG,CAACmF,MAAM,EAAEJ,SAAS,EAAE5F,GAAG,CAACkG,WAAW,EAAEN,SAAS,CAAC,IAClD4S,QAAQ,CACN5S,SAAoC,EACpC5F,GAAG,CAACgG,MAAM,EAAEJ,SAAS,CAAC,CACvB;;aAEF;QACL,IAAI9G,KAAK,IAAIe,WAAW,CAAC0F,UAAU,CAAC,EAAE;UACpC,KAAK,MAAMrH,IAAI,IAAIoH,MAAM,CAAC2C,KAAK,EAAE;YAC/B,MAAMX,KAAK,GAAGtH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC;YAChC,IAAIoJ,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;cACrB,MAAM8I,cAAc,GAAG3S,KAAK,CAACC,OAAO,CAAC0J,KAAK,CAACE,EAAE,CAAC0H,IAAI,IAC9C5H,KAAK,CAACE,EAAE,CAAC0H,IAAI,CAAC,CAAC,IACf5H,KAAK,CAACE,EAAE,CAACJ,GAAG;cAEhB,IAAIuF,aAAa,CAAC2D,cAAc,CAAC,EAAE;gBACjC,MAAMyL,IAAI,GAAGzL,cAAc,CAAC0L,OAAO,CAAC,MAAM,CAAC;gBAC3C,IAAID,IAAI,EAAE;kBACRA,IAAI,CAACE,KAAK,EAAE;kBACZ;;;;;;QAOV,KAAK,MAAMrW,SAAS,IAAIN,MAAM,CAAC2C,KAAK,EAAE;UACpCuQ,QAAQ,CACN5S,SAAoC,EACpC5F,GAAG,CAACgG,MAAM,EAAEJ,SAAS,CAAC,CACvB;;;MAILM,WAAW,GAAGhH,WAAW,CAAC8G,MAAM,CAAiB;MAEjD4E,SAAS,CAACpE,KAAK,CAACsE,IAAI,CAAC;QACnB9E,MAAM,EAAE;UAAE,GAAGA;QAAM;MACpB,EAAC;MAEF4E,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QACnB9E,MAAM,EAAE;UAAE,GAAGA;QAAM;MACpB,EAAC;;IAGJV,MAAM,GAAG;MACP2C,KAAK,EAAEsT,gBAAgB,CAACM,eAAe,GAAGvW,MAAM,CAAC2C,KAAK,GAAG,IAAI1I,GAAG,EAAE;MAClEmV,OAAO,EAAE,IAAInV,GAAG,EAAE;MAClBiH,KAAK,EAAE,IAAIjH,GAAG,EAAE;MAChBuE,QAAQ,EAAE,IAAIvE,GAAG,EAAE;MACnBkG,KAAK,EAAE,IAAIlG,GAAG,EAAE;MAChBsG,QAAQ,EAAE,KAAK;MACf4B,KAAK,EAAE;KACR;IAEDS,MAAM,CAACD,KAAK,GACV,CAACtE,eAAe,CAACgB,OAAO,IACxB,CAAC,CAAC4W,gBAAgB,CAACjB,WAAW,IAC9B,CAAC,CAACiB,gBAAgB,CAACM,eAAe;IAEpC3T,MAAM,CAACzC,KAAK,GAAG,CAAC,CAACsC,QAAQ,CAACzB,gBAAgB;IAE1CsE,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnB0J,WAAW,EAAE+G,gBAAgB,CAACW,eAAA,GAC1BhY,UAAU,CAACsQ,WAAA,GACX,CAAC;MACLnQ,OAAO,EAAEqX,kBAAA,GACL,QACAH,gBAAgB,CAACrB,SAAA,GACfhW,UAAU,CAACG,OAAA,GACX,CAAC,EACCkX,gBAAgB,CAACI,iBAAiB,IAClC,CAAC3P,SAAS,CAACzG,UAAU,EAAEhC,cAAc,CAAC,CACvC;MACPoO,WAAW,EAAE4J,gBAAgB,CAACY,eAAA,GAC1BjY,UAAU,CAACyN,WAAA,GACX,KAAK;MACTpN,WAAW,EAAEmX,kBAAA,GACT,KACAH,gBAAgB,CAACM,eAAA,GACfN,gBAAgB,CAACI,iBAAiB,IAAIzV,WAAA,GACpC+H,cAAc,CAAC1K,cAAc,EAAE2C,WAAW,IAC1ChC,UAAU,CAACK,WAAA,GACbgX,gBAAgB,CAACI,iBAAiB,IAAIpW,UAAA,GACpC0I,cAAc,CAAC1K,cAAc,EAAEgC,UAAU,IACzCgW,gBAAgB,CAACrB,SAAA,GACfhW,UAAU,CAACK,WAAA,GACX,EAAE;MACZC,aAAa,EAAE+W,gBAAgB,CAACpB,WAAA,GAC5BjW,UAAU,CAACM,aAAA,GACX,EAAE;MACNI,MAAM,EAAE2W,gBAAgB,CAACa,UAAU,GAAGlY,UAAU,CAACU,MAAM,GAAG,EAAE;MAC5DmG,kBAAkB,EAAEwQ,gBAAgB,CAACc,sBAAA,GACjCnY,UAAU,CAAC6G,kBAAA,GACX,KAAK;MACT0J,YAAY,EAAE;IACf,EAAC;EACJ,CAAC;EAED,MAAMwH,KAAK,GAA+BA,CAAC1W,UAAU,EAAEgW,gBAAgB,KACrED,MAAM,CACJ5O,UAAU,CAACnH,UAAU,IAChBA,UAAuB,CAACW,WAA2B,IACpDX,UAAU,EACdgW,gBAAgB,CACjB;EAEH,MAAMe,QAAQ,GAAkCA,CAACpe,IAAI,EAAEmQ,OAAO,GAAG,EAAE,KAAI;IACrE,MAAM/G,KAAK,GAAGtH,GAAG,CAACuH,OAAO,EAAErJ,IAAI,CAAC;IAChC,MAAMoS,cAAc,GAAGhJ,KAAK,IAAIA,KAAK,CAACE,EAAE;IAExC,IAAI8I,cAAc,EAAE;MAClB,MAAMmK,QAAQ,GAAGnK,cAAc,CAACpB,IAAA,GAC5BoB,cAAc,CAACpB,IAAI,CAAC,CAAC,IACrBoB,cAAc,CAAClJ,GAAG;MAEtB,IAAIqT,QAAQ,CAAChT,KAAK,EAAE;QAClBgT,QAAQ,CAAChT,KAAK,EAAE;QAChB4G,OAAO,CAACkO,YAAY,IAClB7P,UAAU,CAAC+N,QAAQ,CAAC/S,MAAM,CAAC,IAC3B+S,QAAQ,CAAC/S,MAAM,EAAE;;;EAGzB,CAAC;EAED,MAAMmS,aAAa,GACjBxC,gBAAkD,IAChD;IACFnT,UAAU,GAAG;MACX,GAAGA,UAAU;MACb,GAAGmT;KACJ;EACH,CAAC;EAED,MAAMmF,mBAAmB,GAAGA,CAAA,KAC1B9P,UAAU,CAAC3E,QAAQ,CAACzE,aAAa,CAAC,IACjCyE,QAAQ,CAACzE,aAA0B,EAAE,CAACmZ,IAAI,CAAEzW,MAAoB,IAAI;IACnEiW,KAAK,CAACjW,MAAM,EAAE+B,QAAQ,CAAC2U,YAAY,CAAC;IACpC9R,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MACnBxG,SAAS,EAAE;IACZ,EAAC;EACJ,CAAC,CAAC;EAEJ,MAAMT,OAAO,GAAG;IACdV,OAAO,EAAE;MACPwD,QAAQ;MACRyB,UAAU;MACVkR,aAAa;MACb3P,YAAY;MACZqB,QAAQ;MACRhG,UAAU;MACVuQ,UAAU;MACVpP,SAAS;MACTgQ,SAAS;MACTjR,SAAS;MACT0Q,cAAc;MACdvN,iBAAiB;MACjBgO,UAAU;MACVyB,cAAc;MACdwD,MAAM;MACNkB,mBAAmB;MACnBpW,gBAAgB;MAChByU,YAAY;MACZjQ,SAAS;MACTjH,eAAe;MACf,IAAI4D,OAAOA,CAAA;QACT,OAAOA,OAAO;OACf;MACD,IAAIrB,WAAWA,CAAA;QACb,OAAOA,WAAW;OACnB;MACD,IAAIgC,MAAMA,CAAA;QACR,OAAOA,MAAM;OACd;MACD,IAAIA,MAAMA,CAAC5K,KAAK;QACd4K,MAAM,GAAG5K,KAAK;OACf;MACD,IAAIiG,cAAcA,CAAA;QAChB,OAAOA,cAAc;OACtB;MACD,IAAI+B,MAAMA,CAAA;QACR,OAAOA,MAAM;OACd;MACD,IAAIA,MAAMA,CAAChI,KAAK;QACdgI,MAAM,GAAGhI,KAAK;OACf;MACD,IAAI4G,UAAUA,CAAA;QACZ,OAAOA,UAAU;OAClB;MACD,IAAI6D,QAAQA,CAAA;QACV,OAAOA,QAAQ;OAChB;MACD,IAAIA,QAAQA,CAACzK,KAAK;QAChByK,QAAQ,GAAG;UACT,GAAGA,QAAQ;UACX,GAAGzK;SACJ;;IAEJ;IACDoO,SAAS;IACT2M,OAAO;IACP1R,QAAQ;IACRgD,YAAY;IACZlE,KAAK;IACL+S,QAAQ;IACRX,SAAS;IACToE,KAAK;IACLZ,UAAU;IACV9B,WAAW;IACXnR,UAAU;IACV4C,QAAQ;IACRsR,QAAQ;IACRhD;GACD;EAED,OAAO;IACL,GAAGzV,OAAO;IACV8Y,WAAW,EAAE9Y;GACd;AACH;ACtgDA,IAAA+Y,UAAA,GAAeA,CAAA,KAAK;EAClB,MAAMC,CAAC,GACL,OAAOC,WAAW,KAAK,WAAW,GAAGvf,IAAI,CAACwf,GAAG,EAAE,GAAGD,WAAW,CAACC,GAAG,EAAE,GAAG,IAAI;EAE5E,OAAO,sCAAsC,CAACnc,OAAO,CAAC,OAAO,EAAGoc,CAAC,IAAI;IACnE,MAAMC,CAAC,GAAG,CAACC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAGN,CAAC,IAAI,EAAE,GAAG,CAAC;IAE3C,OAAO,CAACG,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG,EAAEG,QAAQ,CAAC,EAAE,CAAC;EACtD,CAAC,CAAC;AACJ,CAAC;ACND,IAAAC,iBAAA,GAAeA,CACbnf,IAAuB,EACvB4C,KAAa,EACbuN,OAAA,GAAiC,EAAE,KAEnCA,OAAO,CAACgL,WAAW,IAAIxZ,WAAW,CAACwO,OAAO,CAACgL,WAAW,IAClDhL,OAAO,CAACiP,SAAS,IACjB,GAAGpf,IAAI,IAAI2B,WAAW,CAACwO,OAAO,CAACkP,UAAU,CAAC,GAAGzc,KAAK,GAAGuN,OAAO,CAACkP,UAAU,GAAG,GAC1E,EAAE;ACTR,IAAAC,QAAA,GAAeA,CAAIre,IAAS,EAAE7B,KAAc,KAAU,CACpD,GAAG6B,IAAI,EACP,GAAGmM,qBAAqB,CAAChO,KAAK,CAAC,CAChC;ACLD,IAAAmgB,cAAA,GAAmBngB,KAAc,IAC/BK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,CAACqI,GAAG,CAAC,MAAM5F,SAAS,CAAC,GAAGA,SAAS;ACOvC,SAAA2d,MAAMA,CAC5Bve,IAAS,EACT2B,KAAa,EACbxD,KAAe;EAEf,OAAO,CACL,GAAG6B,IAAI,CAACmO,KAAK,CAAC,CAAC,EAAExM,KAAK,CAAC,EACvB,GAAGwK,qBAAqB,CAAChO,KAAK,CAAC,EAC/B,GAAG6B,IAAI,CAACmO,KAAK,CAACxM,KAAK,CAAC,CACrB;AACH;AChBA,IAAA6c,WAAA,GAAeA,CACbxe,IAAuB,EACvBuW,IAAY,EACZkI,EAAU,KACW;EACrB,IAAI,CAACjgB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC,EAAE;IACxB,OAAO,EAAE;;EAGX,IAAIU,WAAW,CAACV,IAAI,CAACye,EAAE,CAAC,CAAC,EAAE;IACzBze,IAAI,CAACye,EAAE,CAAC,GAAG7d,SAAS;;EAEtBZ,IAAI,CAAC0e,MAAM,CAACD,EAAE,EAAE,CAAC,EAAEze,IAAI,CAAC0e,MAAM,CAACnI,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAE3C,OAAOvW,IAAI;AACb,CAAC;ACfD,IAAA2e,SAAA,GAAeA,CAAI3e,IAAS,EAAE7B,KAAc,KAAU,CACpD,GAAGgO,qBAAqB,CAAChO,KAAK,CAAC,EAC/B,GAAGgO,qBAAqB,CAACnM,IAAI,CAAC,CAC/B;ACDD,SAAS4e,eAAeA,CAAI5e,IAAS,EAAE6e,OAAiB;EACtD,IAAIC,CAAC,GAAG,CAAC;EACT,MAAMC,IAAI,GAAG,CAAC,GAAG/e,IAAI,CAAC;EAEtB,KAAK,MAAM2B,KAAK,IAAIkd,OAAO,EAAE;IAC3BE,IAAI,CAACL,MAAM,CAAC/c,KAAK,GAAGmd,CAAC,EAAE,CAAC,CAAC;IACzBA,CAAC,EAAE;;EAGL,OAAOve,OAAO,CAACwe,IAAI,CAAC,CAACld,MAAM,GAAGkd,IAAI,GAAG,EAAE;AACzC;AAEA,IAAAC,aAAA,GAAeA,CAAIhf,IAAS,EAAE2B,KAAyB,KACrDjB,WAAW,CAACiB,KAAK,IACb,KACAid,eAAe,CACb5e,IAAI,EACHmM,qBAAqB,CAACxK,KAAK,CAAc,CAACsd,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CACjE;ACtBP,IAAAC,WAAA,GAAeA,CAAIpf,IAAS,EAAEqf,MAAc,EAAEC,MAAc,KAAU;EACpE,CAACtf,IAAI,CAACqf,MAAM,CAAC,EAAErf,IAAI,CAACsf,MAAM,CAAC,CAAC,GAAG,CAACtf,IAAI,CAACsf,MAAM,CAAC,EAAEtf,IAAI,CAACqf,MAAM,CAAC,CAAC;AAC7D,CAAC;ACFD,IAAAE,QAAA,GAAeA,CAAI1I,WAAgB,EAAElV,KAAa,EAAExD,KAAQ,KAAI;EAC9D0Y,WAAW,CAAClV,KAAK,CAAC,GAAGxD,KAAK;EAC1B,OAAO0Y,WAAW;AACpB,CAAC;;ACuCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCG;AACG,SAAU2I,aAAaA,CAO3B9b,KAKC;EAED,MAAMgB,OAAO,GAAGnB,cAAc,EAAE;EAChC,MAAM;IACJS,OAAO,GAAGU,OAAO,CAACV,OAAO;IACzBjF,IAAI;IACJ0gB,OAAO,GAAG,IAAI;IACdtY,gBAAgB;IAChBM;EAAK,CACN,GAAG/D,KAAK;EACT,MAAM,CAACgL,MAAM,EAAEgR,SAAS,CAAC,GAAGrc,KAAK,CAACyB,QAAQ,CAACd,OAAO,CAAC2U,cAAc,CAAC5Z,IAAI,CAAC,CAAC;EACxE,MAAM4gB,GAAG,GAAGtc,KAAK,CAAC4B,MAAM,CACtBjB,OAAO,CAAC2U,cAAc,CAAC5Z,IAAI,CAAC,CAACyH,GAAG,CAACiX,UAAU,CAAC,CAC7C;EACD,MAAMmC,SAAS,GAAGvc,KAAK,CAAC4B,MAAM,CAACyJ,MAAM,CAAC;EACtC,MAAMhJ,KAAK,GAAGrC,KAAK,CAAC4B,MAAM,CAAClG,IAAI,CAAC;EAChC,MAAM8gB,SAAS,GAAGxc,KAAK,CAAC4B,MAAM,CAAC,KAAK,CAAC;EAErCS,KAAK,CAACC,OAAO,GAAG5G,IAAI;EACpB6gB,SAAS,CAACja,OAAO,GAAG+I,MAAM;EAC1B1K,OAAO,CAACmC,MAAM,CAACkB,KAAK,CAACd,GAAG,CAACxH,IAAI,CAAC;EAE9B0I,KAAK,IACFzD,OAA0D,CAACwD,QAAQ,CAClEzI,IAA+B,EAC/B0I,KAAsC,CACvC;EAEHpE,KAAK,CAACuC,SAAS,CACb,MACE5B,OAAO,CAACyH,SAAS,CAACpE,KAAK,CAACkF,SAAS,CAAC;IAChCZ,IAAI,EAAEA,CAAC;MACL9E,MAAM;MACN9H,IAAI,EAAE+gB;IAAc,CAIrB,KAAI;MACH,IAAIA,cAAc,KAAKpa,KAAK,CAACC,OAAO,IAAI,CAACma,cAAc,EAAE;QACvD,MAAMjJ,WAAW,GAAGhW,GAAG,CAACgG,MAAM,EAAEnB,KAAK,CAACC,OAAO,CAAC;QAC9C,IAAInH,KAAK,CAACC,OAAO,CAACoY,WAAW,CAAC,EAAE;UAC9B6I,SAAS,CAAC7I,WAAW,CAAC;UACtB8I,GAAG,CAACha,OAAO,GAAGkR,WAAW,CAACrQ,GAAG,CAACiX,UAAU,CAAC;;;;EAIhD,EAAC,CAAChR,WAAW,EAChB,CAACzI,OAAO,CAAC,CACV;EAED,MAAM+b,YAAY,GAAG1c,KAAK,CAAC2E,WAAW,CAMlCgY,uBAA0B,IACxB;IACFH,SAAS,CAACla,OAAO,GAAG,IAAI;IACxB3B,OAAO,CAACyS,cAAc,CAAC1X,IAAI,EAAEihB,uBAAuB,CAAC;EACvD,CAAC,EACD,CAAChc,OAAO,EAAEjF,IAAI,CAAC,CAChB;EAED,MAAMiM,MAAM,GAAGA,CACb7M,KAEwD,EACxD+Q,OAA+B,KAC7B;IACF,MAAM+Q,WAAW,GAAG9T,qBAAqB,CAACpM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC7D,MAAM6hB,uBAAuB,GAAG3B,QAAQ,CACtCra,OAAO,CAAC2U,cAAc,CAAC5Z,IAAI,CAAC,EAC5BkhB,WAAW,CACZ;IACDjc,OAAO,CAACmC,MAAM,CAACmC,KAAK,GAAG4V,iBAAiB,CACtCnf,IAAI,EACJihB,uBAAuB,CAACne,MAAM,GAAG,CAAC,EAClCqN,OAAO,CACR;IACDyQ,GAAG,CAACha,OAAO,GAAG0Y,QAAQ,CAACsB,GAAG,CAACha,OAAO,EAAEsa,WAAW,CAACzZ,GAAG,CAACiX,UAAU,CAAC,CAAC;IAChEsC,YAAY,CAACC,uBAAuB,CAAC;IACrCN,SAAS,CAACM,uBAAuB,CAAC;IAClChc,OAAO,CAACyS,cAAc,CAAC1X,IAAI,EAAEihB,uBAAuB,EAAE3B,QAAQ,EAAE;MAC9DvH,IAAI,EAAEwH,cAAc,CAACngB,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAM+hB,OAAO,GAAGA,CACd/hB,KAEwD,EACxD+Q,OAA+B,KAC7B;IACF,MAAMiR,YAAY,GAAGhU,qBAAqB,CAACpM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC9D,MAAM6hB,uBAAuB,GAAGrB,SAAS,CACvC3a,OAAO,CAAC2U,cAAc,CAAC5Z,IAAI,CAAC,EAC5BohB,YAAY,CACb;IACDnc,OAAO,CAACmC,MAAM,CAACmC,KAAK,GAAG4V,iBAAiB,CAACnf,IAAI,EAAE,CAAC,EAAEmQ,OAAO,CAAC;IAC1DyQ,GAAG,CAACha,OAAO,GAAGgZ,SAAS,CAACgB,GAAG,CAACha,OAAO,EAAEwa,YAAY,CAAC3Z,GAAG,CAACiX,UAAU,CAAC,CAAC;IAClEsC,YAAY,CAACC,uBAAuB,CAAC;IACrCN,SAAS,CAACM,uBAAuB,CAAC;IAClChc,OAAO,CAACyS,cAAc,CAAC1X,IAAI,EAAEihB,uBAAuB,EAAErB,SAAS,EAAE;MAC/D7H,IAAI,EAAEwH,cAAc,CAACngB,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAMiiB,MAAM,GAAIze,KAAyB,IAAI;IAC3C,MAAMqe,uBAAuB,GAEvBhB,aAAa,CAAChb,OAAO,CAAC2U,cAAc,CAAC5Z,IAAI,CAAC,EAAE4C,KAAK,CAAC;IACxDge,GAAG,CAACha,OAAO,GAAGqZ,aAAa,CAACW,GAAG,CAACha,OAAO,EAAEhE,KAAK,CAAC;IAC/Coe,YAAY,CAACC,uBAAuB,CAAC;IACrCN,SAAS,CAACM,uBAAuB,CAAC;IAClC,CAACxhB,KAAK,CAACC,OAAO,CAACoC,GAAG,CAACmD,OAAO,CAACoE,OAAO,EAAErJ,IAAI,CAAC,CAAC,IACxC2C,GAAG,CAACsC,OAAO,CAACoE,OAAO,EAAErJ,IAAI,EAAE6B,SAAS,CAAC;IACvCoD,OAAO,CAACyS,cAAc,CAAC1X,IAAI,EAAEihB,uBAAuB,EAAEhB,aAAa,EAAE;MACnElI,IAAI,EAAEnV;IACP,EAAC;EACJ,CAAC;EAED,MAAM0e,QAAM,GAAG9B,CACb5c,KAAa,EACbxD,KAEwD,EACxD+Q,OAA+B,KAC7B;IACF,MAAMoR,WAAW,GAAGnU,qBAAqB,CAACpM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC7D,MAAM6hB,uBAAuB,GAAGzB,MAAQ,CACtCva,OAAO,CAAC2U,cAAc,CAAC5Z,IAAI,CAAC,EAC5B4C,KAAK,EACL2e,WAAW,CACZ;IACDtc,OAAO,CAACmC,MAAM,CAACmC,KAAK,GAAG4V,iBAAiB,CAACnf,IAAI,EAAE4C,KAAK,EAAEuN,OAAO,CAAC;IAC9DyQ,GAAG,CAACha,OAAO,GAAG4Y,MAAQ,CAACoB,GAAG,CAACha,OAAO,EAAEhE,KAAK,EAAE2e,WAAW,CAAC9Z,GAAG,CAACiX,UAAU,CAAC,CAAC;IACvEsC,YAAY,CAACC,uBAAuB,CAAC;IACrCN,SAAS,CAACM,uBAAuB,CAAC;IAClChc,OAAO,CAACyS,cAAc,CAAC1X,IAAI,EAAEihB,uBAAuB,EAAEzB,MAAQ,EAAE;MAC9DzH,IAAI,EAAEnV,KAAK;MACXoV,IAAI,EAAEuH,cAAc,CAACngB,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAMoiB,IAAI,GAAGA,CAAClB,MAAc,EAAEC,MAAc,KAAI;IAC9C,MAAMU,uBAAuB,GAAGhc,OAAO,CAAC2U,cAAc,CAAC5Z,IAAI,CAAC;IAC5DqgB,WAAW,CAACY,uBAAuB,EAAEX,MAAM,EAAEC,MAAM,CAAC;IACpDF,WAAW,CAACO,GAAG,CAACha,OAAO,EAAE0Z,MAAM,EAAEC,MAAM,CAAC;IACxCS,YAAY,CAACC,uBAAuB,CAAC;IACrCN,SAAS,CAACM,uBAAuB,CAAC;IAClChc,OAAO,CAACyS,cAAc,CACpB1X,IAAI,EACJihB,uBAAuB,EACvBZ,WAAW,EACX;MACEtI,IAAI,EAAEuI,MAAM;MACZtI,IAAI,EAAEuI;KACP,EACD,KAAK,CACN;EACH,CAAC;EAED,MAAMkB,IAAI,GAAGA,CAACjK,IAAY,EAAEkI,EAAU,KAAI;IACxC,MAAMuB,uBAAuB,GAAGhc,OAAO,CAAC2U,cAAc,CAAC5Z,IAAI,CAAC;IAC5Dyf,WAAW,CAACwB,uBAAuB,EAAEzJ,IAAI,EAAEkI,EAAE,CAAC;IAC9CD,WAAW,CAACmB,GAAG,CAACha,OAAO,EAAE4Q,IAAI,EAAEkI,EAAE,CAAC;IAClCsB,YAAY,CAACC,uBAAuB,CAAC;IACrCN,SAAS,CAACM,uBAAuB,CAAC;IAClChc,OAAO,CAACyS,cAAc,CACpB1X,IAAI,EACJihB,uBAAuB,EACvBxB,WAAW,EACX;MACE1H,IAAI,EAAEP,IAAI;MACVQ,IAAI,EAAE0H;KACP,EACD,KAAK,CACN;EACH,CAAC;EAED,MAAMgC,MAAM,GAAGA,CACb9e,KAAa,EACbxD,KAAgD,KAC9C;IACF,MAAM2I,WAAW,GAAG/G,WAAW,CAAC5B,KAAK,CAAC;IACtC,MAAM6hB,uBAAuB,GAAGT,QAAQ,CACtCvb,OAAO,CAAC2U,cAAc,CAEpB5Z,IAAI,CAAC,EACP4C,KAAK,EACLmF,WAAwE,CACzE;IACD6Y,GAAG,CAACha,OAAO,GAAG,CAAC,GAAGqa,uBAAuB,CAAC,CAACxZ,GAAG,CAAC,CAACka,IAAI,EAAE5B,CAAC,KACrD,CAAC4B,IAAI,IAAI5B,CAAC,KAAKnd,KAAK,GAAG8b,UAAU,EAAE,GAAGkC,GAAG,CAACha,OAAO,CAACmZ,CAAC,CAAC,CACrD;IACDiB,YAAY,CAACC,uBAAuB,CAAC;IACrCN,SAAS,CAAC,CAAC,GAAGM,uBAAuB,CAAC,CAAC;IACvChc,OAAO,CAACyS,cAAc,CACpB1X,IAAI,EACJihB,uBAAuB,EACvBT,QAAQ,EACR;MACEzI,IAAI,EAAEnV,KAAK;MACXoV,IAAI,EAAEjQ;IACP,GACD,IAAI,EACJ,KAAK,CACN;EACH,CAAC;EAED,MAAMrF,OAAO,GACXtD,KAEwD,IACtD;IACF,MAAM6hB,uBAAuB,GAAG7T,qBAAqB,CAACpM,WAAW,CAAC5B,KAAK,CAAC,CAAC;IACzEwhB,GAAG,CAACha,OAAO,GAAGqa,uBAAuB,CAACxZ,GAAG,CAACiX,UAAU,CAAC;IACrDsC,YAAY,CAAC,CAAC,GAAGC,uBAAuB,CAAC,CAAC;IAC1CN,SAAS,CAAC,CAAC,GAAGM,uBAAuB,CAAC,CAAC;IACvChc,OAAO,CAACyS,cAAc,CACpB1X,IAAI,EACJ,CAAC,GAAGihB,uBAAuB,CAAC,EACxBhgB,IAAO,IAAQA,IAAI,EACvB,EAAE,EACF,IAAI,EACJ,KAAK,CACN;EACH,CAAC;EAEDqD,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB5B,OAAO,CAAC+E,MAAM,CAACC,MAAM,GAAG,KAAK;IAE7BuI,SAAS,CAACxS,IAAI,EAAEiF,OAAO,CAACmC,MAAM,CAAC,IAC7BnC,OAAO,CAACyH,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAC3B,GAAG3H,OAAO,CAACe;IACe,EAAC;IAE/B,IACE8a,SAAS,CAACla,OAAO,KAChB,CAAC+K,kBAAkB,CAAC1M,OAAO,CAAC4E,QAAQ,CAAC+H,IAAI,CAAC,CAACC,UAAU,IACpD5M,OAAO,CAACe,UAAU,CAACyN,WAAW,CAAC,IACjC,CAAC9B,kBAAkB,CAAC1M,OAAO,CAAC4E,QAAQ,CAAC6J,cAAc,CAAC,CAAC7B,UAAU,EAC/D;MACA,IAAI5M,OAAO,CAAC4E,QAAQ,CAACuN,QAAQ,EAAE;QAC7BnS,OAAO,CAACoS,UAAU,CAAC,CAACrX,IAAI,CAAC,CAAC,CAACue,IAAI,CAAErc,MAAM,IAAI;UACzC,MAAM8G,KAAK,GAAGlH,GAAG,CAACI,MAAM,CAACwE,MAAM,EAAE1G,IAAI,CAAC;UACtC,MAAM4hB,aAAa,GAAG9f,GAAG,CAACmD,OAAO,CAACe,UAAU,CAACU,MAAM,EAAE1G,IAAI,CAAC;UAE1D,IACE4hB,aAAA,GACK,CAAC5Y,KAAK,IAAI4Y,aAAa,CAAC1iB,IAAI,IAC5B8J,KAAK,KACH4Y,aAAa,CAAC1iB,IAAI,KAAK8J,KAAK,CAAC9J,IAAI,IAChC0iB,aAAa,CAAClY,OAAO,KAAKV,KAAK,CAACU,OAAO,CAAC,GAC5CV,KAAK,IAAIA,KAAK,CAAC9J,IAAI,EACvB;YACA8J,KAAA,GACIrG,GAAG,CAACsC,OAAO,CAACe,UAAU,CAACU,MAAM,EAAE1G,IAAI,EAAEgJ,KAAK,IAC1CsG,KAAK,CAACrK,OAAO,CAACe,UAAU,CAACU,MAAM,EAAE1G,IAAI,CAAC;YAC1CiF,OAAO,CAACyH,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;cAC3BlG,MAAM,EAAEzB,OAAO,CAACe,UAAU,CAACU;YAC5B,EAAC;;QAEN,CAAC,CAAC;aACG;QACL,MAAM0C,KAAK,GAAUtH,GAAG,CAACmD,OAAO,CAACoE,OAAO,EAAErJ,IAAI,CAAC;QAC/C,IACEoJ,KAAK,IACLA,KAAK,CAACE,EAAE,IACR,EACEqI,kBAAkB,CAAC1M,OAAO,CAAC4E,QAAQ,CAAC6J,cAAc,CAAC,CAAC7B,UAAU,IAC9DF,kBAAkB,CAAC1M,OAAO,CAAC4E,QAAQ,CAAC+H,IAAI,CAAC,CAACC,UAAU,CACrD,EACD;UACAsC,aAAa,CACX/K,KAAK,EACLnE,OAAO,CAACmC,MAAM,CAACxB,QAAQ,EACvBX,OAAO,CAAC+C,WAAW,EACnB/C,OAAO,CAAC4E,QAAQ,CAACuH,YAAY,KAAK7N,eAAe,CAACK,GAAG,EACrDqB,OAAO,CAAC4E,QAAQ,CAACwH,yBAAyB,EAC1C,IAAI,CACL,CAACkN,IAAI,CACHvV,KAAK,IACJ,CAACsF,aAAa,CAACtF,KAAK,CAAC,IACrB/D,OAAO,CAACyH,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;YAC3BlG,MAAM,EAAEkN,yBAAyB,CAC/B3O,OAAO,CAACe,UAAU,CAACU,MAAmC,EACtDsC,KAAK,EACLhJ,IAAI;UAEP,EAAC,CACL;;;;IAKPiF,OAAO,CAACyH,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAC3B5M,IAAI;MACJ8H,MAAM,EAAE9G,WAAW,CAACiE,OAAO,CAAC+C,WAAW;IACxC,EAAC;IAEF/C,OAAO,CAACmC,MAAM,CAACmC,KAAK,IAClBqJ,qBAAqB,CAAC3N,OAAO,CAACoE,OAAO,EAAE,CAACH,GAAG,EAAE3H,GAAW,KAAI;MAC1D,IACE0D,OAAO,CAACmC,MAAM,CAACmC,KAAK,IACpBhI,GAAG,CAACoR,UAAU,CAAC1N,OAAO,CAACmC,MAAM,CAACmC,KAAK,CAAC,IACpCL,GAAG,CAACK,KAAK,EACT;QACAL,GAAG,CAACK,KAAK,EAAE;QACX,OAAO,CAAC;;MAEV;IACF,CAAC,CAAC;IAEJtE,OAAO,CAACmC,MAAM,CAACmC,KAAK,GAAG,EAAE;IAEzBtE,OAAO,CAAC+B,SAAS,EAAE;IACnB8Z,SAAS,CAACla,OAAO,GAAG,KAAK;GAC1B,EAAE,CAAC+I,MAAM,EAAE3P,IAAI,EAAEiF,OAAO,CAAC,CAAC;EAE3BX,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,CAAC/E,GAAG,CAACmD,OAAO,CAAC+C,WAAW,EAAEhI,IAAI,CAAC,IAAIiF,OAAO,CAACyS,cAAc,CAAC1X,IAAI,CAAC;IAE/D,OAAO,MAAK;MACV,MAAM8J,aAAa,GAAGA,CAAC9J,IAAuB,EAAEZ,KAAc,KAAI;QAChE,MAAMgK,KAAK,GAAUtH,GAAG,CAACmD,OAAO,CAACoE,OAAO,EAAErJ,IAAI,CAAC;QAC/C,IAAIoJ,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;UACrBF,KAAK,CAACE,EAAE,CAACS,KAAK,GAAG3K,KAAK;;MAE1B,CAAC;MAED6F,OAAO,CAAC4E,QAAQ,CAACzB,gBAAgB,IAAIA,gBAAA,GACjCnD,OAAO,CAACiF,UAAU,CAAClK,IAA+B,IAClD8J,aAAa,CAAC9J,IAAI,EAAE,KAAK,CAAC;IAChC,CAAC;GACF,EAAE,CAACA,IAAI,EAAEiF,OAAO,EAAEyb,OAAO,EAAEtY,gBAAgB,CAAC,CAAC;EAE9C,OAAO;IACLoZ,IAAI,EAAEld,KAAK,CAAC2E,WAAW,CAACuY,IAAI,EAAE,CAACR,YAAY,EAAEhhB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAC5Dwc,IAAI,EAAEnd,KAAK,CAAC2E,WAAW,CAACwY,IAAI,EAAE,CAACT,YAAY,EAAEhhB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAC5Dkc,OAAO,EAAE7c,KAAK,CAAC2E,WAAW,CAACkY,OAAO,EAAE,CAACH,YAAY,EAAEhhB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAClEgH,MAAM,EAAE3H,KAAK,CAAC2E,WAAW,CAACgD,MAAM,EAAE,CAAC+U,YAAY,EAAEhhB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChEoc,MAAM,EAAE/c,KAAK,CAAC2E,WAAW,CAACoY,MAAM,EAAE,CAACL,YAAY,EAAEhhB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChEua,MAAM,EAAElb,KAAK,CAAC2E,WAAW,CAACqY,QAAM,EAAE,CAACN,YAAY,EAAEhhB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChEyc,MAAM,EAAEpd,KAAK,CAAC2E,WAAW,CAACyY,MAAM,EAAE,CAACV,YAAY,EAAEhhB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAChEvC,OAAO,EAAE4B,KAAK,CAAC2E,WAAW,CAACvG,OAAO,EAAE,CAACse,YAAY,EAAEhhB,IAAI,EAAEiF,OAAO,CAAC,CAAC;IAClE0K,MAAM,EAAErL,KAAK,CAAC2C,OAAO,CACnB,MACE0I,MAAM,CAAClI,GAAG,CAAC,CAAC2B,KAAK,EAAExG,KAAK,MAAM;MAC5B,GAAGwG,KAAK;MACR,CAACsX,OAAO,GAAGE,GAAG,CAACha,OAAO,CAAChE,KAAK,CAAC,IAAI8b,UAAU;IAC5C,EAAC,CAAgE,EACpE,CAAC/O,MAAM,EAAE+Q,OAAO,CAAC;GAEpB;AACH;;ACtbA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACa,SAAAmB,OAAOA,CAKrBld,KAAA,GAAkE,EAAE;EAEpE,MAAMmd,YAAY,GAAGxd,KAAK,CAAC4B,MAAM,CAE/BrE,SAAS,CAAC;EACZ,MAAMkgB,OAAO,GAAGzd,KAAK,CAAC4B,MAAM,CAAsBrE,SAAS,CAAC;EAC5D,MAAM,CAACmD,SAAS,EAAEc,eAAe,CAAC,GAAGxB,KAAK,CAACyB,QAAQ,CAA0B;IAC3EI,OAAO,EAAE,KAAK;IACdK,YAAY,EAAE,KAAK;IACnBJ,SAAS,EAAEoI,UAAU,CAAC7J,KAAK,CAACS,aAAa,CAAC;IAC1CqO,WAAW,EAAE,KAAK;IAClB8C,YAAY,EAAE,KAAK;IACnB1J,kBAAkB,EAAE,KAAK;IACzBpG,OAAO,EAAE,KAAK;IACd6P,WAAW,EAAE,CAAC;IACdjQ,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBG,MAAM,EAAE/B,KAAK,CAAC+B,MAAM,IAAI,EAAE;IAC1Bd,QAAQ,EAAEjB,KAAK,CAACiB,QAAQ,IAAI,KAAK;IACjCR,aAAa,EAAEoJ,UAAU,CAAC7J,KAAK,CAACS,aAAa,IACzCvD,SAAA,GACA8C,KAAK,CAACS;EACX,EAAC;EAEF,IAAI,CAAC0c,YAAY,CAAClb,OAAO,EAAE;IACzBkb,YAAY,CAAClb,OAAO,GAAG;MACrB,IAAIjC,KAAK,CAAC8Z,WAAW,GAAG9Z,KAAK,CAAC8Z,WAAW,GAAGpI,iBAAiB,CAAC1R,KAAK,CAAC,CAAC;MACrEK;KACD;IAED,IACEL,KAAK,CAAC8Z,WAAW,IACjB9Z,KAAK,CAACS,aAAa,IACnB,CAACoJ,UAAU,CAAC7J,KAAK,CAACS,aAAa,CAAC,EAChC;MACAT,KAAK,CAAC8Z,WAAW,CAACV,KAAK,CAACpZ,KAAK,CAACS,aAAa,EAAET,KAAK,CAAC6Z,YAAY,CAAC;;;EAIpE,MAAMvZ,OAAO,GAAG6c,YAAY,CAAClb,OAAO,CAAC3B,OAAO;EAC5CA,OAAO,CAAC4E,QAAQ,GAAGlF,KAAK;EAExBL,KAAK,CAAC0d,eAAe,CACnB,MACE/c,OAAO,CAAC6B,UAAU,CAAC;IACjB9B,SAAS,EAAEC,OAAO,CAACQ,eAAe;IAClCsB,QAAQ,EAAEA,CAAA,KAAMjB,eAAe,CAAC;MAAE,GAAGb,OAAO,CAACe;IAAU,CAAE,CAAC;IAC1D4V,YAAY,EAAE;EACf,EAAC,EACJ,CAAC3W,OAAO,CAAC,CACV;EAEDX,KAAK,CAACuC,SAAS,CACb,MAAM5B,OAAO,CAAC0X,YAAY,CAAChY,KAAK,CAACiB,QAAQ,CAAC,EAC1C,CAACX,OAAO,EAAEN,KAAK,CAACiB,QAAQ,CAAC,CAC1B;EAEDtB,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,IAAI5B,OAAO,CAACQ,eAAe,CAACU,OAAO,EAAE;MACnC,MAAMA,OAAO,GAAGlB,OAAO,CAACgT,SAAS,EAAE;MACnC,IAAI9R,OAAO,KAAKnB,SAAS,CAACmB,OAAO,EAAE;QACjClB,OAAO,CAACyH,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;UAC3BzG;QACD,EAAC;;;GAGP,EAAE,CAAClB,OAAO,EAAED,SAAS,CAACmB,OAAO,CAAC,CAAC;EAEhC7B,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,IAAIlC,KAAK,CAACmD,MAAM,IAAI,CAACgG,SAAS,CAACnJ,KAAK,CAACmD,MAAM,EAAEia,OAAO,CAACnb,OAAO,CAAC,EAAE;MAC7D3B,OAAO,CAACmY,MAAM,CAACzY,KAAK,CAACmD,MAAM,EAAE7C,OAAO,CAAC4E,QAAQ,CAAC2U,YAAY,CAAC;MAC3DuD,OAAO,CAACnb,OAAO,GAAGjC,KAAK,CAACmD,MAAM;MAC9BhC,eAAe,CAAE6G,KAAK,KAAM;QAAE,GAAGA;MAAK,CAAE,CAAC,CAAC;WACrC;MACL1H,OAAO,CAACqZ,mBAAmB,EAAE;;GAEhC,EAAE,CAAC3Z,KAAK,CAACmD,MAAM,EAAE7C,OAAO,CAAC,CAAC;EAE3BX,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,IAAIlC,KAAK,CAAC+B,MAAM,IAAI,CAAC4H,aAAa,CAAC3J,KAAK,CAAC+B,MAAM,CAAC,EAAE;MAChDzB,OAAO,CAACkT,UAAU,CAACxT,KAAK,CAAC+B,MAAM,CAAC;;GAEnC,EAAE,CAAC/B,KAAK,CAAC+B,MAAM,EAAEzB,OAAO,CAAC,CAAC;EAE3BX,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,IAAI,CAAC5B,OAAO,CAAC+E,MAAM,CAACD,KAAK,EAAE;MACzB9E,OAAO,CAAC+B,SAAS,EAAE;MACnB/B,OAAO,CAAC+E,MAAM,CAACD,KAAK,GAAG,IAAI;;IAG7B,IAAI9E,OAAO,CAAC+E,MAAM,CAACzC,KAAK,EAAE;MACxBtC,OAAO,CAAC+E,MAAM,CAACzC,KAAK,GAAG,KAAK;MAC5BtC,OAAO,CAACyH,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;QAAE,GAAG3H,OAAO,CAACe;MAAU,CAAE,CAAC;;IAGzDf,OAAO,CAACiD,gBAAgB,EAAE;EAC5B,CAAC,CAAC;EAEF5D,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnBlC,KAAK,CAACyD,gBAAgB,IACpBnD,OAAO,CAACyH,SAAS,CAACC,KAAK,CAACC,IAAI,CAAC;MAC3B9E,MAAM,EAAE7C,OAAO,CAACgD,SAAS;IAC1B,EAAC;GACL,EAAE,CAACtD,KAAK,CAACyD,gBAAgB,EAAEnD,OAAO,CAAC,CAAC;EAErC6c,YAAY,CAAClb,OAAO,CAAC5B,SAAS,GAAGD,iBAAiB,CAACC,SAAS,EAAEC,OAAO,CAAC;EAEtE,OAAO6c,YAAY,CAAClb,OAAO;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}