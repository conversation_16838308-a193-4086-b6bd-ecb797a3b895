{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Layout\\\\Header\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport \"./Header.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Header({\n  user,\n  onLogin,\n  onLogout\n}) {\n  _s();\n  const navigate = useNavigate();\n  const handleLogin = () => {\n    navigate(\"/login\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header__left\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header__center\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"header__nav-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__icon\",\n          children: \"\\uD83C\\uDFE0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__text\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/about\",\n        className: \"header__nav-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__icon\",\n          children: \"\\u2139\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__text\",\n          children: \"About\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/posts\",\n        className: \"header__nav-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__icon\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__text\",\n          children: \"Post\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header__right\",\n      children: user ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header__user\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: `/profile/${user.id}`,\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: user.avatar,\n            alt: \"Avatar\",\n            className: \"header__avatar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__greeting\",\n          children: [\"Hi \", user.firstname]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"header__logout-btn\",\n          onClick: onLogout,\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header__login\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__login-text\",\n          onClick: handleLogin,\n          children: \"Please Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_s(Header, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "Header", "user", "onLogin", "onLogout", "_s", "navigate", "handleLogin", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "id", "src", "avatar", "alt", "firstname", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Layout/Header/Header.js"], "sourcesContent": ["import React from \"react\";\r\nimport { <PERSON>, useNavigate } from \"react-router-dom\";\r\nimport \"./Header.css\";\r\n\r\nfunction Header({ user, onLogin, onLogout }) {\r\n  const navigate = useNavigate();\r\n\r\n  const handleLogin = () => {\r\n    navigate(\"/login\");\r\n  };\r\n\r\n  return (\r\n    <header className=\"header\">\r\n      <div className=\"header__left\"></div>\r\n\r\n      <div className=\"header__center\">\r\n        <Link to=\"/\" className=\"header__nav-item\">\r\n          <span className=\"header__icon\">🏠</span>\r\n          <span className=\"header__text\">Home</span>\r\n        </Link>\r\n        <Link to=\"/about\" className=\"header__nav-item\">\r\n          <span className=\"header__icon\">ℹ️</span>\r\n          <span className=\"header__text\">About</span>\r\n        </Link>\r\n        <Link to=\"/posts\" className=\"header__nav-item\">\r\n          <span className=\"header__icon\">📝</span>\r\n          <span className=\"header__text\">Post</span>\r\n        </Link>\r\n      </div>\r\n\r\n      <div className=\"header__right\">\r\n        {user ? (\r\n          <div className=\"header__user\">\r\n            <Link to={`/profile/${user.id}`}>\r\n              <img src={user.avatar} alt=\"Avatar\" className=\"header__avatar\" />\r\n            </Link>\r\n            <span className=\"header__greeting\">Hi {user.firstname}</span>\r\n            <button className=\"header__logout-btn\" onClick={onLogout}>\r\n              Logout\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"header__login\">\r\n            <span className=\"header__login-text\" onClick={handleLogin}>\r\n              Please Login\r\n            </span>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n\r\nexport default Header;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,SAASC,MAAMA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAC3C,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACxBD,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEN,OAAA;IAAQQ,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACxBT,OAAA;MAAKQ,SAAS,EAAC;IAAc;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEpCb,OAAA;MAAKQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BT,OAAA,CAACH,IAAI;QAACiB,EAAE,EAAC,GAAG;QAACN,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACvCT,OAAA;UAAMQ,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxCb,OAAA;UAAMQ,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACPb,OAAA,CAACH,IAAI;QAACiB,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC5CT,OAAA;UAAMQ,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxCb,OAAA;UAAMQ,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACPb,OAAA,CAACH,IAAI;QAACiB,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC5CT,OAAA;UAAMQ,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxCb,OAAA;UAAMQ,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENb,OAAA;MAAKQ,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BP,IAAI,gBACHF,OAAA;QAAKQ,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BT,OAAA,CAACH,IAAI;UAACiB,EAAE,EAAE,YAAYZ,IAAI,CAACa,EAAE,EAAG;UAAAN,QAAA,eAC9BT,OAAA;YAAKgB,GAAG,EAAEd,IAAI,CAACe,MAAO;YAACC,GAAG,EAAC,QAAQ;YAACV,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACPb,OAAA;UAAMQ,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,KAAG,EAACP,IAAI,CAACiB,SAAS;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7Db,OAAA;UAAQQ,SAAS,EAAC,oBAAoB;UAACY,OAAO,EAAEhB,QAAS;UAAAK,QAAA,EAAC;QAE1D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAENb,OAAA;QAAKQ,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BT,OAAA;UAAMQ,SAAS,EAAC,oBAAoB;UAACY,OAAO,EAAEb,WAAY;UAAAE,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACR,EAAA,CA/CQJ,MAAM;EAAA,QACIH,WAAW;AAAA;AAAAuB,EAAA,GADrBpB,MAAM;AAiDf,eAAeA,MAAM;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}