{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Common\\\\Modal\\\\Modal.js\";\nimport React from \"react\";\nimport \"./Modal.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Modal({\n  children,\n  onClose\n}) {\n  const handleBackdropClick = e => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal\",\n    onClick: handleBackdropClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal__content\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"modal__close-btn\",\n        onClick: onClose,\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n}\n_c = Modal;\nexport default Modal;\nvar _c;\n$RefreshReg$(_c, \"Modal\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Modal", "children", "onClose", "handleBackdropClick", "e", "target", "currentTarget", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Common/Modal/Modal.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./Modal.css\";\r\n\r\nfunction Modal({ children, onClose }) {\r\n  const handleBackdropClick = (e) => {\r\n    if (e.target === e.currentTarget) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"modal\" onClick={handleBackdropClick}>\r\n      <div className=\"modal__content\">\r\n        <button className=\"modal__close-btn\" onClick={onClose}>\r\n          ✕\r\n        </button>\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Modal;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,SAASC,KAAKA,CAAC;EAAEC,QAAQ;EAAEC;AAAQ,CAAC,EAAE;EACpC,MAAMC,mBAAmB,GAAIC,CAAC,IAAK;IACjC,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAChCJ,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEH,OAAA;IAAKQ,SAAS,EAAC,OAAO;IAACC,OAAO,EAAEL,mBAAoB;IAAAF,QAAA,eAClDF,OAAA;MAAKQ,SAAS,EAAC,gBAAgB;MAAAN,QAAA,gBAC7BF,OAAA;QAAQQ,SAAS,EAAC,kBAAkB;QAACC,OAAO,EAAEN,OAAQ;QAAAD,QAAA,EAAC;MAEvD;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRX,QAAQ;IAAA;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GAjBQb,KAAK;AAmBd,eAAeA,KAAK;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}