.create-post-modal {
  width: 500px;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.create-post-modal__header {
  padding: 16px;
  border-bottom: 1px solid #e4e6ea;
  text-align: center;
}

.create-post-modal__title {
  font-size: 20px;
  font-weight: 700;
  color: #1c1e21;
  margin: 0;
}

.create-post-modal__user {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
}

.create-post-modal__avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.create-post-modal__name {
  font-size: 15px;
  font-weight: 600;
  color: #1c1e21;
}

.create-post-modal__form {
  padding: 0 16px 16px;
}

.create-post-modal__textarea {
  width: 100%;
  border: none;
  outline: none;
  font-size: 24px;
  color: #1c1e21;
  resize: none;
  margin-bottom: 16px;
  font-family: inherit;
}

.create-post-modal__textarea::placeholder {
  color: #65676b;
}

.create-post-modal__image-preview {
  position: relative;
  margin-bottom: 16px;
}

.create-post-modal__remove-image {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.create-post-modal__preview-img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.create-post-modal__image-upload {
  margin-bottom: 16px;
}

.create-post-modal__upload-label {
  display: block;
  cursor: pointer;
}

.create-post-modal__upload-input {
  display: none;
}

.create-post-modal__upload-area {
  border: 2px dashed #e4e6ea;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  transition: border-color 0.2s;
}

.create-post-modal__upload-area:hover {
  border-color: #1877f2;
}

.create-post-modal__upload-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 8px;
}

.create-post-modal__upload-text {
  font-size: 20px;
  font-weight: 600;
  color: #1c1e21;
  display: block;
  margin-bottom: 4px;
}

.create-post-modal__upload-subtext {
  font-size: 15px;
  color: #65676b;
}

.create-post-modal__submit {
  width: 100%;
  background-color: #1877f2;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-post-modal__submit:hover:not(:disabled) {
  background-color: #166fe5;
}

.create-post-modal__submit:disabled {
  background-color: #e4e6ea;
  color: #bcc0c4;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .create-post-modal {
    width: 90vw;
  }
}