{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\Home\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport \"./Home.css\";\nimport Header from \"../../Components/Layout/Header/Header\";\nimport UserList from \"../../Components/Layout/Sidebar/UserList\";\nimport PostList from \"../../Components/Post/PostList/PostList\";\nimport PostDetail from \"../../Components/Post/PostDetail/PostDetail\";\nimport CreatePost from \"../../Components/Post/CreatePost/CreatePost\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Home({\n  user: propUser,\n  onLogout\n}) {\n  _s();\n  const [user, setUser] = useState(propUser);\n  const [selectedPost, setSelectedPost] = useState(null);\n  const [showPostDetail, setShowPostDetail] = useState(false);\n  const [posts, setPosts] = useState([]);\n  useEffect(() => {\n    // Cập nhật user từ props hoặc localStorage\n    if (propUser) {\n      setUser(propUser);\n    } else {\n      const storedUser = localStorage.getItem(\"user\");\n      if (storedUser) {\n        setUser(JSON.parse(storedUser));\n      }\n    }\n\n    // Load posts\n    const storedPosts = localStorage.getItem(\"posts\");\n    if (storedPosts) {\n      setPosts(JSON.parse(storedPosts));\n    } else {\n      const defaultPosts = [{\n        id: 1,\n        caption: \"Cảnh đẹp thiên nhiên tuyệt vời! 🌅\",\n        image: \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500&h=400&fit=crop\",\n        author: {\n          id: 1,\n          name: \"Nguyễn Văn A\",\n          avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n        },\n        comments: [{\n          id: 1,\n          content: \"Đẹp quá!\",\n          author: \"Mai Anh\"\n        }, {\n          id: 2,\n          content: \"Chụp ở đâu vậy bạn?\",\n          author: \"Tuấn Anh\"\n        }]\n      }, {\n        id: 2,\n        caption: \"Buổi sáng tuyệt vời với tách cà phê ☕\",\n        image: \"https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=500&h=400&fit=crop\",\n        author: {\n          id: 2,\n          name: \"Trần Thị B\",\n          avatar: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face\"\n        },\n        comments: [{\n          id: 3,\n          content: \"Nhìn ngon quá!\",\n          author: \"Hương Giang\"\n        }]\n      }];\n      setPosts(defaultPosts);\n      localStorage.setItem(\"posts\", JSON.stringify(defaultPosts));\n    }\n  }, [propUser]);\n  const handleLogin = userData => {\n    setUser(userData);\n  };\n  const handleLogout = () => {\n    setUser(null);\n    if (onLogout) {\n      onLogout();\n    }\n  };\n  const handleOpenPostDetail = post => {\n    setSelectedPost(post);\n    setShowPostDetail(true);\n  };\n  const handleClosePostDetail = () => {\n    setShowPostDetail(false);\n    setSelectedPost(null);\n  };\n  const handleCreatePost = newPost => {\n    const updatedPosts = [newPost, ...posts];\n    setPosts(updatedPosts);\n    localStorage.setItem(\"posts\", JSON.stringify(updatedPosts));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      user: user,\n      onLogin: handleLogin,\n      onLogout: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home__content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home__sidebar\",\n        children: user && /*#__PURE__*/_jsxDEV(UserList, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home__main\",\n        children: [/*#__PURE__*/_jsxDEV(CreatePost, {\n          user: user,\n          onCreatePost: handleCreatePost\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PostList, {\n          posts: posts,\n          onOpenPostDetail: handleOpenPostDetail\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home__right\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), showPostDetail && selectedPost && /*#__PURE__*/_jsxDEV(PostDetail, {\n      post: selectedPost,\n      onClose: handleClosePostDetail\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n}\n_s(Home, \"2CN5UoNi2u08x/6NedVa2eceti4=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Header", "UserList", "PostList", "PostDetail", "CreatePost", "jsxDEV", "_jsxDEV", "Home", "user", "propUser", "onLogout", "_s", "setUser", "selectedPost", "setSelectedPost", "showPostDetail", "setShowPostDetail", "posts", "setPosts", "storedUser", "localStorage", "getItem", "JSON", "parse", "storedPosts", "defaultPosts", "id", "caption", "image", "author", "name", "avatar", "comments", "content", "setItem", "stringify", "handleLogin", "userData", "handleLogout", "handleOpenPostDetail", "post", "handleClosePostDetail", "handleCreatePost", "newPost", "updatedPosts", "className", "children", "onLogin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onCreatePost", "onOpenPostDetail", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/Home/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport \"./Home.css\";\r\nimport Header from \"../../Components/Layout/Header/Header\";\r\nimport UserList from \"../../Components/Layout/Sidebar/UserList\";\r\nimport PostList from \"../../Components/Post/PostList/PostList\";\r\nimport PostDetail from \"../../Components/Post/PostDetail/PostDetail\";\r\nimport CreatePost from \"../../Components/Post/CreatePost/CreatePost\";\r\n\r\nfunction Home({ user: propUser, onLogout }) {\r\n  const [user, setUser] = useState(propUser);\r\n  const [selectedPost, setSelectedPost] = useState(null);\r\n  const [showPostDetail, setShowPostDetail] = useState(false);\r\n  const [posts, setPosts] = useState([]);\r\n\r\n  useEffect(() => {\r\n    // Cập nhật user từ props hoặc localStorage\r\n    if (propUser) {\r\n      setUser(propUser);\r\n    } else {\r\n      const storedUser = localStorage.getItem(\"user\");\r\n      if (storedUser) {\r\n        setUser(JSON.parse(storedUser));\r\n      }\r\n    }\r\n\r\n    // Load posts\r\n    const storedPosts = localStorage.getItem(\"posts\");\r\n    if (storedPosts) {\r\n      setPosts(JSON.parse(storedPosts));\r\n    } else {\r\n      const defaultPosts = [\r\n        {\r\n          id: 1,\r\n          caption: \"Cảnh đẹp thiên nhiên tuyệt vời! 🌅\",\r\n          image:\r\n            \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500&h=400&fit=crop\",\r\n          author: {\r\n            id: 1,\r\n            name: \"Nguyễn Văn A\",\r\n            avatar:\r\n              \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\r\n          },\r\n          comments: [\r\n            { id: 1, content: \"Đẹp quá!\", author: \"Mai Anh\" },\r\n            { id: 2, content: \"Chụp ở đâu vậy bạn?\", author: \"Tuấn Anh\" },\r\n          ],\r\n        },\r\n        {\r\n          id: 2,\r\n          caption: \"Buổi sáng tuyệt vời với tách cà phê ☕\",\r\n          image:\r\n            \"https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=500&h=400&fit=crop\",\r\n          author: {\r\n            id: 2,\r\n            name: \"Trần Thị B\",\r\n            avatar:\r\n              \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face\",\r\n          },\r\n          comments: [\r\n            { id: 3, content: \"Nhìn ngon quá!\", author: \"Hương Giang\" },\r\n          ],\r\n        },\r\n      ];\r\n      setPosts(defaultPosts);\r\n      localStorage.setItem(\"posts\", JSON.stringify(defaultPosts));\r\n    }\r\n  }, [propUser]);\r\n\r\n  const handleLogin = (userData) => {\r\n    setUser(userData);\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    setUser(null);\r\n    if (onLogout) {\r\n      onLogout();\r\n    }\r\n  };\r\n\r\n  const handleOpenPostDetail = (post) => {\r\n    setSelectedPost(post);\r\n    setShowPostDetail(true);\r\n  };\r\n\r\n  const handleClosePostDetail = () => {\r\n    setShowPostDetail(false);\r\n    setSelectedPost(null);\r\n  };\r\n\r\n  const handleCreatePost = (newPost) => {\r\n    const updatedPosts = [newPost, ...posts];\r\n    setPosts(updatedPosts);\r\n    localStorage.setItem(\"posts\", JSON.stringify(updatedPosts));\r\n  };\r\n\r\n  return (\r\n    <div className=\"home\">\r\n      <Header user={user} onLogin={handleLogin} onLogout={handleLogout} />\r\n\r\n      <div className=\"home__content\">\r\n        <div className=\"home__sidebar\">{user && <UserList />}</div>\r\n\r\n        <div className=\"home__main\">\r\n          <CreatePost user={user} onCreatePost={handleCreatePost} />\r\n          <PostList posts={posts} onOpenPostDetail={handleOpenPostDetail} />\r\n        </div>\r\n\r\n        <div className=\"home__right\"></div>\r\n      </div>\r\n\r\n      {showPostDetail && selectedPost && (\r\n        <PostDetail post={selectedPost} onClose={handleClosePostDetail} />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,YAAY;AACnB,OAAOC,MAAM,MAAM,uCAAuC;AAC1D,OAAOC,QAAQ,MAAM,0CAA0C;AAC/D,OAAOC,QAAQ,MAAM,yCAAyC;AAC9D,OAAOC,UAAU,MAAM,6CAA6C;AACpE,OAAOC,UAAU,MAAM,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,SAASC,IAAIA,CAAC;EAAEC,IAAI,EAAEC,QAAQ;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAC1C,MAAM,CAACH,IAAI,EAAEI,OAAO,CAAC,GAAGd,QAAQ,CAACW,QAAQ,CAAC;EAC1C,MAAM,CAACI,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd;IACA,IAAIU,QAAQ,EAAE;MACZG,OAAO,CAACH,QAAQ,CAAC;IACnB,CAAC,MAAM;MACL,MAAMU,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACdP,OAAO,CAACU,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,CAAC;MACjC;IACF;;IAEA;IACA,MAAMK,WAAW,GAAGJ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IACjD,IAAIG,WAAW,EAAE;MACfN,QAAQ,CAACI,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC;IACnC,CAAC,MAAM;MACL,MAAMC,YAAY,GAAG,CACnB;QACEC,EAAE,EAAE,CAAC;QACLC,OAAO,EAAE,oCAAoC;QAC7CC,KAAK,EACH,mFAAmF;QACrFC,MAAM,EAAE;UACNH,EAAE,EAAE,CAAC;UACLI,IAAI,EAAE,cAAc;UACpBC,MAAM,EACJ;QACJ,CAAC;QACDC,QAAQ,EAAE,CACR;UAAEN,EAAE,EAAE,CAAC;UAAEO,OAAO,EAAE,UAAU;UAAEJ,MAAM,EAAE;QAAU,CAAC,EACjD;UAAEH,EAAE,EAAE,CAAC;UAAEO,OAAO,EAAE,qBAAqB;UAAEJ,MAAM,EAAE;QAAW,CAAC;MAEjE,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLC,OAAO,EAAE,uCAAuC;QAChDC,KAAK,EACH,mFAAmF;QACrFC,MAAM,EAAE;UACNH,EAAE,EAAE,CAAC;UACLI,IAAI,EAAE,YAAY;UAClBC,MAAM,EACJ;QACJ,CAAC;QACDC,QAAQ,EAAE,CACR;UAAEN,EAAE,EAAE,CAAC;UAAEO,OAAO,EAAE,gBAAgB;UAAEJ,MAAM,EAAE;QAAc,CAAC;MAE/D,CAAC,CACF;MACDX,QAAQ,CAACO,YAAY,CAAC;MACtBL,YAAY,CAACc,OAAO,CAAC,OAAO,EAAEZ,IAAI,CAACa,SAAS,CAACV,YAAY,CAAC,CAAC;IAC7D;EACF,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;EAEd,MAAM2B,WAAW,GAAIC,QAAQ,IAAK;IAChCzB,OAAO,CAACyB,QAAQ,CAAC;EACnB,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB1B,OAAO,CAAC,IAAI,CAAC;IACb,IAAIF,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAM6B,oBAAoB,GAAIC,IAAI,IAAK;IACrC1B,eAAe,CAAC0B,IAAI,CAAC;IACrBxB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyB,qBAAqB,GAAGA,CAAA,KAAM;IAClCzB,iBAAiB,CAAC,KAAK,CAAC;IACxBF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4B,gBAAgB,GAAIC,OAAO,IAAK;IACpC,MAAMC,YAAY,GAAG,CAACD,OAAO,EAAE,GAAG1B,KAAK,CAAC;IACxCC,QAAQ,CAAC0B,YAAY,CAAC;IACtBxB,YAAY,CAACc,OAAO,CAAC,OAAO,EAAEZ,IAAI,CAACa,SAAS,CAACS,YAAY,CAAC,CAAC;EAC7D,CAAC;EAED,oBACEtC,OAAA;IAAKuC,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBxC,OAAA,CAACN,MAAM;MAACQ,IAAI,EAAEA,IAAK;MAACuC,OAAO,EAAEX,WAAY;MAAC1B,QAAQ,EAAE4B;IAAa;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEpE7C,OAAA;MAAKuC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BxC,OAAA;QAAKuC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEtC,IAAI,iBAAIF,OAAA,CAACL,QAAQ;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE3D7C,OAAA;QAAKuC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxC,OAAA,CAACF,UAAU;UAACI,IAAI,EAAEA,IAAK;UAAC4C,YAAY,EAAEV;QAAiB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1D7C,OAAA,CAACJ,QAAQ;UAACe,KAAK,EAAEA,KAAM;UAACoC,gBAAgB,EAAEd;QAAqB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAEN7C,OAAA;QAAKuC,SAAS,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,EAELpC,cAAc,IAAIF,YAAY,iBAC7BP,OAAA,CAACH,UAAU;MAACqC,IAAI,EAAE3B,YAAa;MAACyC,OAAO,EAAEb;IAAsB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAClE;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACxC,EAAA,CA3GQJ,IAAI;AAAAgD,EAAA,GAAJhD,IAAI;AA6Gb,eAAeA,IAAI;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}