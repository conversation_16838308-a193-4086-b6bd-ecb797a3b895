.comment-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.comment-item__avatar-img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.comment-item__content {
  flex: 1;
}

.comment-item__bubble {
  background-color: #f0f2f5;
  border-radius: 16px;
  padding: 8px 12px;
  display: inline-block;
  max-width: 100%;
}

.comment-item__author {
  font-size: 13px;
  font-weight: 600;
  color: #1c1e21;
  display: block;
  margin-bottom: 2px;
}

.comment-item__text {
  margin: 0;
  font-size: 14px;
  color: #1c1e21;
  line-height: 1.3;
}