{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\ProtectedRoute.js\";\nimport { Navigate } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  user,\n  children\n}) {\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n}\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["Navigate", "jsxDEV", "_jsxDEV", "ProtectedRoute", "user", "children", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/ProtectedRoute.js"], "sourcesContent": ["import { Navigate } from \"react-router-dom\";\r\n\r\nfunction ProtectedRoute({ user, children }) {\r\n  if (!user) {\r\n    return <Navigate to=\"/login\" replace />;\r\n  }\r\n  return children;\r\n}\r\nexport default ProtectedRoute;\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,SAASC,cAAcA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,EAAE;EAC1C,IAAI,CAACD,IAAI,EAAE;IACT,oBAAOF,OAAA,CAACF,QAAQ;MAACM,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EACA,OAAON,QAAQ;AACjB;AAACO,EAAA,GALQT,cAAc;AAMvB,eAAeA,cAAc;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}