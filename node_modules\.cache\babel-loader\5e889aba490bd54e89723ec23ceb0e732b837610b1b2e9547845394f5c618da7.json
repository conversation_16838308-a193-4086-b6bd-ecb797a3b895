{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Layout\\\\Sidebar\\\\UserList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport \"./UserList.css\";\nimport UserItem from \"../../User/UserItem/UserItem\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction UserList() {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // API để lấy danh sách người dùng\n  const getUserList = async () => {\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      setUsers([]);\n      return;\n    }\n    setLoading(true);\n    setError(\"\");\n    try {\n      const response = await fetch(\"http://localhost:8081/api/user/list\", {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (!response.ok) {\n        throw new Error(\"Không thể tải danh sách người dùng\");\n      }\n      const data = await response.json();\n\n      // Transform API data\n      const transformedUsers = data.map(user => ({\n        id: user._id,\n        name: `${user.first_name} ${user.last_name}`,\n        avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n      }));\n      setUsers(transformedUsers);\n    } catch (error) {\n      console.error(\"Error loading users:\", error);\n      setError(\"Không thể tải danh sách người dùng\");\n      setUsers([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    getUserList();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-list\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"user-list__title\",\n        children: \"Danh s\\xE1ch li\\xEAn h\\u1EC7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-list__loading\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u0110ang t\\u1EA3i...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-list\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"user-list__title\",\n        children: \"Danh s\\xE1ch li\\xEAn h\\u1EC7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-list__error\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: getUserList,\n          className: \"user-list__retry-btn\",\n          children: \"Th\\u1EED l\\u1EA1i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-list\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"user-list__title\",\n      children: \"Danh s\\xE1ch li\\xEAn h\\u1EC7\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-list__items\",\n      children: users.length > 0 ? users.map(user => /*#__PURE__*/_jsxDEV(UserItem, {\n        user: user\n      }, user.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 31\n      }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"user-list__empty\",\n        children: \"Kh\\xF4ng c\\xF3 ng\\u01B0\\u1EDDi d\\xF9ng n\\xE0o\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n}\n_s(UserList, \"jbqMkoeUOQ80BhXw5fkwMpm3ADs=\");\n_c = UserList;\nexport default UserList;\nvar _c;\n$RefreshReg$(_c, \"UserList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "UserItem", "jsxDEV", "_jsxDEV", "UserList", "_s", "users", "setUsers", "loading", "setLoading", "error", "setError", "getUserList", "token", "localStorage", "getItem", "response", "fetch", "method", "headers", "Authorization", "ok", "Error", "data", "json", "transformedUsers", "map", "user", "id", "_id", "name", "first_name", "last_name", "avatar", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Layout/Sidebar/UserList.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport \"./UserList.css\";\r\nimport UserItem from \"../../User/UserItem/UserItem\";\r\n\r\nfunction UserList() {\r\n  const [users, setUsers] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(\"\");\r\n\r\n  // API để lấy danh sách người dùng\r\n  const getUserList = async () => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (!token) {\r\n      setUsers([]);\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(\"\");\r\n\r\n    try {\r\n      const response = await fetch(\"http://localhost:8081/api/user/list\", {\r\n        method: \"GET\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(\"Không thể tải danh sách người dùng\");\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      // Transform API data\r\n      const transformedUsers = data.map((user) => ({\r\n        id: user._id,\r\n        name: `${user.first_name} ${user.last_name}`,\r\n        avatar:\r\n          \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\r\n      }));\r\n\r\n      setUsers(transformedUsers);\r\n    } catch (error) {\r\n      console.error(\"Error loading users:\", error);\r\n      setError(\"Không thể tải danh sách người dùng\");\r\n      setUsers([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getUserList();\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"user-list\">\r\n        <h3 className=\"user-list__title\">Danh sách liên hệ</h3>\r\n        <div className=\"user-list__loading\">\r\n          <p>Đang tải...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"user-list\">\r\n        <h3 className=\"user-list__title\">Danh sách liên hệ</h3>\r\n        <div className=\"user-list__error\">\r\n          <p>{error}</p>\r\n          <button onClick={getUserList} className=\"user-list__retry-btn\">\r\n            Thử lại\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"user-list\">\r\n      <h3 className=\"user-list__title\">Danh sách liên hệ</h3>\r\n      <div className=\"user-list__items\">\r\n        {users.length > 0 ? (\r\n          users.map((user) => <UserItem key={user.id} user={user} />)\r\n        ) : (\r\n          <p className=\"user-list__empty\">Không có người dùng nào</p>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,gBAAgB;AACvB,OAAOC,QAAQ,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAMa,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVN,QAAQ,CAAC,EAAE,CAAC;MACZ;IACF;IAEAE,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUP,KAAK;QAChC;MACF,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACK,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,oCAAoC,CAAC;MACvD;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;;MAElC;MACA,MAAMC,gBAAgB,GAAGF,IAAI,CAACG,GAAG,CAAEC,IAAI,KAAM;QAC3CC,EAAE,EAAED,IAAI,CAACE,GAAG;QACZC,IAAI,EAAE,GAAGH,IAAI,CAACI,UAAU,IAAIJ,IAAI,CAACK,SAAS,EAAE;QAC5CC,MAAM,EACJ;MACJ,CAAC,CAAC,CAAC;MAEH1B,QAAQ,CAACkB,gBAAgB,CAAC;IAC5B,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,oCAAoC,CAAC;MAC9CJ,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDT,SAAS,CAAC,MAAM;IACdY,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIJ,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKgC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBjC,OAAA;QAAIgC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvDrC,OAAA;QAAKgC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjCjC,OAAA;UAAAiC,QAAA,EAAG;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI9B,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKgC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBjC,OAAA;QAAIgC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvDrC,OAAA;QAAKgC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BjC,OAAA;UAAAiC,QAAA,EAAI1B;QAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdrC,OAAA;UAAQsC,OAAO,EAAE7B,WAAY;UAACuB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErC,OAAA;IAAKgC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBjC,OAAA;MAAIgC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvDrC,OAAA;MAAKgC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9B9B,KAAK,CAACoC,MAAM,GAAG,CAAC,GACfpC,KAAK,CAACoB,GAAG,CAAEC,IAAI,iBAAKxB,OAAA,CAACF,QAAQ;QAAe0B,IAAI,EAAEA;MAAK,GAApBA,IAAI,CAACC,EAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,CAAC,gBAE3DrC,OAAA;QAAGgC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAC3D;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACnC,EAAA,CA1FQD,QAAQ;AAAAuC,EAAA,GAARvC,QAAQ;AA4FjB,eAAeA,QAAQ;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}