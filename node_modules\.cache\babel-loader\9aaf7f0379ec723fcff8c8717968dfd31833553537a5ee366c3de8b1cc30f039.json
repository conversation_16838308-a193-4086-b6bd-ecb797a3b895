{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\Login\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate, Link } from \"react-router-dom\";\nimport \"./Login.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login({\n  onLogin\n}) {\n  _s();\n  const [userName, setUserName] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const [error, setError] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError(\"\");\n    setLoading(true);\n    try {\n      const response = await fetch(\"http://localhost:8081/api/user/login\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          userName: userName,\n          password: password\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Đăng nhập thành công\n        // Lưu token vào localStorage\n        localStorage.setItem(\"token\", data.token);\n\n        // Tạo user object từ response (có thể cần điều chỉnh theo API của bạn)\n        const userData = {\n          id: data._id,\n          // Có thể lấy từ token hoặc API khác\n          firstname: userName,\n          // Tạm thời dùng userName\n          lastname: \"\",\n          userName: userName,\n          avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n        };\n\n        // Lưu user vào localStorage\n        localStorage.setItem(\"user\", JSON.stringify(userData));\n\n        // Gọi callback để cập nhật state ở component cha\n        if (onLogin) {\n          onLogin(userData);\n        }\n\n        // Chuyển về trang chủ\n        navigate(\"/\");\n      } else {\n        // Đăng nhập thất bại\n        setError(data.message || \"Đăng nhập thất bại!\");\n      }\n    } catch (error) {\n      console.error(\"Login error:\", error);\n      setError(\"Lỗi kết nối đến server!\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login__container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login__header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"login__title\",\n          children: \"\\u0110\\u0103ng nh\\u1EADp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login__subtitle\",\n          children: \"Ch\\xE0o m\\u1EEBng b\\u1EA1n quay tr\\u1EDF l\\u1EA1i!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"login__form\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login__error\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login__field\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: userName,\n            onChange: e => setUserName(e.target.value),\n            placeholder: \"T\\xEAn \\u0111\\u0103ng nh\\u1EADp\",\n            className: \"login__input\",\n            required: true,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login__field\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            value: password,\n            onChange: e => setPassword(e.target.value),\n            placeholder: \"M\\u1EADt kh\\u1EA9u\",\n            className: \"login__input\",\n            required: true,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"login__submit\",\n          disabled: loading,\n          children: loading ? \"Đang đăng nhập...\" : \"Đăng nhập\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login__footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login__register-text\",\n          children: [\"Ch\\u01B0a c\\xF3 t\\xE0i kho\\u1EA3n?\", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"login__register-link\",\n            children: \"\\u0110\\u0103ng k\\xFD ngay\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login__demo\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login__demo-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"T\\xE0i kho\\u1EA3n demo:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), \"Username: a1\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), \"M\\u1EADt kh\\u1EA9u: 1\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"CpGPeHoFp3aW+bE2Jhg53TKoMnc=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Link", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "onLogin", "_s", "userName", "setUserName", "password", "setPassword", "error", "setError", "loading", "setLoading", "navigate", "handleSubmit", "e", "preventDefault", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "localStorage", "setItem", "token", "userData", "id", "_id", "firstname", "lastname", "avatar", "message", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/Login/Login.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { useNavi<PERSON>, Link } from \"react-router-dom\";\r\nimport \"./Login.css\";\r\n\r\nfunction Login({ onLogin }) {\r\n  const [userName, setUserName] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [error, setError] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setError(\"\");\r\n    setLoading(true);\r\n\r\n    try {\r\n      const response = await fetch(\"http://localhost:8081/api/user/login\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          userName: userName,\r\n          password: password,\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        // Đ<PERSON>ng nhập thành công\r\n        // Lưu token vào localStorage\r\n        localStorage.setItem(\"token\", data.token);\r\n\r\n        // Tạo user object từ response (có thể cần điều chỉnh theo API của bạn)\r\n        const userData = {\r\n          id: data._id, // Có thể lấy từ token hoặc API khác\r\n          firstname: userName, // Tạm thời dùng userName\r\n          lastname: \"\",\r\n          userName: userName,\r\n          avatar:\r\n            \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\r\n        };\r\n\r\n        // Lưu user vào localStorage\r\n        localStorage.setItem(\"user\", JSON.stringify(userData));\r\n\r\n        // Gọi callback để cập nhật state ở component cha\r\n        if (onLogin) {\r\n          onLogin(userData);\r\n        }\r\n\r\n        // Chuyển về trang chủ\r\n        navigate(\"/\");\r\n      } else {\r\n        // Đăng nhập thất bại\r\n        setError(data.message || \"Đăng nhập thất bại!\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Login error:\", error);\r\n      setError(\"Lỗi kết nối đến server!\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"login\">\r\n      <div className=\"login__container\">\r\n        <div className=\"login__header\">\r\n          <h1 className=\"login__title\">Đăng nhập</h1>\r\n          <p className=\"login__subtitle\">Chào mừng bạn quay trở lại!</p>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"login__form\">\r\n          {error && <div className=\"login__error\">{error}</div>}\r\n\r\n          <div className=\"login__field\">\r\n            <input\r\n              type=\"text\"\r\n              value={userName}\r\n              onChange={(e) => setUserName(e.target.value)}\r\n              placeholder=\"Tên đăng nhập\"\r\n              className=\"login__input\"\r\n              required\r\n              disabled={loading}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"login__field\">\r\n            <input\r\n              type=\"password\"\r\n              value={password}\r\n              onChange={(e) => setPassword(e.target.value)}\r\n              placeholder=\"Mật khẩu\"\r\n              className=\"login__input\"\r\n              required\r\n              disabled={loading}\r\n            />\r\n          </div>\r\n\r\n          <button type=\"submit\" className=\"login__submit\" disabled={loading}>\r\n            {loading ? \"Đang đăng nhập...\" : \"Đăng nhập\"}\r\n          </button>\r\n        </form>\r\n\r\n        <div className=\"login__footer\">\r\n          <p className=\"login__register-text\">\r\n            Chưa có tài khoản?\r\n            <Link to=\"/register\" className=\"login__register-link\">\r\n              Đăng ký ngay\r\n            </Link>\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"login__demo\">\r\n          <p className=\"login__demo-text\">\r\n            <strong>Tài khoản demo:</strong>\r\n            <br />\r\n            Username: a1\r\n            <br />\r\n            Mật khẩu: 1\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,SAASC,KAAKA,CAAC;EAAEC;AAAQ,CAAC,EAAE;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAMgB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBN,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC,EAAE;QACnEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBlB,QAAQ,EAAEA,QAAQ;UAClBE,QAAQ,EAAEA;QACZ,CAAC;MACH,CAAC,CAAC;MAEF,MAAMiB,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,IAAIR,QAAQ,CAACS,EAAE,EAAE;QACf;QACA;QACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,IAAI,CAACK,KAAK,CAAC;;QAEzC;QACA,MAAMC,QAAQ,GAAG;UACfC,EAAE,EAAEP,IAAI,CAACQ,GAAG;UAAE;UACdC,SAAS,EAAE5B,QAAQ;UAAE;UACrB6B,QAAQ,EAAE,EAAE;UACZ7B,QAAQ,EAAEA,QAAQ;UAClB8B,MAAM,EACJ;QACJ,CAAC;;QAED;QACAR,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEN,IAAI,CAACC,SAAS,CAACO,QAAQ,CAAC,CAAC;;QAEtD;QACA,IAAI3B,OAAO,EAAE;UACXA,OAAO,CAAC2B,QAAQ,CAAC;QACnB;;QAEA;QACAjB,QAAQ,CAAC,GAAG,CAAC;MACf,CAAC,MAAM;QACL;QACAH,QAAQ,CAACc,IAAI,CAACY,OAAO,IAAI,qBAAqB,CAAC;MACjD;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACd4B,OAAO,CAAC5B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CAAC,yBAAyB,CAAC;IACrC,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEX,OAAA;IAAKqC,SAAS,EAAC,OAAO;IAAAC,QAAA,eACpBtC,OAAA;MAAKqC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BtC,OAAA;QAAKqC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BtC,OAAA;UAAIqC,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3C1C,OAAA;UAAGqC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAEN1C,OAAA;QAAM2C,QAAQ,EAAE9B,YAAa;QAACwB,SAAS,EAAC,aAAa;QAAAC,QAAA,GAClD9B,KAAK,iBAAIR,OAAA;UAAKqC,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAE9B;QAAK;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAErD1C,OAAA;UAAKqC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BtC,OAAA;YACE4C,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEzC,QAAS;YAChB0C,QAAQ,EAAGhC,CAAC,IAAKT,WAAW,CAACS,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;YAC7CG,WAAW,EAAC,iCAAe;YAC3BX,SAAS,EAAC,cAAc;YACxBY,QAAQ;YACRC,QAAQ,EAAExC;UAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BtC,OAAA;YACE4C,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEvC,QAAS;YAChBwC,QAAQ,EAAGhC,CAAC,IAAKP,WAAW,CAACO,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;YAC7CG,WAAW,EAAC,oBAAU;YACtBX,SAAS,EAAC,cAAc;YACxBY,QAAQ;YACRC,QAAQ,EAAExC;UAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN1C,OAAA;UAAQ4C,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,eAAe;UAACa,QAAQ,EAAExC,OAAQ;UAAA4B,QAAA,EAC/D5B,OAAO,GAAG,mBAAmB,GAAG;QAAW;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP1C,OAAA;QAAKqC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BtC,OAAA;UAAGqC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,oCAElC,eAAAtC,OAAA,CAACF,IAAI;YAACqD,EAAE,EAAC,WAAW;YAACd,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN1C,OAAA;QAAKqC,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BtC,OAAA;UAAGqC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC7BtC,OAAA;YAAAsC,QAAA,EAAQ;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChC1C,OAAA;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAEN,eAAA1C,OAAA;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,yBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvC,EAAA,CA5HQF,KAAK;EAAA,QAKKJ,WAAW;AAAA;AAAAuD,EAAA,GALrBnD,KAAK;AA8Hd,eAAeA,KAAK;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}