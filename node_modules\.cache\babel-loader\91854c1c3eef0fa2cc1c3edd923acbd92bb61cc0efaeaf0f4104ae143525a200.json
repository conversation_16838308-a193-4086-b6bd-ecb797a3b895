{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\Register\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate, Link } from \"react-router-dom\";\nimport \"./Register.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Register() {\n  _s();\n  const [formData, setFormData] = useState({\n    firstname: \"\",\n    lastname: \"\",\n    email: \"\",\n    password: \"\",\n    confirmPassword: \"\"\n  });\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (formData.password !== formData.confirmPassword) {\n      alert(\"Mật khẩu không khớp!\");\n      return;\n    }\n    if (formData.firstname && formData.lastname && formData.email && formData.password) {\n      const newUser = {\n        id: Date.now(),\n        firstname: formData.firstname,\n        lastname: formData.lastname,\n        email: formData.email,\n        avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n      };\n      localStorage.setItem(\"user\", JSON.stringify(newUser));\n      navigate(\"/\");\n      window.location.reload();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"register\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"register__container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"register__header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"register__title\",\n          children: \"\\u0110\\u0103ng k\\xFD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"register__subtitle\",\n          children: \"T\\u1EA1o t\\xE0i kho\\u1EA3n m\\u1EDBi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"register__form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register__name-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"register__field\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"firstname\",\n              value: formData.firstname,\n              onChange: handleChange,\n              placeholder: \"T\\xEAn\",\n              className: \"register__input\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"register__field\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"lastname\",\n              value: formData.lastname,\n              onChange: handleChange,\n              placeholder: \"H\\u1ECD\",\n              className: \"register__input\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register__field\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleChange,\n            placeholder: \"Email\",\n            className: \"register__input\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register__field\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleChange,\n            placeholder: \"M\\u1EADt kh\\u1EA9u\",\n            className: \"register__input\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register__field\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"confirmPassword\",\n            value: formData.confirmPassword,\n            onChange: handleChange,\n            placeholder: \"X\\xE1c nh\\u1EADn m\\u1EADt kh\\u1EA9u\",\n            className: \"register__input\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"register__submit\",\n          children: \"\\u0110\\u0103ng k\\xFD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"register__footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"register__login-text\",\n          children: [\"\\u0110\\xE3 c\\xF3 t\\xE0i kho\\u1EA3n?\", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"register__login-link\",\n            children: \"\\u0110\\u0103ng nh\\u1EADp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n}\n_s(Register, \"f0nHe88jcM5Vyw0DTJo/pAMNvNM=\", false, function () {\n  return [useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Link", "jsxDEV", "_jsxDEV", "Register", "_s", "formData", "setFormData", "firstname", "lastname", "email", "password", "confirmPassword", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "alert", "newUser", "id", "Date", "now", "avatar", "localStorage", "setItem", "JSON", "stringify", "window", "location", "reload", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "onChange", "placeholder", "required", "to", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/Register/Register.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { useN<PERSON><PERSON>, <PERSON> } from \"react-router-dom\";\r\nimport \"./Register.css\";\r\n\r\nfunction Register() {\r\n  const [formData, setFormData] = useState({\r\n    firstname: \"\",\r\n    lastname: \"\",\r\n    email: \"\",\r\n    password: \"\",\r\n    confirmPassword: \"\",\r\n  });\r\n  const navigate = useNavigate();\r\n\r\n  const handleChange = (e) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value,\r\n    });\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n\r\n    if (formData.password !== formData.confirmPassword) {\r\n      alert(\"Mật khẩu không khớp!\");\r\n      return;\r\n    }\r\n\r\n    if (\r\n      formData.firstname &&\r\n      formData.lastname &&\r\n      formData.email &&\r\n      formData.password\r\n    ) {\r\n      const newUser = {\r\n        id: Date.now(),\r\n        firstname: formData.firstname,\r\n        lastname: formData.lastname,\r\n        email: formData.email,\r\n        avatar:\r\n          \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\r\n      };\r\n\r\n      localStorage.setItem(\"user\", JSON.stringify(newUser));\r\n      navigate(\"/\");\r\n      window.location.reload();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"register\">\r\n      <div className=\"register__container\">\r\n        <div className=\"register__header\">\r\n          <h1 className=\"register__title\">Đăng ký</h1>\r\n          <p className=\"register__subtitle\">Tạo tài khoản mới</p>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"register__form\">\r\n          <div className=\"register__name-row\">\r\n            <div className=\"register__field\">\r\n              <input\r\n                type=\"text\"\r\n                name=\"firstname\"\r\n                value={formData.firstname}\r\n                onChange={handleChange}\r\n                placeholder=\"Tên\"\r\n                className=\"register__input\"\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"register__field\">\r\n              <input\r\n                type=\"text\"\r\n                name=\"lastname\"\r\n                value={formData.lastname}\r\n                onChange={handleChange}\r\n                placeholder=\"Họ\"\r\n                className=\"register__input\"\r\n                required\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"register__field\">\r\n            <input\r\n              type=\"email\"\r\n              name=\"email\"\r\n              value={formData.email}\r\n              onChange={handleChange}\r\n              placeholder=\"Email\"\r\n              className=\"register__input\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div className=\"register__field\">\r\n            <input\r\n              type=\"password\"\r\n              name=\"password\"\r\n              value={formData.password}\r\n              onChange={handleChange}\r\n              placeholder=\"Mật khẩu\"\r\n              className=\"register__input\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div className=\"register__field\">\r\n            <input\r\n              type=\"password\"\r\n              name=\"confirmPassword\"\r\n              value={formData.confirmPassword}\r\n              onChange={handleChange}\r\n              placeholder=\"Xác nhận mật khẩu\"\r\n              className=\"register__input\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <button type=\"submit\" className=\"register__submit\">\r\n            Đăng ký\r\n          </button>\r\n        </form>\r\n\r\n        <div className=\"register__footer\">\r\n          <p className=\"register__login-text\">\r\n            Đã có tài khoản?\r\n            <Link to=\"/login\" className=\"register__login-link\">\r\n              Đăng nhập\r\n            </Link>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Register;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC;IACvCS,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAMc,YAAY,GAAIC,CAAC,IAAK;IAC1BR,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACS,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAIJ,CAAC,IAAK;IAC1BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,IAAId,QAAQ,CAACK,QAAQ,KAAKL,QAAQ,CAACM,eAAe,EAAE;MAClDS,KAAK,CAAC,sBAAsB,CAAC;MAC7B;IACF;IAEA,IACEf,QAAQ,CAACE,SAAS,IAClBF,QAAQ,CAACG,QAAQ,IACjBH,QAAQ,CAACI,KAAK,IACdJ,QAAQ,CAACK,QAAQ,EACjB;MACA,MAAMW,OAAO,GAAG;QACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdjB,SAAS,EAAEF,QAAQ,CAACE,SAAS;QAC7BC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3BC,KAAK,EAAEJ,QAAQ,CAACI,KAAK;QACrBgB,MAAM,EACJ;MACJ,CAAC;MAEDC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACR,OAAO,CAAC,CAAC;MACrDT,QAAQ,CAAC,GAAG,CAAC;MACbkB,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,oBACE9B,OAAA;IAAK+B,SAAS,EAAC,UAAU;IAAAC,QAAA,eACvBhC,OAAA;MAAK+B,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClChC,OAAA;QAAK+B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BhC,OAAA;UAAI+B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CpC,OAAA;UAAG+B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAENpC,OAAA;QAAMqC,QAAQ,EAAErB,YAAa;QAACe,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBACtDhC,OAAA;UAAK+B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjChC,OAAA;YAAK+B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhC,OAAA;cACEsC,IAAI,EAAC,MAAM;cACXxB,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAEZ,QAAQ,CAACE,SAAU;cAC1BkC,QAAQ,EAAE5B,YAAa;cACvB6B,WAAW,EAAC,QAAK;cACjBT,SAAS,EAAC,iBAAiB;cAC3BU,QAAQ;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpC,OAAA;YAAK+B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhC,OAAA;cACEsC,IAAI,EAAC,MAAM;cACXxB,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEZ,QAAQ,CAACG,QAAS;cACzBiC,QAAQ,EAAE5B,YAAa;cACvB6B,WAAW,EAAC,SAAI;cAChBT,SAAS,EAAC,iBAAiB;cAC3BU,QAAQ;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpC,OAAA;UAAK+B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BhC,OAAA;YACEsC,IAAI,EAAC,OAAO;YACZxB,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEZ,QAAQ,CAACI,KAAM;YACtBgC,QAAQ,EAAE5B,YAAa;YACvB6B,WAAW,EAAC,OAAO;YACnBT,SAAS,EAAC,iBAAiB;YAC3BU,QAAQ;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpC,OAAA;UAAK+B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BhC,OAAA;YACEsC,IAAI,EAAC,UAAU;YACfxB,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEZ,QAAQ,CAACK,QAAS;YACzB+B,QAAQ,EAAE5B,YAAa;YACvB6B,WAAW,EAAC,oBAAU;YACtBT,SAAS,EAAC,iBAAiB;YAC3BU,QAAQ;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpC,OAAA;UAAK+B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BhC,OAAA;YACEsC,IAAI,EAAC,UAAU;YACfxB,IAAI,EAAC,iBAAiB;YACtBC,KAAK,EAAEZ,QAAQ,CAACM,eAAgB;YAChC8B,QAAQ,EAAE5B,YAAa;YACvB6B,WAAW,EAAC,qCAAmB;YAC/BT,SAAS,EAAC,iBAAiB;YAC3BU,QAAQ;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpC,OAAA;UAAQsC,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPpC,OAAA;QAAK+B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BhC,OAAA;UAAG+B,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,qCAElC,eAAAhC,OAAA,CAACF,IAAI;YAAC4C,EAAE,EAAC,QAAQ;YAACX,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClC,EAAA,CApIQD,QAAQ;EAAA,QAQEJ,WAAW;AAAA;AAAA8C,EAAA,GARrB1C,QAAQ;AAsIjB,eAAeA,QAAQ;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}