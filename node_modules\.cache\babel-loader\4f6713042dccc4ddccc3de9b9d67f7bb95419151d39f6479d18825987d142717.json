{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Post\\\\CreatePost\\\\CreatePost.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport \"./CreatePost.css\";\nimport CreatePostModal from \"./CreatePostModal\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction CreatePost({\n  user,\n  onCreatePost\n}) {\n  _s();\n  const [showModal, setShowModal] = useState(false);\n  const handleOpenModal = () => {\n    setShowModal(true);\n  };\n  const handleCloseModal = () => {\n    setShowModal(false);\n  };\n  const handleSubmitPost = postData => {\n    onCreatePost(postData);\n    setShowModal(false);\n  };\n  if (!user) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"create-post\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"create-post__header\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: user.avatar,\n          alt: user.firstname,\n          className: \"create-post__avatar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"create-post__input\",\n          onClick: handleOpenModal,\n          children: [user.firstname, \" \\u01A1i, b\\u1EA1n \\u0111ang ngh\\u0129 g\\xEC th\\u1EBF?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"create-post__actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"create-post__action\",\n          onClick: handleOpenModal,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"create-post__icon\",\n            children: \"\\uD83D\\uDDBC\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"create-post__text\",\n            children: \"\\u1EA2nh/video\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"create-post__action\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"create-post__icon\",\n            children: \"\\uD83D\\uDE0A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"create-post__text\",\n            children: \"C\\u1EA3m x\\xFAc/ho\\u1EA1t \\u0111\\u1ED9ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(CreatePostModal, {\n      user: user,\n      onClose: handleCloseModal,\n      onSubmit: handleSubmitPost\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n}\n_s(CreatePost, \"uVlnG5KLfXemZk5i5Fl+Cg356FU=\");\n_c = CreatePost;\nexport default CreatePost;\nvar _c;\n$RefreshReg$(_c, \"CreatePost\");", "map": {"version": 3, "names": ["React", "useState", "CreatePostModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreatePost", "user", "onCreatePost", "_s", "showModal", "setShowModal", "handleOpenModal", "handleCloseModal", "handleSubmitPost", "postData", "children", "className", "src", "avatar", "alt", "firstname", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onClose", "onSubmit", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Post/CreatePost/CreatePost.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport \"./CreatePost.css\";\r\nimport CreatePostModal from \"./CreatePostModal\";\r\n\r\nfunction CreatePost({ user, onCreatePost }) {\r\n  const [showModal, setShowModal] = useState(false);\r\n\r\n  const handleOpenModal = () => {\r\n    setShowModal(true);\r\n  };\r\n\r\n  const handleCloseModal = () => {\r\n    setShowModal(false);\r\n  };\r\n\r\n  const handleSubmitPost = (postData) => {\r\n    onCreatePost(postData);\r\n    setShowModal(false);\r\n  };\r\n\r\n  if (!user) return null;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"create-post\">\r\n        <div className=\"create-post__header\">\r\n          <img\r\n            src={user.avatar}\r\n            alt={user.firstname}\r\n            className=\"create-post__avatar\"\r\n          />\r\n          <div className=\"create-post__input\" onClick={handleOpenModal}>\r\n            {user.firstname} ơi, bạn đang ngh<PERSON> gì thế?\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"create-post__actions\">\r\n          <div className=\"create-post__action\" onClick={handleOpenModal}>\r\n            <span className=\"create-post__icon\">🖼️</span>\r\n            <span className=\"create-post__text\">Ảnh/video</span>\r\n          </div>\r\n          <div className=\"create-post__action\">\r\n            <span className=\"create-post__icon\">😊</span>\r\n            <span className=\"create-post__text\">Cảm xúc/hoạt động</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {showModal && (\r\n        <CreatePostModal\r\n          user={user}\r\n          onClose={handleCloseModal}\r\n          onSubmit={handleSubmitPost}\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default CreatePost;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,kBAAkB;AACzB,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,SAASC,UAAUA,CAAC;EAAEC,IAAI;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAC1C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMY,eAAe,GAAGA,CAAA,KAAM;IAC5BD,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7BF,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMG,gBAAgB,GAAIC,QAAQ,IAAK;IACrCP,YAAY,CAACO,QAAQ,CAAC;IACtBJ,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,IAAI,CAACJ,IAAI,EAAE,OAAO,IAAI;EAEtB,oBACEJ,OAAA,CAAAE,SAAA;IAAAW,QAAA,gBACEb,OAAA;MAAKc,SAAS,EAAC,aAAa;MAAAD,QAAA,gBAC1Bb,OAAA;QAAKc,SAAS,EAAC,qBAAqB;QAAAD,QAAA,gBAClCb,OAAA;UACEe,GAAG,EAAEX,IAAI,CAACY,MAAO;UACjBC,GAAG,EAAEb,IAAI,CAACc,SAAU;UACpBJ,SAAS,EAAC;QAAqB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACFtB,OAAA;UAAKc,SAAS,EAAC,oBAAoB;UAACS,OAAO,EAAEd,eAAgB;UAAAI,QAAA,GAC1DT,IAAI,CAACc,SAAS,EAAC,wDAClB;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtB,OAAA;QAAKc,SAAS,EAAC,sBAAsB;QAAAD,QAAA,gBACnCb,OAAA;UAAKc,SAAS,EAAC,qBAAqB;UAACS,OAAO,EAAEd,eAAgB;UAAAI,QAAA,gBAC5Db,OAAA;YAAMc,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAG;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9CtB,OAAA;YAAMc,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNtB,OAAA;UAAKc,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBAClCb,OAAA;YAAMc,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7CtB,OAAA;YAAMc,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAiB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELf,SAAS,iBACRP,OAAA,CAACF,eAAe;MACdM,IAAI,EAAEA,IAAK;MACXoB,OAAO,EAAEd,gBAAiB;MAC1Be,QAAQ,EAAEd;IAAiB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACF;EAAA,eACD,CAAC;AAEP;AAAChB,EAAA,CArDQH,UAAU;AAAAuB,EAAA,GAAVvB,UAAU;AAuDnB,eAAeA,UAAU;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}