{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Post\\\\PostDetail\\\\PostDetail.js\";\nimport React from \"react\";\nimport \"./PostDetail.css\";\nimport Modal from \"../../Common/Modal/Modal\";\nimport CommentList from \"../../Comment/CommentList/CommentList\";\nimport CommentForm from \"../../Comment/CommentForm/CommentForm\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PostDetail({\n  post,\n  onClose\n}) {\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    onClose: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"post-detail\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"post-detail__image-section\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: post.image,\n          alt: \"Post\",\n          className: \"post-detail__image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"post-detail__content-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"post-detail__header\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: post.author.avatar,\n            alt: post.author.name,\n            className: \"post-detail__author-avatar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"post-detail__author-name\",\n            children: post.author.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"post-detail__caption\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: post.caption\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"post-detail__comments\",\n          children: /*#__PURE__*/_jsxDEV(CommentList, {\n            comments: post.comments\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"post-detail__comment-form\",\n          children: /*#__PURE__*/_jsxDEV(CommentForm, {\n            postId: post.id\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n_c = PostDetail;\nexport default PostDetail;\nvar _c;\n$RefreshReg$(_c, \"PostDetail\");", "map": {"version": 3, "names": ["React", "Modal", "CommentList", "CommentForm", "jsxDEV", "_jsxDEV", "PostDetail", "post", "onClose", "children", "className", "src", "image", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "author", "avatar", "name", "caption", "comments", "postId", "id", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Post/PostDetail/PostDetail.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./PostDetail.css\";\r\nimport Modal from \"../../Common/Modal/Modal\";\r\nimport CommentList from \"../../Comment/CommentList/CommentList\";\r\nimport CommentForm from \"../../Comment/CommentForm/CommentForm\";\r\n\r\nfunction PostDetail({ post, onClose }) {\r\n  return (\r\n    <Modal onClose={onClose}>\r\n      <div className=\"post-detail\">\r\n        <div className=\"post-detail__image-section\">\r\n          <img src={post.image} alt=\"Post\" className=\"post-detail__image\" />\r\n        </div>\r\n\r\n        <div className=\"post-detail__content-section\">\r\n          <div className=\"post-detail__header\">\r\n            <img\r\n              src={post.author.avatar}\r\n              alt={post.author.name}\r\n              className=\"post-detail__author-avatar\"\r\n            />\r\n            <span className=\"post-detail__author-name\">{post.author.name}</span>\r\n          </div>\r\n\r\n          <div className=\"post-detail__caption\">\r\n            <p>{post.caption}</p>\r\n          </div>\r\n\r\n          <div className=\"post-detail__comments\">\r\n            <CommentList comments={post.comments} />\r\n          </div>\r\n\r\n          <div className=\"post-detail__comment-form\">\r\n            <CommentForm postId={post.id} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  );\r\n}\r\n\r\nexport default PostDetail;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,kBAAkB;AACzB,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,WAAW,MAAM,uCAAuC;AAC/D,OAAOC,WAAW,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,SAASC,UAAUA,CAAC;EAAEC,IAAI;EAAEC;AAAQ,CAAC,EAAE;EACrC,oBACEH,OAAA,CAACJ,KAAK;IAACO,OAAO,EAAEA,OAAQ;IAAAC,QAAA,eACtBJ,OAAA;MAAKK,SAAS,EAAC,aAAa;MAAAD,QAAA,gBAC1BJ,OAAA;QAAKK,SAAS,EAAC,4BAA4B;QAAAD,QAAA,eACzCJ,OAAA;UAAKM,GAAG,EAAEJ,IAAI,CAACK,KAAM;UAACC,GAAG,EAAC,MAAM;UAACH,SAAS,EAAC;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAENZ,OAAA;QAAKK,SAAS,EAAC,8BAA8B;QAAAD,QAAA,gBAC3CJ,OAAA;UAAKK,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBAClCJ,OAAA;YACEM,GAAG,EAAEJ,IAAI,CAACW,MAAM,CAACC,MAAO;YACxBN,GAAG,EAAEN,IAAI,CAACW,MAAM,CAACE,IAAK;YACtBV,SAAS,EAAC;UAA4B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACFZ,OAAA;YAAMK,SAAS,EAAC,0BAA0B;YAAAD,QAAA,EAAEF,IAAI,CAACW,MAAM,CAACE;UAAI;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eAENZ,OAAA;UAAKK,SAAS,EAAC,sBAAsB;UAAAD,QAAA,eACnCJ,OAAA;YAAAI,QAAA,EAAIF,IAAI,CAACc;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAENZ,OAAA;UAAKK,SAAS,EAAC,uBAAuB;UAAAD,QAAA,eACpCJ,OAAA,CAACH,WAAW;YAACoB,QAAQ,EAAEf,IAAI,CAACe;UAAS;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAENZ,OAAA;UAAKK,SAAS,EAAC,2BAA2B;UAAAD,QAAA,eACxCJ,OAAA,CAACF,WAAW;YAACoB,MAAM,EAAEhB,IAAI,CAACiB;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ;AAACQ,EAAA,GAjCQnB,UAAU;AAmCnB,eAAeA,UAAU;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}