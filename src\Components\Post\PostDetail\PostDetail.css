.post-detail {
  display: flex;
  max-width: 1000px;
  max-height: 600px;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.post-detail__image-section {
  flex: 1;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.post-detail__image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.post-detail__content-section {
  width: 400px;
  display: flex;
  flex-direction: column;
}

.post-detail__header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #e4e6ea;
}

.post-detail__author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.post-detail__author-name {
  font-size: 14px;
  font-weight: 600;
  color: #1c1e21;
}

.post-detail__caption {
  padding: 16px;
  border-bottom: 1px solid #e4e6ea;
}

.post-detail__caption p {
  margin: 0;
  font-size: 14px;
  color: #1c1e21;
  line-height: 1.4;
}

.post-detail__comments {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.post-detail__comment-form {
  border-top: 1px solid #e4e6ea;
  padding: 16px;
}

@media (max-width: 768px) {
  .post-detail {
    flex-direction: column;
    max-width: 90vw;
    max-height: 90vh;
  }
  
  .post-detail__content-section {
    width: 100%;
  }
}