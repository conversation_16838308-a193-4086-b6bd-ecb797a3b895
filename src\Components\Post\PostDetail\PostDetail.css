.post-detail {
  width: 90vw;
  max-width: 1000px;
  height: 90vh;
  max-height: 600px;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.post-detail__content {
  display: flex;
  height: 100%;
}

.post-detail__image {
  flex: 1;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.post-detail__image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.post-detail__sidebar {
  width: 400px;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #e4e6ea;
}

.post-detail__header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #e4e6ea;
}

.post-detail__author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.post-detail__author-name {
  font-size: 15px;
  font-weight: 600;
  color: #1c1e21;
}

.post-detail__caption {
  padding: 16px;
  border-bottom: 1px solid #e4e6ea;
}

.post-detail__caption p {
  margin: 0;
  color: #1c1e21;
  line-height: 1.4;
}

.post-detail__comments {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.post-detail__comments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.post-detail__comment {
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f2f5;
}

.post-detail__comment:last-child {
  border-bottom: none;
}

.post-detail__comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.post-detail__comment-author {
  font-size: 13px;
  font-weight: 600;
  color: #1c1e21;
}

.post-detail__comment-date {
  font-size: 12px;
  color: #65676b;
}

.post-detail__comment-content {
  margin: 0;
  font-size: 14px;
  color: #1c1e21;
  line-height: 1.4;
}

.post-detail__comment-form {
  padding: 16px;
  border-top: 1px solid #e4e6ea;
}

.post-detail__comment-input-wrapper {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.post-detail__comment-input {
  flex: 1;
  border: 1px solid #e4e6ea;
  border-radius: 20px;
  padding: 8px 12px;
  font-size: 14px;
  resize: none;
  outline: none;
  font-family: inherit;
}

.post-detail__comment-input:focus {
  border-color: #1877f2;
}

.post-detail__comment-input:disabled {
  background-color: #f0f2f5;
  color: #8a8d91;
}

.post-detail__comment-submit {
  background-color: #1877f2;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.post-detail__comment-submit:hover:not(:disabled) {
  background-color: #166fe5;
}

.post-detail__comment-submit:disabled {
  background-color: #e4e6ea;
  color: #8a8d91;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .post-detail {
    width: 95vw;
    height: 95vh;
  }
  
  .post-detail__content {
    flex-direction: column;
  }
  
  .post-detail__image {
    height: 60%;
  }
  
  .post-detail__sidebar {
    width: 100%;
    height: 40%;
  }
}