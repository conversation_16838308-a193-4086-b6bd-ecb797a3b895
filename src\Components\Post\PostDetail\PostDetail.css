.post-detail {
  width: 500px;
  max-height: 80vh;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.post-detail__header {
  padding: 16px;
  border-bottom: 1px solid #e4e6ea;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.post-detail__title {
  font-size: 18px;
  font-weight: 600;
  color: #1c1e21;
  margin: 0 0 12px 0;
  text-align: center;
}

.post-detail__author {
  display: flex;
  align-items: center;
  gap: 12px;
}

.post-detail__author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.post-detail__author-name {
  font-size: 15px;
  font-weight: 600;
  color: #1c1e21;
}

.post-detail__content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.post-detail__caption {
  margin-bottom: 16px;
}

.post-detail__caption p {
  margin: 0;
  font-size: 15px;
  color: #1c1e21;
  line-height: 1.4;
}

.post-detail__image-container {
  margin-bottom: 20px;
}

.post-detail__image {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.post-detail__comments {
  margin-bottom: 20px;
}

.post-detail__comment-form {
  border-top: 1px solid #e4e6ea;
  padding: 16px;
  background-color: #ffffff;
  position: sticky;
  bottom: 0;
}

@media (max-width: 768px) {
  .post-detail {
    width: 90vw;
    max-height: 90vh;
  }
}