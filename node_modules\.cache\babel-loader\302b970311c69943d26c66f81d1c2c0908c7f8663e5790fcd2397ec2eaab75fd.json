{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Comment\\\\CommentForm\\\\CommentForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport \"./CommentForm.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CommentForm({\n  postId\n}) {\n  _s();\n  const [comment, setComment] = useState(\"\");\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (comment.trim()) {\n      console.log(\"New comment:\", comment, \"for post:\", postId);\n      setComment(\"\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    className: \"comment-form\",\n    onSubmit: handleSubmit,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"comment-form__input-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: comment,\n        onChange: e => setComment(e.target.value),\n        placeholder: \"Vi\\u1EBFt b\\xECnh lu\\u1EADn...\",\n        className: \"comment-form__input\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"comment-form__submit-btn\",\n        disabled: !comment.trim(),\n        children: \"G\\u1EEDi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_s(CommentForm, \"rrFebW9Q28aJJ+M9AxcXUmcj4W0=\");\n_c = CommentForm;\nexport default CommentForm;\nvar _c;\n$RefreshReg$(_c, \"CommentForm\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "CommentForm", "postId", "_s", "comment", "setComment", "handleSubmit", "e", "preventDefault", "trim", "console", "log", "className", "onSubmit", "children", "type", "value", "onChange", "target", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Comment/CommentForm/CommentForm.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport \"./CommentForm.css\";\r\n\r\nfunction CommentForm({ postId }) {\r\n  const [comment, setComment] = useState(\"\");\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    if (comment.trim()) {\r\n      console.log(\"New comment:\", comment, \"for post:\", postId);\r\n      setComment(\"\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form className=\"comment-form\" onSubmit={handleSubmit}>\r\n      <div className=\"comment-form__input-container\">\r\n        <input\r\n          type=\"text\"\r\n          value={comment}\r\n          onChange={(e) => setComment(e.target.value)}\r\n          placeholder=\"Viết bình luận...\"\r\n          className=\"comment-form__input\"\r\n        />\r\n        <button\r\n          type=\"submit\"\r\n          className=\"comment-form__submit-btn\"\r\n          disabled={!comment.trim()}\r\n        >\r\n          Gửi\r\n        </button>\r\n      </div>\r\n    </form>\r\n  );\r\n}\r\n\r\nexport default CommentForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,SAASC,WAAWA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAC/B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMQ,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIJ,OAAO,CAACK,IAAI,CAAC,CAAC,EAAE;MAClBC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEP,OAAO,EAAE,WAAW,EAAEF,MAAM,CAAC;MACzDG,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EAED,oBACEL,OAAA;IAAMY,SAAS,EAAC,cAAc;IAACC,QAAQ,EAAEP,YAAa;IAAAQ,QAAA,eACpDd,OAAA;MAAKY,SAAS,EAAC,+BAA+B;MAAAE,QAAA,gBAC5Cd,OAAA;QACEe,IAAI,EAAC,MAAM;QACXC,KAAK,EAAEZ,OAAQ;QACfa,QAAQ,EAAGV,CAAC,IAAKF,UAAU,CAACE,CAAC,CAACW,MAAM,CAACF,KAAK,CAAE;QAC5CG,WAAW,EAAC,gCAAmB;QAC/BP,SAAS,EAAC;MAAqB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACFvB,OAAA;QACEe,IAAI,EAAC,QAAQ;QACbH,SAAS,EAAC,0BAA0B;QACpCY,QAAQ,EAAE,CAACpB,OAAO,CAACK,IAAI,CAAC,CAAE;QAAAK,QAAA,EAC3B;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX;AAACpB,EAAA,CA/BQF,WAAW;AAAAwB,EAAA,GAAXxB,WAAW;AAiCpB,eAAeA,WAAW;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}