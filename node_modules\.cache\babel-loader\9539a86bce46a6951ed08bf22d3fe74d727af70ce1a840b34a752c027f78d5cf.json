{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Layout\\\\UserItem\\\\UserItem.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport \"./UserItem.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction UserItem({\n  user\n}) {\n  _s();\n  const navigate = useNavigate();\n  const handleClick = () => {\n    navigate(`/profile/${user.id}`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-item\",\n    onClick: handleClick,\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      src: user.avatar,\n      alt: user.name,\n      className: \"user-item__avatar\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"user-item__name\",\n      children: user.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_s(UserItem, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = UserItem;\nexport default UserItem;\nvar _c;\n$RefreshReg$(_c, \"UserItem\");", "map": {"version": 3, "names": ["React", "useNavigate", "jsxDEV", "_jsxDEV", "UserItem", "user", "_s", "navigate", "handleClick", "id", "className", "onClick", "children", "src", "avatar", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Layout/UserItem/UserItem.js"], "sourcesContent": ["import React from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport \"./UserItem.css\";\r\n\r\nfunction UserItem({ user }) {\r\n  const navigate = useNavigate();\r\n\r\n  const handleClick = () => {\r\n    navigate(`/profile/${user.id}`);\r\n  };\r\n\r\n  return (\r\n    <div className=\"user-item\" onClick={handleClick}>\r\n      <img src={user.avatar} alt={user.name} className=\"user-item__avatar\" />\r\n      <span className=\"user-item__name\">{user.name}</span>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserItem;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,SAASC,QAAQA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAE9B,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxBD,QAAQ,CAAC,YAAYF,IAAI,CAACI,EAAE,EAAE,CAAC;EACjC,CAAC;EAED,oBACEN,OAAA;IAAKO,SAAS,EAAC,WAAW;IAACC,OAAO,EAAEH,WAAY;IAAAI,QAAA,gBAC9CT,OAAA;MAAKU,GAAG,EAAER,IAAI,CAACS,MAAO;MAACC,GAAG,EAAEV,IAAI,CAACW,IAAK;MAACN,SAAS,EAAC;IAAmB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvEjB,OAAA;MAAMO,SAAS,EAAC,iBAAiB;MAAAE,QAAA,EAAEP,IAAI,CAACW;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjD,CAAC;AAEV;AAACd,EAAA,CAbQF,QAAQ;EAAA,QACEH,WAAW;AAAA;AAAAoB,EAAA,GADrBjB,QAAQ;AAejB,eAAeA,QAAQ;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}