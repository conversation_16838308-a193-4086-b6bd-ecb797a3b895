{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Post\\\\PostList\\\\PostList.js\";\nimport React from \"react\";\nimport \"./PostList.css\";\nimport PostCard from \"../PostCard/PostCard\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PostList({\n  onOpenPostDetail\n}) {\n  const fakePosts = [{\n    id: 1,\n    caption: \"Cảnh đẹp thiên nhiên tuyệt vời! 🌅\",\n    image: \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500&h=400&fit=crop\",\n    author: {\n      id: 1,\n      name: \"<PERSON>uyễ<PERSON> Văn <PERSON>\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n    },\n    comments: [{\n      id: 1,\n      content: \"Đẹp quá!\",\n      author: \"<PERSON>\"\n    }, {\n      id: 2,\n      content: \"Chụp ở đâu vậy bạn?\",\n      author: \"Tuấn Anh\"\n    }]\n  }, {\n    id: 2,\n    caption: \"Bu<PERSON>i sáng tuyệt vời với tách cà phê ☕\",\n    image: \"https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=500&h=400&fit=crop\",\n    author: {\n      id: 2,\n      name: \"Trần Thị B\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\"\n    },\n    comments: [{\n      id: 3,\n      content: \"Nhìn ngon quá!\",\n      author: \"Hương Giang\"\n    }, {\n      id: 4,\n      content: \"Mình cũng thích cà phê\",\n      author: \"Đức Minh\"\n    }, {\n      id: 5,\n      content: \"Quán nào vậy bạn?\",\n      author: \"Lan Anh\"\n    }]\n  }, {\n    id: 3,\n    caption: \"Thành phố về đêm luôn có điều gì đó huyền bí ✨\",\n    image: \"https://images.unsplash.com/photo-1519501025264-65ba15a82390?w=500&h=400&fit=crop\",\n    author: {\n      id: 3,\n      name: \"Lê Văn C\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\"\n    },\n    comments: [{\n      id: 6,\n      content: \"Đẹp lắm!\",\n      author: \"Minh Tú\"\n    }]\n  }, {\n    id: 4,\n    caption: \"Món ăn ngon tuyệt! 🍜\",\n    image: \"https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=500&h=400&fit=crop\",\n    author: {\n      id: 4,\n      name: \"Phạm Thị D\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\"\n    },\n    comments: [{\n      id: 7,\n      content: \"Nhìn ngon quá!\",\n      author: \"Thanh Hà\"\n    }, {\n      id: 8,\n      content: \"Công thức làm thế nào vậy?\",\n      author: \"Quốc Anh\"\n    }]\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"post-list\",\n    children: fakePosts.map(post => /*#__PURE__*/_jsxDEV(PostCard, {\n      post: post,\n      onOpenPostDetail: onOpenPostDetail\n    }, post.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n}\n_c = PostList;\nexport default PostList;\nvar _c;\n$RefreshReg$(_c, \"PostList\");", "map": {"version": 3, "names": ["React", "PostCard", "jsxDEV", "_jsxDEV", "PostList", "onOpenPostDetail", "fakePosts", "id", "caption", "image", "author", "name", "avatar", "comments", "content", "className", "children", "map", "post", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Post/PostList/PostList.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./PostList.css\";\r\nimport PostCard from \"../PostCard/PostCard\";\r\n\r\nfunction PostList({ onOpenPostDetail }) {\r\n  const fakePosts = [\r\n    {\r\n      id: 1,\r\n      caption: \"<PERSON><PERSON><PERSON> đẹp thiên nhiên tuyệt vời! 🌅\",\r\n      image:\r\n        \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500&h=400&fit=crop\",\r\n      author: {\r\n        id: 1,\r\n        name: \"Nguyễn Văn A\",\r\n        avatar:\r\n          \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\r\n      },\r\n      comments: [\r\n        { id: 1, content: \"Đẹp quá!\", author: \"<PERSON> Anh\" },\r\n        { id: 2, content: \"Chụp ở đâu vậy bạn?\", author: \"Tuấn Anh\" },\r\n      ],\r\n    },\r\n    {\r\n      id: 2,\r\n      caption: \"<PERSON><PERSON><PERSON><PERSON> sáng tuyệt vời với tách cà phê ☕\",\r\n      image:\r\n        \"https://images.unsplash.com/photo-1501594907352-04cda38ebc29?w=500&h=400&fit=crop\",\r\n      author: {\r\n        id: 2,\r\n        name: \"Trần Thị B\",\r\n        avatar:\r\n          \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\",\r\n      },\r\n      comments: [\r\n        { id: 3, content: \"Nhìn ngon quá!\", author: \"Hương Giang\" },\r\n        { id: 4, content: \"Mình cũng thích cà phê\", author: \"Đức Minh\" },\r\n        { id: 5, content: \"Quán nào vậy bạn?\", author: \"Lan Anh\" },\r\n      ],\r\n    },\r\n    {\r\n      id: 3,\r\n      caption: \"Thành phố về đêm luôn có điều gì đó huyền bí ✨\",\r\n      image:\r\n        \"https://images.unsplash.com/photo-1519501025264-65ba15a82390?w=500&h=400&fit=crop\",\r\n      author: {\r\n        id: 3,\r\n        name: \"Lê Văn C\",\r\n        avatar:\r\n          \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\r\n      },\r\n      comments: [{ id: 6, content: \"Đẹp lắm!\", author: \"Minh Tú\" }],\r\n    },\r\n    {\r\n      id: 4,\r\n      caption: \"Món ăn ngon tuyệt! 🍜\",\r\n      image:\r\n        \"https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=500&h=400&fit=crop\",\r\n      author: {\r\n        id: 4,\r\n        name: \"Phạm Thị D\",\r\n        avatar:\r\n          \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\r\n      },\r\n      comments: [\r\n        { id: 7, content: \"Nhìn ngon quá!\", author: \"Thanh Hà\" },\r\n        { id: 8, content: \"Công thức làm thế nào vậy?\", author: \"Quốc Anh\" },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"post-list\">\r\n      {fakePosts.map((post) => (\r\n        <PostCard\r\n          key={post.id}\r\n          post={post}\r\n          onOpenPostDetail={onOpenPostDetail}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default PostList;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,gBAAgB;AACvB,OAAOC,QAAQ,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,SAASC,QAAQA,CAAC;EAAEC;AAAiB,CAAC,EAAE;EACtC,MAAMC,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,oCAAoC;IAC7CC,KAAK,EACH,mFAAmF;IACrFC,MAAM,EAAE;MACNH,EAAE,EAAE,CAAC;MACLI,IAAI,EAAE,cAAc;MACpBC,MAAM,EACJ;IACJ,CAAC;IACDC,QAAQ,EAAE,CACR;MAAEN,EAAE,EAAE,CAAC;MAAEO,OAAO,EAAE,UAAU;MAAEJ,MAAM,EAAE;IAAU,CAAC,EACjD;MAAEH,EAAE,EAAE,CAAC;MAAEO,OAAO,EAAE,qBAAqB;MAAEJ,MAAM,EAAE;IAAW,CAAC;EAEjE,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,uCAAuC;IAChDC,KAAK,EACH,mFAAmF;IACrFC,MAAM,EAAE;MACNH,EAAE,EAAE,CAAC;MACLI,IAAI,EAAE,YAAY;MAClBC,MAAM,EACJ;IACJ,CAAC;IACDC,QAAQ,EAAE,CACR;MAAEN,EAAE,EAAE,CAAC;MAAEO,OAAO,EAAE,gBAAgB;MAAEJ,MAAM,EAAE;IAAc,CAAC,EAC3D;MAAEH,EAAE,EAAE,CAAC;MAAEO,OAAO,EAAE,wBAAwB;MAAEJ,MAAM,EAAE;IAAW,CAAC,EAChE;MAAEH,EAAE,EAAE,CAAC;MAAEO,OAAO,EAAE,mBAAmB;MAAEJ,MAAM,EAAE;IAAU,CAAC;EAE9D,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,gDAAgD;IACzDC,KAAK,EACH,mFAAmF;IACrFC,MAAM,EAAE;MACNH,EAAE,EAAE,CAAC;MACLI,IAAI,EAAE,UAAU;MAChBC,MAAM,EACJ;IACJ,CAAC;IACDC,QAAQ,EAAE,CAAC;MAAEN,EAAE,EAAE,CAAC;MAAEO,OAAO,EAAE,UAAU;MAAEJ,MAAM,EAAE;IAAU,CAAC;EAC9D,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,uBAAuB;IAChCC,KAAK,EACH,mFAAmF;IACrFC,MAAM,EAAE;MACNH,EAAE,EAAE,CAAC;MACLI,IAAI,EAAE,YAAY;MAClBC,MAAM,EACJ;IACJ,CAAC;IACDC,QAAQ,EAAE,CACR;MAAEN,EAAE,EAAE,CAAC;MAAEO,OAAO,EAAE,gBAAgB;MAAEJ,MAAM,EAAE;IAAW,CAAC,EACxD;MAAEH,EAAE,EAAE,CAAC;MAAEO,OAAO,EAAE,4BAA4B;MAAEJ,MAAM,EAAE;IAAW,CAAC;EAExE,CAAC,CACF;EAED,oBACEP,OAAA;IAAKY,SAAS,EAAC,WAAW;IAAAC,QAAA,EACvBV,SAAS,CAACW,GAAG,CAAEC,IAAI,iBAClBf,OAAA,CAACF,QAAQ;MAEPiB,IAAI,EAAEA,IAAK;MACXb,gBAAgB,EAAEA;IAAiB,GAF9Ba,IAAI,CAACX,EAAE;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGb,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACC,EAAA,GA7EQnB,QAAQ;AA+EjB,eAAeA,QAAQ;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}