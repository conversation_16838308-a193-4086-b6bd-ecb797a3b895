{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Routes\\\\AppLayout.js\";\nimport React from \"react\";\nimport { BrowserRouter as Router, Routes, Route } from \"react-router-dom\";\nimport Home from \"./Pages/Home/Home\";\nimport Login from \"./Pages/Login/Login\";\nimport Register from \"./Pages/Register/Register\";\nimport About from \"./Pages/About/About\";\nimport UserProfile from \"./Pages/UserProfile/UserProfile\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/register\",\n        element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/about\",\n        element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/profile/:userId\",\n        element: /*#__PURE__*/_jsxDEV(UserProfile, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/posts\",\n        element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Home", "<PERSON><PERSON>", "Register", "About", "UserProfile", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Routes/AppLayout.js"], "sourcesContent": ["import React from \"react\";\r\nimport { <PERSON>rowserRouter as Router, Routes, Route } from \"react-router-dom\";\r\nimport Home from \"./Pages/Home/Home\";\r\nimport Login from \"./Pages/Login/Login\";\r\nimport Register from \"./Pages/Register/Register\";\r\nimport About from \"./Pages/About/About\";\r\nimport UserProfile from \"./Pages/UserProfile/UserProfile\";\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <Routes>\r\n        <Route path=\"/\" element={<Home />} />\r\n        <Route path=\"/login\" element={<Login />} />\r\n        <Route path=\"/register\" element={<Register />} />\r\n        <Route path=\"/about\" element={<About />} />\r\n        <Route path=\"/profile/:userId\" element={<UserProfile />} />\r\n        <Route path=\"/posts\" element={<Home />} />\r\n      </Routes>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,WAAW,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACT,MAAM;IAAAW,QAAA,eACLF,OAAA,CAACR,MAAM;MAAAU,QAAA,gBACLF,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEJ,OAAA,CAACN,IAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrCR,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEJ,OAAA,CAACL,KAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CR,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,WAAW;QAACC,OAAO,eAAEJ,OAAA,CAACJ,QAAQ;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjDR,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEJ,OAAA,CAACH,KAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CR,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,kBAAkB;QAACC,OAAO,eAAEJ,OAAA,CAACF,WAAW;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DR,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEJ,OAAA,CAACN,IAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACC,EAAA,GAbQR,GAAG;AAeZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}