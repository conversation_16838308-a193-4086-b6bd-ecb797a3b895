{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\AppLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Routes, Route, useNavigate, Link } from \"react-router-dom\";\nimport \"./styles.css\";\nimport Home from \"./Home\";\nimport About from \"./About\";\nimport Login from \"./Login\";\nimport NoMatch from \"./NoMatch\";\nimport Posts from \"./Posts\";\nimport Post from \"./Post\";\nimport PostLists from \"./PostLists\";\nimport Stats from \"./Stats\";\nimport NewPost from \"./NewPost\";\nimport ProtectedRoute from \"./ProtectedRoute\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppLayout() {\n  _s();\n  const [user, setUser] = useState(null);\n  const navigate = useNavigate();\n  function logOut() {\n    setUser(null);\n    navigate(\"/\");\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"navbar\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"nav-link\",\n        children: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/posts\",\n        className: \"nav-link\",\n        children: \"Posts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/about\",\n        className: \"nav-link\",\n        children: \"About\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \" | \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), user && /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/stats\",\n        className: \"nav-link\",\n        children: \"Stats\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this), user && /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/newpost\",\n        className: \"nav-link\",\n        children: \"New Post\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this), !user && /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/login\",\n        className: \"nav-link\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this), user && /*#__PURE__*/_jsxDEV(\"span\", {\n        onClick: logOut,\n        className: \"nav-link logout\",\n        children: \"Logout\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/posts\",\n        element: /*#__PURE__*/_jsxDEV(Posts, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 39\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          index: true,\n          element: /*#__PURE__*/_jsxDEV(PostLists, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \":slug\",\n          element: /*#__PURE__*/_jsxDEV(Post, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), \" \", /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/about\",\n        element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {\n          onLogin: setUser\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/stats\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          user: user,\n          children: /*#__PURE__*/_jsxDEV(Stats, {\n            user: user\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/newpost\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          user: user,\n          children: /*#__PURE__*/_jsxDEV(NewPost, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(NoMatch, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(AppLayout, \"6IF8oi2v+FFFMVJXFQinFj68c40=\", false, function () {\n  return [useNavigate];\n});\n_c = AppLayout;\nexport default AppLayout;\nvar _c;\n$RefreshReg$(_c, \"AppLayout\");", "map": {"version": 3, "names": ["React", "useState", "Routes", "Route", "useNavigate", "Link", "Home", "About", "<PERSON><PERSON>", "NoMatch", "Posts", "Post", "PostLists", "Stats", "NewPost", "ProtectedRoute", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AppLayout", "_s", "user", "setUser", "navigate", "logOut", "children", "className", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "path", "element", "index", "onLogin", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/AppLayout.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Routes, Route, useNavigate, Link } from \"react-router-dom\";\r\nimport \"./styles.css\";\r\nimport Home from \"./Home\";\r\nimport About from \"./About\";\r\nimport Login from \"./Login\";\r\nimport NoMatch from \"./NoMatch\";\r\nimport Posts from \"./Posts\";\r\nimport Post from \"./Post\";\r\nimport PostLists from \"./PostLists\";\r\nimport Stats from \"./Stats\";\r\nimport NewPost from \"./NewPost\";\r\nimport ProtectedRoute from \"./ProtectedRoute\";\r\n\r\nfunction AppLayout() {\r\n  const [user, setUser] = useState(null);\r\n  const navigate = useNavigate();\r\n\r\n  function logOut() {\r\n    setUser(null);\r\n    navigate(\"/\");\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <nav className=\"navbar\">\r\n        <Link to=\"/\" className=\"nav-link\">\r\n          Home\r\n        </Link>\r\n        <Link to=\"/posts\" className=\"nav-link\">\r\n          Posts\r\n        </Link>\r\n        <Link to=\"/about\" className=\"nav-link\">\r\n          About\r\n        </Link>\r\n        <span> | </span>\r\n        {user && (\r\n          <Link to=\"/stats\" className=\"nav-link\">\r\n            Stats\r\n          </Link>\r\n        )}\r\n        {user && (\r\n          <Link to=\"/newpost\" className=\"nav-link\">\r\n            New Post\r\n          </Link>\r\n        )}\r\n        {!user && (\r\n          <Link to=\"/login\" className=\"nav-link\">\r\n            Login\r\n          </Link>\r\n        )}\r\n        {user && (\r\n          <span onClick={logOut} className=\"nav-link logout\">\r\n            Logout\r\n          </span>\r\n        )}\r\n      </nav>\r\n      <Routes>\r\n        <Route path=\"/\" element={<Home />} />\r\n        <Route path=\"/posts\" element={<Posts />}>\r\n          <Route index element={<PostLists />} />\r\n          <Route path=\":slug\" element={<Post />} />\r\n        </Route>{\" \"}\r\n        <Route path=\"/about\" element={<About />} />\r\n        <Route path=\"/login\" element={<Login onLogin={setUser} />} />\r\n        <Route\r\n          path=\"/stats\"\r\n          element={\r\n            <ProtectedRoute user={user}>\r\n              <Stats user={user} />\r\n            </ProtectedRoute>\r\n          }\r\n        />\r\n        <Route\r\n          path=\"/newpost\"\r\n          element={\r\n            <ProtectedRoute user={user}>\r\n              <NewPost />\r\n            </ProtectedRoute>\r\n          }\r\n        />\r\n        <Route path=\"*\" element={<NoMatch />} />\r\n      </Routes>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default AppLayout;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACnE,OAAO,cAAc;AACrB,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMuB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE9B,SAASqB,MAAMA,CAAA,EAAG;IAChBF,OAAO,CAAC,IAAI,CAAC;IACbC,QAAQ,CAAC,GAAG,CAAC;EACf;EAEA,oBACEP,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBACET,OAAA;MAAKU,SAAS,EAAC,QAAQ;MAAAD,QAAA,gBACrBT,OAAA,CAACZ,IAAI;QAACuB,EAAE,EAAC,GAAG;QAACD,SAAS,EAAC,UAAU;QAAAD,QAAA,EAAC;MAElC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPf,OAAA,CAACZ,IAAI;QAACuB,EAAE,EAAC,QAAQ;QAACD,SAAS,EAAC,UAAU;QAAAD,QAAA,EAAC;MAEvC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPf,OAAA,CAACZ,IAAI;QAACuB,EAAE,EAAC,QAAQ;QAACD,SAAS,EAAC,UAAU;QAAAD,QAAA,EAAC;MAEvC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPf,OAAA;QAAAS,QAAA,EAAM;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACfV,IAAI,iBACHL,OAAA,CAACZ,IAAI;QAACuB,EAAE,EAAC,QAAQ;QAACD,SAAS,EAAC,UAAU;QAAAD,QAAA,EAAC;MAEvC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP,EACAV,IAAI,iBACHL,OAAA,CAACZ,IAAI;QAACuB,EAAE,EAAC,UAAU;QAACD,SAAS,EAAC,UAAU;QAAAD,QAAA,EAAC;MAEzC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP,EACA,CAACV,IAAI,iBACJL,OAAA,CAACZ,IAAI;QAACuB,EAAE,EAAC,QAAQ;QAACD,SAAS,EAAC,UAAU;QAAAD,QAAA,EAAC;MAEvC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP,EACAV,IAAI,iBACHL,OAAA;QAAMgB,OAAO,EAAER,MAAO;QAACE,SAAS,EAAC,iBAAiB;QAAAD,QAAA,EAAC;MAEnD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNf,OAAA,CAACf,MAAM;MAAAwB,QAAA,gBACLT,OAAA,CAACd,KAAK;QAAC+B,IAAI,EAAC,GAAG;QAACC,OAAO,eAAElB,OAAA,CAACX,IAAI;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrCf,OAAA,CAACd,KAAK;QAAC+B,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAElB,OAAA,CAACP,KAAK;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAN,QAAA,gBACtCT,OAAA,CAACd,KAAK;UAACiC,KAAK;UAACD,OAAO,eAAElB,OAAA,CAACL,SAAS;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvCf,OAAA,CAACd,KAAK;UAAC+B,IAAI,EAAC,OAAO;UAACC,OAAO,eAAElB,OAAA,CAACN,IAAI;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,EAAC,GAAG,eACZf,OAAA,CAACd,KAAK;QAAC+B,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAElB,OAAA,CAACV,KAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3Cf,OAAA,CAACd,KAAK;QAAC+B,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAElB,OAAA,CAACT,KAAK;UAAC6B,OAAO,EAAEd;QAAQ;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7Df,OAAA,CAACd,KAAK;QACJ+B,IAAI,EAAC,QAAQ;QACbC,OAAO,eACLlB,OAAA,CAACF,cAAc;UAACO,IAAI,EAAEA,IAAK;UAAAI,QAAA,eACzBT,OAAA,CAACJ,KAAK;YAACS,IAAI,EAAEA;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFf,OAAA,CAACd,KAAK;QACJ+B,IAAI,EAAC,UAAU;QACfC,OAAO,eACLlB,OAAA,CAACF,cAAc;UAACO,IAAI,EAAEA,IAAK;UAAAI,QAAA,eACzBT,OAAA,CAACH,OAAO;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFf,OAAA,CAACd,KAAK;QAAC+B,IAAI,EAAC,GAAG;QAACC,OAAO,eAAElB,OAAA,CAACR,OAAO;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA,eACT,CAAC;AAEP;AAACX,EAAA,CAvEQD,SAAS;EAAA,QAEChB,WAAW;AAAA;AAAAkC,EAAA,GAFrBlB,SAAS;AAyElB,eAAeA,SAAS;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}