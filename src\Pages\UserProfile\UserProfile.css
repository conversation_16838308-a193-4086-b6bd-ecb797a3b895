.user-profile {
  min-height: 100vh;
  background-color: #f0f2f5;
}

.user-profile__content {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.user-profile__header {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 32px;
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 24px;
}

.user-profile__avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-profile__info {
  flex: 1;
}

.user-profile__name {
  font-size: 32px;
  font-weight: 700;
  color: #1c1e21;
  margin: 0 0 8px 0;
}

.user-profile__posts-count {
  font-size: 16px;
  color: #65676b;
  margin: 0;
}

.user-profile__posts {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-profile__posts-title {
  font-size: 24px;
  font-weight: 600;
  color: #1c1e21;
  margin: 0 0 20px 0;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e6ea;
}

.user-profile__loading,
.user-profile__error {
  text-align: center;
  padding: 40px;
}

.user-profile__loading p,
.user-profile__error p {
  color: #65676b;
  font-size: 16px;
  margin: 0 0 16px 0;
}

.user-profile__retry-btn {
  background-color: #1877f2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-profile__retry-btn:hover {
  background-color: #166fe5;
}

.user-profile__posts-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-profile__no-posts {
  text-align: center;
  color: #65676b;
  font-size: 16px;
  padding: 40px;
  margin: 0;
}

@media (max-width: 768px) {
  .user-profile__header {
    flex-direction: column;
    text-align: center;
  }
  
  .user-profile__avatar {
    width: 100px;
    height: 100px;
  }
  
  .user-profile__name {
    font-size: 24px;
  }
}