{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\Post\\\\Post.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\n// import \"../../styles.css\";\nimport { useParams } from \"react-router-dom\";\nimport { useState, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Post() {\n  _s();\n  const {\n    slug\n  } = useParams();\n  const [post, setPost] = useState(\"\");\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const response = await fetch(\"http://localhost:8080/api/post/\" + slug);\n        const result = await response.json();\n        setPost(result);\n      } catch (error) {\n        console.error(\"Error fetching data:\", error);\n      }\n    };\n    fetchData();\n  }, []);\n  const {\n    title,\n    description\n  } = post;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 20\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n}\n_s(Post, \"oBB1nxxCDdSYD7mhwnqC4D5ECjI=\", false, function () {\n  return [useParams];\n});\n_c = Post;\nvar _c;\n$RefreshReg$(_c, \"Post\");", "map": {"version": 3, "names": ["React", "useParams", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Post", "_s", "slug", "post", "setPost", "fetchData", "response", "fetch", "result", "json", "error", "console", "title", "description", "style", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/Post/Post.js"], "sourcesContent": ["import React from \"react\";\r\n// import \"../../styles.css\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { useState, useEffect } from \"react\";\r\n\r\nexport default function Post() {\r\n  const { slug } = useParams();\r\n  const [post, setPost] = useState(\"\");\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        const response = await fetch(\"http://localhost:8080/api/post/\" + slug);\r\n        const result = await response.json();\r\n        setPost(result);\r\n      } catch (error) {\r\n        console.error(\"Error fetching data:\", error);\r\n      }\r\n    };\r\n    fetchData();\r\n  }, []);\r\n  const { title, description } = post;\r\n  return (\r\n    <div style={{ padding: 20 }}>\r\n      <h3>{title}</h3>\r\n      <p>{description}</p>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;AACA,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,eAAe,SAASC,IAAIA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAK,CAAC,GAAGP,SAAS,CAAC,CAAC;EAC5B,MAAM,CAACQ,IAAI,EAAEC,OAAO,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACpCC,SAAS,CAAC,MAAM;IACd,MAAMQ,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,iCAAiC,GAAGL,IAAI,CAAC;QACtE,MAAMM,MAAM,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QACpCL,OAAO,CAACI,MAAM,CAAC;MACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF,CAAC;IACDL,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EACN,MAAM;IAAEO,KAAK;IAAEC;EAAY,CAAC,GAAGV,IAAI;EACnC,oBACEJ,OAAA;IAAKe,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAE;IAAAC,QAAA,gBAC1BjB,OAAA;MAAAiB,QAAA,EAAKJ;IAAK;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAChBrB,OAAA;MAAAiB,QAAA,EAAIH;IAAW;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEV;AAACnB,EAAA,CAtBuBD,IAAI;EAAA,QACTL,SAAS;AAAA;AAAA0B,EAAA,GADJrB,IAAI;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}