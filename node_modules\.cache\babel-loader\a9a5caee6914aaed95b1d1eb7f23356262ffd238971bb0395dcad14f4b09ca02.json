{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Layout\\\\Header\\\\Header.js\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport \"./Header.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Header({\n  user,\n  onLogin,\n  onLogout\n}) {\n  const handleLogin = () => {\n    const fakeUser = {\n      id: 1,\n      firstname: \"<PERSON>\",\n      lastname: \"<PERSON><PERSON>\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n    };\n    onLogin(fakeUser);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header__left\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header__center\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"header__nav-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__icon\",\n          children: \"\\uD83C\\uDFE0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__text\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/about\",\n        className: \"header__nav-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__icon\",\n          children: \"\\u2139\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__text\",\n          children: \"About\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/posts\",\n        className: \"header__nav-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__icon\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__text\",\n          children: \"Post\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header__right\",\n      children: user ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header__user\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: user.avatar,\n          alt: \"Avatar\",\n          className: \"header__avatar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__greeting\",\n          children: [\"Hi \", user.firstname]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"header__logout-btn\",\n          onClick: onLogout,\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header__login\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__login-text\",\n          onClick: handleLogin,\n          children: \"Please Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Header", "user", "onLogin", "onLogout", "handleLogin", "fakeUser", "id", "firstname", "lastname", "avatar", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "src", "alt", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Layout/Header/Header.js"], "sourcesContent": ["import React from \"react\";\r\nimport { Link } from \"react-router-dom\";\r\nimport \"./Header.css\";\r\n\r\nfunction Header({ user, onLogin, onLogout }) {\r\n  const handleLogin = () => {\r\n    const fakeUser = {\r\n      id: 1,\r\n      firstname: \"<PERSON>\",\r\n      lastname: \"<PERSON><PERSON>\",\r\n      avatar:\r\n        \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\r\n    };\r\n    onLogin(fakeUser);\r\n  };\r\n\r\n  return (\r\n    <header className=\"header\">\r\n      <div className=\"header__left\"></div>\r\n\r\n      <div className=\"header__center\">\r\n        <Link to=\"/\" className=\"header__nav-item\">\r\n          <span className=\"header__icon\">🏠</span>\r\n          <span className=\"header__text\">Home</span>\r\n        </Link>\r\n        <Link to=\"/about\" className=\"header__nav-item\">\r\n          <span className=\"header__icon\">ℹ️</span>\r\n          <span className=\"header__text\">About</span>\r\n        </Link>\r\n        <Link to=\"/posts\" className=\"header__nav-item\">\r\n          <span className=\"header__icon\">📝</span>\r\n          <span className=\"header__text\">Post</span>\r\n        </Link>\r\n      </div>\r\n\r\n      <div className=\"header__right\">\r\n        {user ? (\r\n          <div className=\"header__user\">\r\n            <img src={user.avatar} alt=\"Avatar\" className=\"header__avatar\" />\r\n            <span className=\"header__greeting\">Hi {user.firstname}</span>\r\n            <button className=\"header__logout-btn\" onClick={onLogout}>\r\n              Logout\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"header__login\">\r\n            <span className=\"header__login-text\" onClick={handleLogin}>\r\n              Please Login\r\n            </span>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n\r\nexport default Header;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,SAASC,MAAMA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAS,CAAC,EAAE;EAC3C,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,QAAQ,GAAG;MACfC,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EACJ;IACJ,CAAC;IACDP,OAAO,CAACG,QAAQ,CAAC;EACnB,CAAC;EAED,oBACEN,OAAA;IAAQW,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACxBZ,OAAA;MAAKW,SAAS,EAAC;IAAc;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEpChB,OAAA;MAAKW,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BZ,OAAA,CAACF,IAAI;QAACmB,EAAE,EAAC,GAAG;QAACN,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACvCZ,OAAA;UAAMW,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxChB,OAAA;UAAMW,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACPhB,OAAA,CAACF,IAAI;QAACmB,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC5CZ,OAAA;UAAMW,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxChB,OAAA;UAAMW,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACPhB,OAAA,CAACF,IAAI;QAACmB,EAAE,EAAC,QAAQ;QAACN,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC5CZ,OAAA;UAAMW,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxChB,OAAA;UAAMW,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENhB,OAAA;MAAKW,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BV,IAAI,gBACHF,OAAA;QAAKW,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BZ,OAAA;UAAKkB,GAAG,EAAEhB,IAAI,CAACQ,MAAO;UAACS,GAAG,EAAC,QAAQ;UAACR,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjEhB,OAAA;UAAMW,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAAC,KAAG,EAACV,IAAI,CAACM,SAAS;QAAA;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7DhB,OAAA;UAAQW,SAAS,EAAC,oBAAoB;UAACS,OAAO,EAAEhB,QAAS;UAAAQ,QAAA,EAAC;QAE1D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAENhB,OAAA;QAAKW,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BZ,OAAA;UAAMW,SAAS,EAAC,oBAAoB;UAACS,OAAO,EAAEf,WAAY;UAAAO,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACK,EAAA,GAlDQpB,MAAM;AAoDf,eAAeA,MAAM;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}