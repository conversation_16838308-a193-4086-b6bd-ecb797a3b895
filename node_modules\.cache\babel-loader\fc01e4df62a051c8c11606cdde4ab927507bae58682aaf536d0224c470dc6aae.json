{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Comment\\\\CommentItem\\\\CommentItem.js\";\nimport React from \"react\";\nimport \"./CommentItem.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CommentItem({\n  comment\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"comment-item\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"comment-item__avatar\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: `https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face&sig=${comment.id}`,\n        alt: comment.author,\n        className: \"comment-item__avatar-img\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"comment-item__content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"comment-item__bubble\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"comment-item__author\",\n          children: comment.author\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"comment-item__text\",\n          children: comment.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = CommentItem;\nexport default CommentItem;\nvar _c;\n$RefreshReg$(_c, \"CommentItem\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "CommentItem", "comment", "className", "children", "src", "id", "alt", "author", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Comment/CommentItem/CommentItem.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./CommentItem.css\";\r\n\r\nfunction CommentItem({ comment }) {\r\n  return (\r\n    <div className=\"comment-item\">\r\n      <div className=\"comment-item__avatar\">\r\n        <img\r\n          src={`https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face&sig=${comment.id}`}\r\n          alt={comment.author}\r\n          className=\"comment-item__avatar-img\"\r\n        />\r\n      </div>\r\n      <div className=\"comment-item__content\">\r\n        <div className=\"comment-item__bubble\">\r\n          <span className=\"comment-item__author\">{comment.author}</span>\r\n          <p className=\"comment-item__text\">{comment.content}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default CommentItem;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,SAASC,WAAWA,CAAC;EAAEC;AAAQ,CAAC,EAAE;EAChC,oBACEF,OAAA;IAAKG,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BJ,OAAA;MAAKG,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnCJ,OAAA;QACEK,GAAG,EAAE,mGAAmGH,OAAO,CAACI,EAAE,EAAG;QACrHC,GAAG,EAAEL,OAAO,CAACM,MAAO;QACpBL,SAAS,EAAC;MAA0B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNZ,OAAA;MAAKG,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCJ,OAAA;QAAKG,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCJ,OAAA;UAAMG,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAEF,OAAO,CAACM;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9DZ,OAAA;UAAGG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEF,OAAO,CAACW;QAAO;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACE,EAAA,GAlBQb,WAAW;AAoBpB,eAAeA,WAAW;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}