{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Comment\\\\CommentList\\\\CommentList.js\";\nimport React from \"react\";\nimport \"./CommentList.css\";\nimport CommentItem from \"../CommentItem/CommentItem\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CommentList({\n  comments\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"comment-list\",\n    children: comments.map(comment => /*#__PURE__*/_jsxDEV(CommentItem, {\n      comment: comment\n    }, comment.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = CommentList;\nexport default CommentList;\nvar _c;\n$RefreshReg$(_c, \"CommentList\");", "map": {"version": 3, "names": ["React", "CommentItem", "jsxDEV", "_jsxDEV", "CommentList", "comments", "className", "children", "map", "comment", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Comment/CommentList/CommentList.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./CommentList.css\";\r\nimport CommentItem from \"../CommentItem/CommentItem\";\r\n\r\nfunction CommentList({ comments }) {\r\n  return (\r\n    <div className=\"comment-list\">\r\n      {comments.map((comment) => (\r\n        <CommentItem key={comment.id} comment={comment} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default CommentList;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAC1B,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,SAASC,WAAWA,CAAC;EAAEC;AAAS,CAAC,EAAE;EACjC,oBACEF,OAAA;IAAKG,SAAS,EAAC,cAAc;IAAAC,QAAA,EAC1BF,QAAQ,CAACG,GAAG,CAAEC,OAAO,iBACpBN,OAAA,CAACF,WAAW;MAAkBQ,OAAO,EAAEA;IAAQ,GAA7BA,OAAO,CAACC,EAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAqB,CAClD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACC,EAAA,GARQX,WAAW;AAUpB,eAAeA,WAAW;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}