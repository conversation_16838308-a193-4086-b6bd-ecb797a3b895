{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Post\\\\PostList\\\\PostList.js\";\nimport React from \"react\";\nimport \"./PostList.css\";\nimport PostCard from \"../PostCard/PostCard\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PostList({\n  posts,\n  onOpenPostDetail\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"post-list\",\n    children: posts && posts.length > 0 ? posts.map(post => /*#__PURE__*/_jsxDEV(PostCard, {\n      post: post,\n      onOpenPostDetail: onOpenPostDetail\n    }, post.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 11\n    }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"post-list__empty\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Ch\\u01B0a c\\xF3 b\\xE0i vi\\u1EBFt n\\xE0o\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = PostList;\nexport default PostList;\nvar _c;\n$RefreshReg$(_c, \"PostList\");", "map": {"version": 3, "names": ["React", "PostCard", "jsxDEV", "_jsxDEV", "PostList", "posts", "onOpenPostDetail", "className", "children", "length", "map", "post", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Post/PostList/PostList.js"], "sourcesContent": ["import React from \"react\";\r\nimport \"./PostList.css\";\r\nimport PostCard from \"../PostCard/PostCard\";\r\n\r\nfunction PostList({ posts, onOpenPostDetail }) {\r\n  return (\r\n    <div className=\"post-list\">\r\n      {posts && posts.length > 0 ? (\r\n        posts.map((post) => (\r\n          <PostCard\r\n            key={post.id}\r\n            post={post}\r\n            onOpenPostDetail={onOpenPostDetail}\r\n          />\r\n        ))\r\n      ) : (\r\n        <div className=\"post-list__empty\">\r\n          <p>Chưa có bài viết nào</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default PostList;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,gBAAgB;AACvB,OAAOC,QAAQ,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,SAASC,QAAQA,CAAC;EAAEC,KAAK;EAAEC;AAAiB,CAAC,EAAE;EAC7C,oBACEH,OAAA;IAAKI,SAAS,EAAC,WAAW;IAAAC,QAAA,EACvBH,KAAK,IAAIA,KAAK,CAACI,MAAM,GAAG,CAAC,GACxBJ,KAAK,CAACK,GAAG,CAAEC,IAAI,iBACbR,OAAA,CAACF,QAAQ;MAEPU,IAAI,EAAEA,IAAK;MACXL,gBAAgB,EAAEA;IAAiB,GAF9BK,IAAI,CAACC,EAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGb,CACF,CAAC,gBAEFb,OAAA;MAAKI,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BL,OAAA;QAAAK,QAAA,EAAG;MAAoB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACC,EAAA,GAlBQb,QAAQ;AAoBjB,eAAeA,QAAQ;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}