{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\NewPost.js\",\n  _s = $RefreshSig$();\nimport { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction NewPost() {\n  _s();\n  const [newPost, setNewPost] = useState(\"\");\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const onSubmit = async data => {\n    const post = JSON.stringify(data);\n    try {\n      const response = await fetch(\"http://localhost:8080/api/post\", {\n        method: \"post\",\n        headers: {\n          Accept: \"application /json\",\n          \"Content-Type\": \"application/json\"\n        },\n        body: post\n      });\n      if (response.ok) setNewPost(\"Post created successfully!\");\n    } catch (error) {\n      console.error(\"Error creating data:\", error);\n      setNewPost(\"Post created failed!\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit(onSubmit),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"np-container\",\n      children: [\" \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Slug:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        ...register(\"slug\", {\n          required: true\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), errors.slug && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: \"red\"\n        },\n        children: \"Slug is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Title:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        ...register(\"title\", {\n          required: true\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), errors.title && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: \"red\"\n        },\n        children: \"Title is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 26\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Description:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        ...register(\"description\", {\n          required: true\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), errors.description && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: \"red\"\n        },\n        children: \"Description is required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Add New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-success\",\n        children: newPost\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n}\n_s(NewPost, \"wuC9hRsW9I5uNIpIiVpO84+nL7w=\", false, function () {\n  return [useForm];\n});\n_c = NewPost;\nexport default NewPost;\nvar _c;\n$RefreshReg$(_c, \"NewPost\");", "map": {"version": 3, "names": ["useState", "useForm", "jsxDEV", "_jsxDEV", "NewPost", "_s", "newPost", "setNewPost", "register", "handleSubmit", "formState", "errors", "onSubmit", "data", "post", "JSON", "stringify", "response", "fetch", "method", "headers", "Accept", "body", "ok", "error", "console", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "required", "slug", "style", "color", "title", "description", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/NewPost.js"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\n\r\nfunction NewPost() {\r\n  const [newPost, setNewPost] = useState(\"\");\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n  } = useForm();\r\n  const onSubmit = async (data) => {\r\n    const post = JSON.stringify(data);\r\n    try {\r\n      const response = await fetch(\"http://localhost:8080/api/post\", {\r\n        method: \"post\",\r\n        headers: {\r\n          Accept: \"application /json\",\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: post,\r\n      });\r\n      if (response.ok) setNewPost(\"Post created successfully!\");\r\n    } catch (error) {\r\n      console.error(\"Error creating data:\", error);\r\n      setNewPost(\"Post created failed!\");\r\n    }\r\n  };\r\n  return (\r\n    <form onSubmit={handleSubmit(onSubmit)}>\r\n      <div className=\"np-container\">\r\n        {\" \"}\r\n        <br />\r\n        <span>Slug:</span>\r\n        <br />\r\n        <input type=\"text\" {...register(\"slug\", { required: true })} />\r\n        <br />\r\n        {errors.slug && <div style={{ color: \"red\" }}>Slug is required</div>}\r\n        <span>Title:</span>\r\n        <br />\r\n        <input type=\"text\" {...register(\"title\", { required: true })} />\r\n        <br />\r\n        {errors.title && <div style={{ color: \"red\" }}>Title is required</div>}\r\n        <span>Description:</span>\r\n        <br />\r\n        <input type=\"text\" {...register(\"description\", { required: true })} />\r\n        <br />\r\n        {errors.description && (\r\n          <div style={{ color: \"red\" }}>Description is required</div>\r\n        )}\r\n        <br />\r\n        <button type=\"submit\">Add New</button>\r\n        <p className=\"text-success\">{newPost}</p>\r\n      </div>\r\n    </form>\r\n  );\r\n}\r\n\r\nexport default NewPost;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACjB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM;IACJQ,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO;EACtB,CAAC,GAAGV,OAAO,CAAC,CAAC;EACb,MAAMW,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B,MAAMC,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC;IACjC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACPC,MAAM,EAAE,mBAAmB;UAC3B,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAER;MACR,CAAC,CAAC;MACF,IAAIG,QAAQ,CAACM,EAAE,EAAEhB,UAAU,CAAC,4BAA4B,CAAC;IAC3D,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CjB,UAAU,CAAC,sBAAsB,CAAC;IACpC;EACF,CAAC;EACD,oBACEJ,OAAA;IAAMS,QAAQ,EAAEH,YAAY,CAACG,QAAQ,CAAE;IAAAc,QAAA,eACrCvB,OAAA;MAAKwB,SAAS,EAAC,cAAc;MAAAD,QAAA,GAC1B,GAAG,eACJvB,OAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN5B,OAAA;QAAAuB,QAAA,EAAM;MAAK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAClB5B,OAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN5B,OAAA;QAAO6B,IAAI,EAAC,MAAM;QAAA,GAAKxB,QAAQ,CAAC,MAAM,EAAE;UAAEyB,QAAQ,EAAE;QAAK,CAAC;MAAC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC/D5B,OAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACLpB,MAAM,CAACuB,IAAI,iBAAI/B,OAAA;QAAKgC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACpE5B,OAAA;QAAAuB,QAAA,EAAM;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnB5B,OAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN5B,OAAA;QAAO6B,IAAI,EAAC,MAAM;QAAA,GAAKxB,QAAQ,CAAC,OAAO,EAAE;UAAEyB,QAAQ,EAAE;QAAK,CAAC;MAAC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChE5B,OAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACLpB,MAAM,CAAC0B,KAAK,iBAAIlC,OAAA;QAAKgC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtE5B,OAAA;QAAAuB,QAAA,EAAM;MAAY;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzB5B,OAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN5B,OAAA;QAAO6B,IAAI,EAAC,MAAM;QAAA,GAAKxB,QAAQ,CAAC,aAAa,EAAE;UAAEyB,QAAQ,EAAE;QAAK,CAAC;MAAC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACtE5B,OAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACLpB,MAAM,CAAC2B,WAAW,iBACjBnC,OAAA;QAAKgC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAC3D,eACD5B,OAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN5B,OAAA;QAAQ6B,IAAI,EAAC,QAAQ;QAAAN,QAAA,EAAC;MAAO;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACtC5B,OAAA;QAAGwB,SAAS,EAAC,cAAc;QAAAD,QAAA,EAAEpB;MAAO;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX;AAAC1B,EAAA,CApDQD,OAAO;EAAA,QAMVH,OAAO;AAAA;AAAAsC,EAAA,GANJnC,OAAO;AAsDhB,eAAeA,OAAO;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}