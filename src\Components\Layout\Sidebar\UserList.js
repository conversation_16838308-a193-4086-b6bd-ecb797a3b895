import React from "react";
import "./UserList.css";
import UserItem from "../../User/UserItem/UserItem";

function UserList() {
  const fakeUsers = [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON><PERSON>",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 2,
      name: "Meta AI",
      avatar:
        "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON> b<PERSON>",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 4,
      name: "<PERSON><PERSON> niệm",
      avatar:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 5,
      name: "<PERSON><PERSON> lưu",
      avatar:
        "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 6,
      name: "Nhóm",
      avatar:
        "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
    },
  ];

  return (
    <div className="user-list">
      <div className="user-list__header">
        <h3 className="user-list__title">Người liên hệ</h3>
      </div>
      <div className="user-list__content">
        {fakeUsers.map((user) => (
          <UserItem key={user.id} user={user} />
        ))}
      </div>
    </div>
  );
}

export default UserList;
