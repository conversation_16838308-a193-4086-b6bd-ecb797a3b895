{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\App.js\";\nimport \"./styles.css\";\nimport { BrowserRouter as Router } from \"react-router-dom\";\nimport AppLayout from \"./AppLayout\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(AppLayout, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "AppLayout", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/App.js"], "sourcesContent": ["import \"./styles.css\";\nimport { BrowserRouter as Router } from \"react-router-dom\";\nimport AppLayout from \"./AppLayout\";\n\nfunction App() {\n  return (\n    <Router>\n      <AppLayout />\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAO,cAAc;AACrB,SAASA,aAAa,IAAIC,MAAM,QAAQ,kBAAkB;AAC1D,OAAOC,SAAS,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACH,MAAM;IAAAK,QAAA,eACLF,OAAA,CAACF,SAAS;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEb;AAACC,EAAA,GANQN,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}