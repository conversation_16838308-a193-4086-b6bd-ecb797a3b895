.post-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.post-card__header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
}

.post-card__author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.post-card__author-name {
  font-size: 15px;
  font-weight: 600;
  color: #1c1e21;
}

.post-card__content {
  padding: 0 16px 16px;
}

.post-card__caption {
  margin: 0 0 12px 0;
  font-size: 15px;
  color: #1c1e21;
  line-height: 1.4;
}

.post-card__image {
  width: 100%;
  height: auto;
  border-radius: 8px;
  cursor: pointer;
}

.post-card__actions {
  padding: 12px 16px;
  border-top: 1px solid #e4e6ea;
}

.post-card__comment-btn {
  background: none;
  border: none;
  color: #65676b;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.post-card__comment-btn:hover {
  background-color: #f0f2f5;
}