{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Routes\\\\AppLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Routes, Route, useNavigate, Link } from \"react-router-dom\";\nimport \"../styles.css\";\nimport Home from \"../Pages/Home/Home\";\nimport About from \"../Pages/About/About\";\nimport Login from \"../Pages/Login/Login\";\nimport NoMatch from \"../Pages/NotFound/NoMatch\";\nimport Posts from \"../Pages/Post/Posts\";\nimport Post from \"../Pages/Post/Post\";\nimport PostLists from \"../Pages/Post/PostLists\";\nimport Stats from \"../Stats\";\nimport NewPost from \"../Pages/Post/NewPost\";\nimport ProtectedRoute from \"../Routes/ProtectedRoute\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppLayout() {\n  _s();\n  c;\n  const [user, setUser] = useState(null);\n  const navigate = useNavigate();\n  function logOut() {\n    setUser(null);\n    navigate(\"/\");\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/posts\",\n        element: /*#__PURE__*/_jsxDEV(Posts, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 39\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          index: true,\n          element: /*#__PURE__*/_jsxDEV(PostLists, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \":slug\",\n          element: /*#__PURE__*/_jsxDEV(Post, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), \" \", /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/about\",\n        element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {\n          onLogin: setUser\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/stats\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          user: user,\n          children: /*#__PURE__*/_jsxDEV(Stats, {\n            user: user\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/newpost\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          user: user,\n          children: /*#__PURE__*/_jsxDEV(NewPost, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(NoMatch, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n_s(AppLayout, \"6IF8oi2v+FFFMVJXFQinFj68c40=\", false, function () {\n  return [useNavigate];\n});\n_c = AppLayout;\nexport default AppLayout;\nvar _c;\n$RefreshReg$(_c, \"AppLayout\");", "map": {"version": 3, "names": ["React", "useState", "Routes", "Route", "useNavigate", "Link", "Home", "About", "<PERSON><PERSON>", "NoMatch", "Posts", "Post", "PostLists", "Stats", "NewPost", "ProtectedRoute", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AppLayout", "_s", "c", "user", "setUser", "navigate", "logOut", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "onLogin", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Routes/AppLayout.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Routes, Route, useNavigate, Link } from \"react-router-dom\";\r\nimport \"../styles.css\";\r\nimport Home from \"../Pages/Home/Home\";\r\nimport About from \"../Pages/About/About\";\r\nimport Login from \"../Pages/Login/Login\";\r\nimport NoMatch from \"../Pages/NotFound/NoMatch\";\r\nimport Posts from \"../Pages/Post/Posts\";\r\nimport Post from \"../Pages/Post/Post\";\r\nimport PostLists from \"../Pages/Post/PostLists\";\r\nimport Stats from \"../Stats\";\r\nimport NewPost from \"../Pages/Post/NewPost\";\r\nimport ProtectedRoute from \"../Routes/ProtectedRoute\";\r\n\r\nfunction AppLayout() {\r\n  c;\r\n  const [user, setUser] = useState(null);\r\n  const navigate = useNavigate();\r\n\r\n  function logOut() {\r\n    setUser(null);\r\n    navigate(\"/\");\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* <nav className=\"navbar\">\r\n        <Link to=\"/\" className=\"nav-link\">\r\n          Home\r\n        </Link>\r\n        <Link to=\"/posts\" className=\"nav-link\">\r\n          Posts\r\n        </Link>\r\n        <Link to=\"/about\" className=\"nav-link\">\r\n          About\r\n        </Link>\r\n        <span> | </span>\r\n        {user && (\r\n          <Link to=\"/stats\" className=\"nav-link\">\r\n            Stats\r\n          </Link>\r\n        )}\r\n        {user && (\r\n          <Link to=\"/newpost\" className=\"nav-link\">\r\n            New Post\r\n          </Link>\r\n        )}\r\n        {!user && (\r\n          <Link to=\"/login\" className=\"nav-link\">\r\n            Login\r\n          </Link>\r\n        )}\r\n        {user && (\r\n          <span onClick={logOut} className=\"nav-link logout\">\r\n            Logout\r\n          </span>\r\n        )}\r\n      </nav> */}\r\n      <Routes>\r\n        <Route path=\"/\" element={<Home />} />\r\n        <Route path=\"/posts\" element={<Posts />}>\r\n          <Route index element={<PostLists />} />\r\n          <Route path=\":slug\" element={<Post />} />\r\n        </Route>{\" \"}\r\n        <Route path=\"/about\" element={<About />} />\r\n        <Route path=\"/login\" element={<Login onLogin={setUser} />} />\r\n        <Route\r\n          path=\"/stats\"\r\n          element={\r\n            <ProtectedRoute user={user}>\r\n              <Stats user={user} />\r\n            </ProtectedRoute>\r\n          }\r\n        />\r\n        <Route\r\n          path=\"/newpost\"\r\n          element={\r\n            <ProtectedRoute user={user}>\r\n              <NewPost />\r\n            </ProtectedRoute>\r\n          }\r\n        />\r\n        <Route path=\"*\" element={<NoMatch />} />\r\n      </Routes>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default AppLayout;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACnE,OAAO,eAAe;AACtB,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,OAAO,MAAM,2BAA2B;AAC/C,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,cAAc,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnBC,CAAC;EACD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMwB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAE9B,SAASsB,MAAMA,CAAA,EAAG;IAChBF,OAAO,CAAC,IAAI,CAAC;IACbC,QAAQ,CAAC,GAAG,CAAC;EACf;EAEA,oBACER,OAAA,CAAAE,SAAA;IAAAQ,QAAA,eAiCEV,OAAA,CAACf,MAAM;MAAAyB,QAAA,gBACLV,OAAA,CAACd,KAAK;QAACyB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEZ,OAAA,CAACX,IAAI;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrChB,OAAA,CAACd,KAAK;QAACyB,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEZ,OAAA,CAACP,KAAK;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAN,QAAA,gBACtCV,OAAA,CAACd,KAAK;UAAC+B,KAAK;UAACL,OAAO,eAAEZ,OAAA,CAACL,SAAS;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvChB,OAAA,CAACd,KAAK;UAACyB,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEZ,OAAA,CAACN,IAAI;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,EAAC,GAAG,eACZhB,OAAA,CAACd,KAAK;QAACyB,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEZ,OAAA,CAACV,KAAK;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3ChB,OAAA,CAACd,KAAK;QAACyB,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEZ,OAAA,CAACT,KAAK;UAAC2B,OAAO,EAAEX;QAAQ;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7DhB,OAAA,CAACd,KAAK;QACJyB,IAAI,EAAC,QAAQ;QACbC,OAAO,eACLZ,OAAA,CAACF,cAAc;UAACQ,IAAI,EAAEA,IAAK;UAAAI,QAAA,eACzBV,OAAA,CAACJ,KAAK;YAACU,IAAI,EAAEA;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFhB,OAAA,CAACd,KAAK;QACJyB,IAAI,EAAC,UAAU;QACfC,OAAO,eACLZ,OAAA,CAACF,cAAc;UAACQ,IAAI,EAAEA,IAAK;UAAAI,QAAA,eACzBV,OAAA,CAACH,OAAO;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFhB,OAAA,CAACd,KAAK;QAACyB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEZ,OAAA,CAACR,OAAO;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC;EAAC,gBACT,CAAC;AAEP;AAACZ,EAAA,CAxEQD,SAAS;EAAA,QAGChB,WAAW;AAAA;AAAAgC,EAAA,GAHrBhB,SAAS;AA0ElB,eAAeA,SAAS;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}