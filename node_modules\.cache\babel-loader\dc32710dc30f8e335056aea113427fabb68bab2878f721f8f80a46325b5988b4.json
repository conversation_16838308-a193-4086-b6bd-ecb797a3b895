{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\Login\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate, Link } from \"react-router-dom\";\nimport \"./Login.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login({\n  onLogin\n}) {\n  _s();\n  const [email, setEmail] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const [error, setError] = useState(\"\");\n  const navigate = useNavigate();\n  const handleSubmit = e => {\n    e.preventDefault();\n    setError(\"\");\n\n    // Kiểm tra tài khoản và mật khẩu cụ thể\n    if (email === \"<EMAIL>\" && password === \"1\") {\n      const userData = {\n        id: 1,\n        firstname: \"<PERSON><PERSON><PERSON><PERSON>\",\n        lastname: \"<PERSON><PERSON><PERSON><PERSON>\",\n        email: email,\n        avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n      };\n\n      // Lưu user vào localStorage\n      localStorage.setItem(\"user\", JSON.stringify(userData));\n\n      // Gọi callback để cập nhật state ở component cha\n      if (onLogin) {\n        onLogin(userData);\n      }\n\n      // Chuyển về trang chủ\n      navigate(\"/\");\n    } else {\n      setError(\"Email hoặc mật khẩu không đúng!\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login__container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login__header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"login__title\",\n          children: \"\\u0110\\u0103ng nh\\u1EADp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login__subtitle\",\n          children: \"Ch\\xE0o m\\u1EEBng b\\u1EA1n quay tr\\u1EDF l\\u1EA1i!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"login__form\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login__error\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login__field\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            value: email,\n            onChange: e => setEmail(e.target.value),\n            placeholder: \"Email\",\n            className: \"login__input\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login__field\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            value: password,\n            onChange: e => setPassword(e.target.value),\n            placeholder: \"M\\u1EADt kh\\u1EA9u\",\n            className: \"login__input\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"login__submit\",\n          children: \"\\u0110\\u0103ng nh\\u1EADp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login__footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login__register-text\",\n          children: [\"Ch\\u01B0a c\\xF3 t\\xE0i kho\\u1EA3n?\", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"login__register-link\",\n            children: \"\\u0110\\u0103ng k\\xFD ngay\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login__demo\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login__demo-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"T\\xE0i kho\\u1EA3n demo:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), \"Email: <EMAIL>\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), \"M\\u1EADt kh\\u1EA9u: 1\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"Ktx5ktSKAplPJrsY5gRTpmAHqe0=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Link", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "onLogin", "_s", "email", "setEmail", "password", "setPassword", "error", "setError", "navigate", "handleSubmit", "e", "preventDefault", "userData", "id", "firstname", "lastname", "avatar", "localStorage", "setItem", "JSON", "stringify", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "to", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/Login/Login.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { useNavigate, Link } from \"react-router-dom\";\r\nimport \"./Login.css\";\r\n\r\nfunction Login({ onLogin }) {\r\n  const [email, setEmail] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [error, setError] = useState(\"\");\r\n  const navigate = useNavigate();\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    setError(\"\");\r\n\r\n    // Kiểm tra tài khoản và mật khẩu cụ thể\r\n    if (email === \"<EMAIL>\" && password === \"1\") {\r\n      const userData = {\r\n        id: 1,\r\n        firstname: \"<PERSON><PERSON>ế<PERSON>\",\r\n        lastname: \"<PERSON><PERSON><PERSON><PERSON>\",\r\n        email: email,\r\n        avatar:\r\n          \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\r\n      };\r\n\r\n      // Lưu user vào localStorage\r\n      localStorage.setItem(\"user\", JSON.stringify(userData));\r\n\r\n      // Gọi callback để cập nhật state ở component cha\r\n      if (onLogin) {\r\n        onLogin(userData);\r\n      }\r\n\r\n      // Chuyển về trang chủ\r\n      navigate(\"/\");\r\n    } else {\r\n      setError(\"Email hoặc mật khẩu không đúng!\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"login\">\r\n      <div className=\"login__container\">\r\n        <div className=\"login__header\">\r\n          <h1 className=\"login__title\">Đăng nhập</h1>\r\n          <p className=\"login__subtitle\">Chào mừng bạn quay trở lại!</p>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"login__form\">\r\n          {error && <div className=\"login__error\">{error}</div>}\r\n\r\n          <div className=\"login__field\">\r\n            <input\r\n              type=\"email\"\r\n              value={email}\r\n              onChange={(e) => setEmail(e.target.value)}\r\n              placeholder=\"Email\"\r\n              className=\"login__input\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div className=\"login__field\">\r\n            <input\r\n              type=\"password\"\r\n              value={password}\r\n              onChange={(e) => setPassword(e.target.value)}\r\n              placeholder=\"Mật khẩu\"\r\n              className=\"login__input\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <button type=\"submit\" className=\"login__submit\">\r\n            Đăng nhập\r\n          </button>\r\n        </form>\r\n\r\n        <div className=\"login__footer\">\r\n          <p className=\"login__register-text\">\r\n            Chưa có tài khoản?\r\n            <Link to=\"/register\" className=\"login__register-link\">\r\n              Đăng ký ngay\r\n            </Link>\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"login__demo\">\r\n          <p className=\"login__demo-text\">\r\n            <strong>Tài khoản demo:</strong>\r\n            <br />\r\n            Email: <EMAIL>\r\n            <br />\r\n            Mật khẩu: 1\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,SAASC,KAAKA,CAAC;EAAEC;AAAQ,CAAC,EAAE;EAAAC,EAAA;EAC1B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAMc,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBJ,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,IAAIL,KAAK,KAAK,+BAA+B,IAAIE,QAAQ,KAAK,GAAG,EAAE;MACjE,MAAMQ,QAAQ,GAAG;QACfC,EAAE,EAAE,CAAC;QACLC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,OAAO;QACjBb,KAAK,EAAEA,KAAK;QACZc,MAAM,EACJ;MACJ,CAAC;;MAED;MACAC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACR,QAAQ,CAAC,CAAC;;MAEtD;MACA,IAAIZ,OAAO,EAAE;QACXA,OAAO,CAACY,QAAQ,CAAC;MACnB;;MAEA;MACAJ,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,MAAM;MACLD,QAAQ,CAAC,iCAAiC,CAAC;IAC7C;EACF,CAAC;EAED,oBACET,OAAA;IAAKuB,SAAS,EAAC,OAAO;IAAAC,QAAA,eACpBxB,OAAA;MAAKuB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BxB,OAAA;QAAKuB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BxB,OAAA;UAAIuB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3C5B,OAAA;UAAGuB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAEN5B,OAAA;QAAM6B,QAAQ,EAAElB,YAAa;QAACY,SAAS,EAAC,aAAa;QAAAC,QAAA,GAClDhB,KAAK,iBAAIR,OAAA;UAAKuB,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAEhB;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAErD5B,OAAA;UAAKuB,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BxB,OAAA;YACE8B,IAAI,EAAC,OAAO;YACZC,KAAK,EAAE3B,KAAM;YACb4B,QAAQ,EAAGpB,CAAC,IAAKP,QAAQ,CAACO,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;YAC1CG,WAAW,EAAC,OAAO;YACnBX,SAAS,EAAC,cAAc;YACxBY,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5B,OAAA;UAAKuB,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BxB,OAAA;YACE8B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEzB,QAAS;YAChB0B,QAAQ,EAAGpB,CAAC,IAAKL,WAAW,CAACK,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;YAC7CG,WAAW,EAAC,oBAAU;YACtBX,SAAS,EAAC,cAAc;YACxBY,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5B,OAAA;UAAQ8B,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAEhD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP5B,OAAA;QAAKuB,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BxB,OAAA;UAAGuB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAAC,oCAElC,eAAAxB,OAAA,CAACF,IAAI;YAACsC,EAAE,EAAC,WAAW;YAACb,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN5B,OAAA;QAAKuB,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BxB,OAAA;UAAGuB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC7BxB,OAAA;YAAAwB,QAAA,EAAQ;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChC5B,OAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,wCAEN,eAAA5B,OAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,yBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACzB,EAAA,CA/FQF,KAAK;EAAA,QAIKJ,WAAW;AAAA;AAAAwC,EAAA,GAJrBpC,KAAK;AAiGd,eAAeA,KAAK;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}