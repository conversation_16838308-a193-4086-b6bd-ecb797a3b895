.create-post {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.create-post__header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.create-post__avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.create-post__input {
  flex: 1;
  background-color: #f0f2f5;
  border-radius: 24px;
  padding: 12px 16px;
  color: #65676b;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-post__input:hover {
  background-color: #e4e6ea;
}

.create-post__actions {
  display: flex;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid #e4e6ea;
}

.create-post__action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-post__action:hover {
  background-color: #f0f2f5;
}

.create-post__icon {
  font-size: 20px;
}

.create-post__text {
  font-size: 15px;
  font-weight: 600;
  color: #65676b;
}