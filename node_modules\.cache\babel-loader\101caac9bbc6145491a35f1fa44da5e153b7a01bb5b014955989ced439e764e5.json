{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\Home\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport \"./Home.css\";\nimport Header from \"../../Components/Layout/Header/Header\";\nimport UserList from \"../../Components/Layout/Sidebar/UserList\";\nimport PostList from \"../../Components/Post/PostList/PostList\";\nimport PostDetail from \"../../Components/Post/PostDetail/PostDetail\";\nimport CreatePost from \"../../Components/Post/CreatePost/CreatePost\";\nimport { getPhotos } from \"../../services/api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Home({\n  user: propUser,\n  onLogout\n}) {\n  _s();\n  const [user, setUser] = useState(propUser);\n  const [selectedPost, setSelectedPost] = useState(null);\n  const [showPostDetail, setShowPostDetail] = useState(false);\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  useEffect(() => {\n    // Cập nhật user từ props hoặc localStorage\n    if (propUser) {\n      setUser(propUser);\n    } else {\n      const storedUser = localStorage.getItem(\"user\");\n      if (storedUser) {\n        setUser(JSON.parse(storedUser));\n      }\n    }\n\n    // Load posts từ API\n    loadPosts();\n  }, [propUser]);\n  const loadPosts = async () => {\n    const token = localStorage.getItem(\"token\");\n    if (!token) {\n      // Nếu không có token, hiển thị fake data hoặc empty\n      setPosts([]);\n      return;\n    }\n    setLoading(true);\n    setError(\"\");\n    try {\n      const photosData = await getPhotos();\n\n      // Transform API data thành format component cần\n      const transformedPosts = photosData.map((photo, index) => ({\n        id: index + 1,\n        caption: `Bài viết của ${photo.user.name}`,\n        // Có thể thêm caption từ API sau\n        image: `http://localhost:8081/uploads/${photo.file_name}`,\n        // Đường dẫn đến ảnh\n        author: {\n          id: photo.user.user_id,\n          name: photo.user.name,\n          avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\" // Default avatar\n        },\n        comments: photo.comments.map((comment, commentIndex) => ({\n          id: commentIndex + 1,\n          content: comment.comment,\n          author: comment.user.name,\n          date: comment.date_time\n        }))\n      }));\n      setPosts(transformedPosts);\n    } catch (error) {\n      console.error(\"Error loading posts:\", error);\n      setError(\"Không thể tải bài viết. Vui lòng thử lại!\");\n      // Fallback to empty array\n      setPosts([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLogin = userData => {\n    setUser(userData);\n    // Reload posts after login\n    loadPosts();\n  };\n  const handleLogout = () => {\n    setUser(null);\n    setPosts([]); // Clear posts when logout\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    if (onLogout) {\n      onLogout();\n    }\n  };\n  const handleOpenPostDetail = post => {\n    setSelectedPost(post);\n    setShowPostDetail(true);\n  };\n  const handleClosePostDetail = () => {\n    setShowPostDetail(false);\n    setSelectedPost(null);\n  };\n  const handleCreatePost = newPost => {\n    // Thêm post mới vào đầu danh sách\n    const updatedPosts = [newPost, ...posts];\n    setPosts(updatedPosts);\n    // Có thể gọi API để reload posts sau khi tạo thành công\n    // loadPosts();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      user: user,\n      onLogin: handleLogin,\n      onLogout: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home__content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home__sidebar\",\n        children: user && /*#__PURE__*/_jsxDEV(UserList, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home__main\",\n        children: [/*#__PURE__*/_jsxDEV(CreatePost, {\n          user: user,\n          onCreatePost: handleCreatePost\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"home__loading\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u0110ang t\\u1EA3i b\\xE0i vi\\u1EBFt...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"home__error\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: loadPosts,\n            className: \"home__retry-btn\",\n            children: \"Th\\u1EED l\\u1EA1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this), !loading && !error && /*#__PURE__*/_jsxDEV(PostList, {\n          posts: posts,\n          onOpenPostDetail: handleOpenPostDetail\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home__right\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), showPostDetail && selectedPost && /*#__PURE__*/_jsxDEV(PostDetail, {\n      post: selectedPost,\n      onClose: handleClosePostDetail\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n}\n_s(Home, \"xL6d89OnXPPL2hYOFFxlFc31hGg=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Header", "UserList", "PostList", "PostDetail", "CreatePost", "getPhotos", "jsxDEV", "_jsxDEV", "Home", "user", "propUser", "onLogout", "_s", "setUser", "selectedPost", "setSelectedPost", "showPostDetail", "setShowPostDetail", "posts", "setPosts", "loading", "setLoading", "error", "setError", "storedUser", "localStorage", "getItem", "JSON", "parse", "loadPosts", "token", "photosData", "transformedPosts", "map", "photo", "index", "id", "caption", "name", "image", "file_name", "author", "user_id", "avatar", "comments", "comment", "commentIndex", "content", "date", "date_time", "console", "handleLogin", "userData", "handleLogout", "removeItem", "handleOpenPostDetail", "post", "handleClosePostDetail", "handleCreatePost", "newPost", "updatedPosts", "className", "children", "onLogin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onCreatePost", "onClick", "onOpenPostDetail", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/Home/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport \"./Home.css\";\r\nimport Header from \"../../Components/Layout/Header/Header\";\r\nimport UserList from \"../../Components/Layout/Sidebar/UserList\";\r\nimport PostList from \"../../Components/Post/PostList/PostList\";\r\nimport PostDetail from \"../../Components/Post/PostDetail/PostDetail\";\r\nimport CreatePost from \"../../Components/Post/CreatePost/CreatePost\";\r\nimport { getPhotos } from \"../../services/api\";\r\n\r\nfunction Home({ user: propUser, onLogout }) {\r\n  const [user, setUser] = useState(propUser);\r\n  const [selectedPost, setSelectedPost] = useState(null);\r\n  const [showPostDetail, setShowPostDetail] = useState(false);\r\n  const [posts, setPosts] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    // Cập nhật user từ props hoặc localStorage\r\n    if (propUser) {\r\n      setUser(propUser);\r\n    } else {\r\n      const storedUser = localStorage.getItem(\"user\");\r\n      if (storedUser) {\r\n        setUser(JSON.parse(storedUser));\r\n      }\r\n    }\r\n\r\n    // Load posts từ API\r\n    loadPosts();\r\n  }, [propUser]);\r\n\r\n  const loadPosts = async () => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (!token) {\r\n      // Nếu không có token, hiển thị fake data hoặc empty\r\n      setPosts([]);\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(\"\");\r\n\r\n    try {\r\n      const photosData = await getPhotos();\r\n\r\n      // Transform API data thành format component cần\r\n      const transformedPosts = photosData.map((photo, index) => ({\r\n        id: index + 1,\r\n        caption: `Bài viết của ${photo.user.name}`, // Có thể thêm caption từ API sau\r\n        image: `http://localhost:8081/uploads/${photo.file_name}`, // Đường dẫn đến ảnh\r\n        author: {\r\n          id: photo.user.user_id,\r\n          name: photo.user.name,\r\n          avatar:\r\n            \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\", // Default avatar\r\n        },\r\n        comments: photo.comments.map((comment, commentIndex) => ({\r\n          id: commentIndex + 1,\r\n          content: comment.comment,\r\n          author: comment.user.name,\r\n          date: comment.date_time,\r\n        })),\r\n      }));\r\n\r\n      setPosts(transformedPosts);\r\n    } catch (error) {\r\n      console.error(\"Error loading posts:\", error);\r\n      setError(\"Không thể tải bài viết. Vui lòng thử lại!\");\r\n      // Fallback to empty array\r\n      setPosts([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleLogin = (userData) => {\r\n    setUser(userData);\r\n    // Reload posts after login\r\n    loadPosts();\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    setUser(null);\r\n    setPosts([]); // Clear posts when logout\r\n    localStorage.removeItem(\"token\");\r\n    localStorage.removeItem(\"user\");\r\n    if (onLogout) {\r\n      onLogout();\r\n    }\r\n  };\r\n\r\n  const handleOpenPostDetail = (post) => {\r\n    setSelectedPost(post);\r\n    setShowPostDetail(true);\r\n  };\r\n\r\n  const handleClosePostDetail = () => {\r\n    setShowPostDetail(false);\r\n    setSelectedPost(null);\r\n  };\r\n\r\n  const handleCreatePost = (newPost) => {\r\n    // Thêm post mới vào đầu danh sách\r\n    const updatedPosts = [newPost, ...posts];\r\n    setPosts(updatedPosts);\r\n    // Có thể gọi API để reload posts sau khi tạo thành công\r\n    // loadPosts();\r\n  };\r\n\r\n  return (\r\n    <div className=\"home\">\r\n      <Header user={user} onLogin={handleLogin} onLogout={handleLogout} />\r\n\r\n      <div className=\"home__content\">\r\n        <div className=\"home__sidebar\">{user && <UserList />}</div>\r\n\r\n        <div className=\"home__main\">\r\n          <CreatePost user={user} onCreatePost={handleCreatePost} />\r\n\r\n          {loading && (\r\n            <div className=\"home__loading\">\r\n              <p>Đang tải bài viết...</p>\r\n            </div>\r\n          )}\r\n\r\n          {error && (\r\n            <div className=\"home__error\">\r\n              <p>{error}</p>\r\n              <button onClick={loadPosts} className=\"home__retry-btn\">\r\n                Thử lại\r\n              </button>\r\n            </div>\r\n          )}\r\n\r\n          {!loading && !error && (\r\n            <PostList posts={posts} onOpenPostDetail={handleOpenPostDetail} />\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"home__right\"></div>\r\n      </div>\r\n\r\n      {showPostDetail && selectedPost && (\r\n        <PostDetail post={selectedPost} onClose={handleClosePostDetail} />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,YAAY;AACnB,OAAOC,MAAM,MAAM,uCAAuC;AAC1D,OAAOC,QAAQ,MAAM,0CAA0C;AAC/D,OAAOC,QAAQ,MAAM,yCAAyC;AAC9D,OAAOC,UAAU,MAAM,6CAA6C;AACpE,OAAOC,UAAU,MAAM,6CAA6C;AACpE,SAASC,SAAS,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,IAAIA,CAAC;EAAEC,IAAI,EAAEC,QAAQ;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAC1C,MAAM,CAACH,IAAI,EAAEI,OAAO,CAAC,GAAGf,QAAQ,CAACY,QAAQ,CAAC;EAC1C,MAAM,CAACI,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd;IACA,IAAIW,QAAQ,EAAE;MACZG,OAAO,CAACH,QAAQ,CAAC;IACnB,CAAC,MAAM;MACL,MAAMc,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACdX,OAAO,CAACc,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,CAAC;MACjC;IACF;;IAEA;IACAK,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACnB,QAAQ,CAAC,CAAC;EAEd,MAAMmB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACI,KAAK,EAAE;MACV;MACAX,QAAQ,CAAC,EAAE,CAAC;MACZ;IACF;IAEAE,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMQ,UAAU,GAAG,MAAM1B,SAAS,CAAC,CAAC;;MAEpC;MACA,MAAM2B,gBAAgB,GAAGD,UAAU,CAACE,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;QACzDC,EAAE,EAAED,KAAK,GAAG,CAAC;QACbE,OAAO,EAAE,gBAAgBH,KAAK,CAACzB,IAAI,CAAC6B,IAAI,EAAE;QAAE;QAC5CC,KAAK,EAAE,iCAAiCL,KAAK,CAACM,SAAS,EAAE;QAAE;QAC3DC,MAAM,EAAE;UACNL,EAAE,EAAEF,KAAK,CAACzB,IAAI,CAACiC,OAAO;UACtBJ,IAAI,EAAEJ,KAAK,CAACzB,IAAI,CAAC6B,IAAI;UACrBK,MAAM,EACJ,6FAA6F,CAAE;QACnG,CAAC;QACDC,QAAQ,EAAEV,KAAK,CAACU,QAAQ,CAACX,GAAG,CAAC,CAACY,OAAO,EAAEC,YAAY,MAAM;UACvDV,EAAE,EAAEU,YAAY,GAAG,CAAC;UACpBC,OAAO,EAAEF,OAAO,CAACA,OAAO;UACxBJ,MAAM,EAAEI,OAAO,CAACpC,IAAI,CAAC6B,IAAI;UACzBU,IAAI,EAAEH,OAAO,CAACI;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;MAEH9B,QAAQ,CAACa,gBAAgB,CAAC;IAC5B,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd4B,OAAO,CAAC5B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,2CAA2C,CAAC;MACrD;MACAJ,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,WAAW,GAAIC,QAAQ,IAAK;IAChCvC,OAAO,CAACuC,QAAQ,CAAC;IACjB;IACAvB,SAAS,CAAC,CAAC;EACb,CAAC;EAED,MAAMwB,YAAY,GAAGA,CAAA,KAAM;IACzBxC,OAAO,CAAC,IAAI,CAAC;IACbM,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IACdM,YAAY,CAAC6B,UAAU,CAAC,OAAO,CAAC;IAChC7B,YAAY,CAAC6B,UAAU,CAAC,MAAM,CAAC;IAC/B,IAAI3C,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAM4C,oBAAoB,GAAIC,IAAI,IAAK;IACrCzC,eAAe,CAACyC,IAAI,CAAC;IACrBvC,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMwC,qBAAqB,GAAGA,CAAA,KAAM;IAClCxC,iBAAiB,CAAC,KAAK,CAAC;IACxBF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM2C,gBAAgB,GAAIC,OAAO,IAAK;IACpC;IACA,MAAMC,YAAY,GAAG,CAACD,OAAO,EAAE,GAAGzC,KAAK,CAAC;IACxCC,QAAQ,CAACyC,YAAY,CAAC;IACtB;IACA;EACF,CAAC;EAED,oBACErD,OAAA;IAAKsD,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBvD,OAAA,CAACP,MAAM;MAACS,IAAI,EAAEA,IAAK;MAACsD,OAAO,EAAEZ,WAAY;MAACxC,QAAQ,EAAE0C;IAAa;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEpE5D,OAAA;MAAKsD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BvD,OAAA;QAAKsD,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAErD,IAAI,iBAAIF,OAAA,CAACN,QAAQ;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE3D5D,OAAA;QAAKsD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBvD,OAAA,CAACH,UAAU;UAACK,IAAI,EAAEA,IAAK;UAAC2D,YAAY,EAAEV;QAAiB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAEzD/C,OAAO,iBACNb,OAAA;UAAKsD,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BvD,OAAA;YAAAuD,QAAA,EAAG;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACN,EAEA7C,KAAK,iBACJf,OAAA;UAAKsD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BvD,OAAA;YAAAuD,QAAA,EAAIxC;UAAK;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACd5D,OAAA;YAAQ8D,OAAO,EAAExC,SAAU;YAACgC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAEA,CAAC/C,OAAO,IAAI,CAACE,KAAK,iBACjBf,OAAA,CAACL,QAAQ;UAACgB,KAAK,EAAEA,KAAM;UAACoD,gBAAgB,EAAEf;QAAqB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAClE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN5D,OAAA;QAAKsD,SAAS,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,EAELnD,cAAc,IAAIF,YAAY,iBAC7BP,OAAA,CAACJ,UAAU;MAACqD,IAAI,EAAE1C,YAAa;MAACyD,OAAO,EAAEd;IAAsB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAClE;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACvD,EAAA,CA3IQJ,IAAI;AAAAgE,EAAA,GAAJhE,IAAI;AA6Ib,eAAeA,IAAI;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}