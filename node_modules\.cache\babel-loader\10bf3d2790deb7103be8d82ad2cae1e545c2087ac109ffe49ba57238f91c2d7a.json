{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Components\\\\Common\\\\Modal\\\\CreatePostModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport \"./CreatePostModal.css\";\nimport Modal from \"../../Common/Modal/Modal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CreatePostModal({\n  user,\n  onClose,\n  onSubmit\n}) {\n  _s();\n  const [caption, setCaption] = useState(\"\");\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const handleImageChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = e => {\n        setImagePreview(e.target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (caption.trim() || selectedImage) {\n      const newPost = {\n        id: Date.now(),\n        caption: caption.trim(),\n        image: imagePreview || \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500&h=400&fit=crop\",\n        author: {\n          id: user.id,\n          name: `${user.firstname} ${user.lastname}`,\n          avatar: user.avatar\n        },\n        comments: []\n      };\n      onSubmit(newPost);\n    }\n  };\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    onClose: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"create-post-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"create-post-modal__header\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"create-post-modal__title\",\n          children: \"T\\u1EA1o b\\xE0i vi\\u1EBFt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"create-post-modal__user\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: user.avatar,\n          alt: user.firstname,\n          className: \"create-post-modal__avatar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"create-post-modal__name\",\n          children: [user.firstname, \" \", user.lastname]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"create-post-modal__form\",\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: caption,\n          onChange: e => setCaption(e.target.value),\n          placeholder: `${user.firstname} ơi, bạn đang nghĩ gì thế?`,\n          className: \"create-post-modal__textarea\",\n          rows: \"4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"create-post-modal__image-preview\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"create-post-modal__remove-image\",\n            onClick: removeImage,\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imagePreview,\n            alt: \"Preview\",\n            className: \"create-post-modal__preview-img\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"create-post-modal__image-upload\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"create-post-modal__upload-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              accept: \"image/*\",\n              onChange: handleImageChange,\n              className: \"create-post-modal__upload-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"create-post-modal__upload-area\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"create-post-modal__upload-icon\",\n                children: \"\\uD83D\\uDCF7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"create-post-modal__upload-text\",\n                children: \"Th\\xEAm \\u1EA3nh/video\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"create-post-modal__upload-subtext\",\n                children: \"ho\\u1EB7c k\\xE9o v\\xE0 th\\u1EA3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"create-post-modal__submit\",\n          disabled: !caption.trim() && !selectedImage,\n          children: \"\\u0110\\u0103ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n}\n_s(CreatePostModal, \"X2H414fqXDaxfF/DZEdfhJIX6uY=\");\n_c = CreatePostModal;\nexport default CreatePostModal;\nvar _c;\n$RefreshReg$(_c, \"CreatePostModal\");", "map": {"version": 3, "names": ["React", "useState", "Modal", "jsxDEV", "_jsxDEV", "CreatePostModal", "user", "onClose", "onSubmit", "_s", "caption", "setCaption", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "handleImageChange", "e", "file", "target", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSubmit", "preventDefault", "trim", "newPost", "id", "Date", "now", "image", "author", "name", "firstname", "lastname", "avatar", "comments", "removeImage", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "value", "onChange", "placeholder", "rows", "type", "onClick", "accept", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Components/Common/Modal/CreatePostModal.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport \"./CreatePostModal.css\";\r\nimport Modal from \"../../Common/Modal/Modal\";\r\n\r\nfunction CreatePostModal({ user, onClose, onSubmit }) {\r\n  const [caption, setCaption] = useState(\"\");\r\n  const [selectedImage, setSelectedImage] = useState(null);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n\r\n  const handleImageChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setSelectedImage(file);\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        setImagePreview(e.target.result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    if (caption.trim() || selectedImage) {\r\n      const newPost = {\r\n        id: Date.now(),\r\n        caption: caption.trim(),\r\n        image:\r\n          imagePreview ||\r\n          \"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500&h=400&fit=crop\",\r\n        author: {\r\n          id: user.id,\r\n          name: `${user.firstname} ${user.lastname}`,\r\n          avatar: user.avatar,\r\n        },\r\n        comments: [],\r\n      };\r\n      onSubmit(newPost);\r\n    }\r\n  };\r\n\r\n  const removeImage = () => {\r\n    setSelectedImage(null);\r\n    setImagePreview(null);\r\n  };\r\n\r\n  return (\r\n    <Modal onClose={onClose}>\r\n      <div className=\"create-post-modal\">\r\n        <div className=\"create-post-modal__header\">\r\n          <h2 className=\"create-post-modal__title\">Tạo bài viết</h2>\r\n        </div>\r\n\r\n        <div className=\"create-post-modal__user\">\r\n          <img\r\n            src={user.avatar}\r\n            alt={user.firstname}\r\n            className=\"create-post-modal__avatar\"\r\n          />\r\n          <span className=\"create-post-modal__name\">\r\n            {user.firstname} {user.lastname}\r\n          </span>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"create-post-modal__form\">\r\n          <textarea\r\n            value={caption}\r\n            onChange={(e) => setCaption(e.target.value)}\r\n            placeholder={`${user.firstname} ơi, bạn đang nghĩ gì thế?`}\r\n            className=\"create-post-modal__textarea\"\r\n            rows=\"4\"\r\n          />\r\n\r\n          {imagePreview && (\r\n            <div className=\"create-post-modal__image-preview\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"create-post-modal__remove-image\"\r\n                onClick={removeImage}\r\n              >\r\n                ✕\r\n              </button>\r\n              <img\r\n                src={imagePreview}\r\n                alt=\"Preview\"\r\n                className=\"create-post-modal__preview-img\"\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"create-post-modal__image-upload\">\r\n            <label className=\"create-post-modal__upload-label\">\r\n              <input\r\n                type=\"file\"\r\n                accept=\"image/*\"\r\n                onChange={handleImageChange}\r\n                className=\"create-post-modal__upload-input\"\r\n              />\r\n              <div className=\"create-post-modal__upload-area\">\r\n                <span className=\"create-post-modal__upload-icon\">📷</span>\r\n                <span className=\"create-post-modal__upload-text\">\r\n                  Thêm ảnh/video\r\n                </span>\r\n                <span className=\"create-post-modal__upload-subtext\">\r\n                  hoặc kéo và thả\r\n                </span>\r\n              </div>\r\n            </label>\r\n          </div>\r\n\r\n          <button\r\n            type=\"submit\"\r\n            className=\"create-post-modal__submit\"\r\n            disabled={!caption.trim() && !selectedImage}\r\n          >\r\n            Đăng\r\n          </button>\r\n        </form>\r\n      </div>\r\n    </Modal>\r\n  );\r\n}\r\n\r\nexport default CreatePostModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,uBAAuB;AAC9B,OAAOC,KAAK,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,SAASC,eAAeA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMe,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACRL,gBAAgB,CAACK,IAAI,CAAC;MACtB,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIN,CAAC,IAAK;QACrBF,eAAe,CAACE,CAAC,CAACE,MAAM,CAACK,MAAM,CAAC;MAClC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMQ,YAAY,GAAIT,CAAC,IAAK;IAC1BA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClB,IAAIjB,OAAO,CAACkB,IAAI,CAAC,CAAC,IAAIhB,aAAa,EAAE;MACnC,MAAMiB,OAAO,GAAG;QACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdtB,OAAO,EAAEA,OAAO,CAACkB,IAAI,CAAC,CAAC;QACvBK,KAAK,EACHnB,YAAY,IACZ,mFAAmF;QACrFoB,MAAM,EAAE;UACNJ,EAAE,EAAExB,IAAI,CAACwB,EAAE;UACXK,IAAI,EAAE,GAAG7B,IAAI,CAAC8B,SAAS,IAAI9B,IAAI,CAAC+B,QAAQ,EAAE;UAC1CC,MAAM,EAAEhC,IAAI,CAACgC;QACf,CAAC;QACDC,QAAQ,EAAE;MACZ,CAAC;MACD/B,QAAQ,CAACqB,OAAO,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxB3B,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACEX,OAAA,CAACF,KAAK;IAACK,OAAO,EAAEA,OAAQ;IAAAkC,QAAA,eACtBrC,OAAA;MAAKsC,SAAS,EAAC,mBAAmB;MAAAD,QAAA,gBAChCrC,OAAA;QAAKsC,SAAS,EAAC,2BAA2B;QAAAD,QAAA,eACxCrC,OAAA;UAAIsC,SAAS,EAAC,0BAA0B;UAAAD,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAEN1C,OAAA;QAAKsC,SAAS,EAAC,yBAAyB;QAAAD,QAAA,gBACtCrC,OAAA;UACE2C,GAAG,EAAEzC,IAAI,CAACgC,MAAO;UACjBU,GAAG,EAAE1C,IAAI,CAAC8B,SAAU;UACpBM,SAAS,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACF1C,OAAA;UAAMsC,SAAS,EAAC,yBAAyB;UAAAD,QAAA,GACtCnC,IAAI,CAAC8B,SAAS,EAAC,GAAC,EAAC9B,IAAI,CAAC+B,QAAQ;QAAA;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN1C,OAAA;QAAMI,QAAQ,EAAEkB,YAAa;QAACgB,SAAS,EAAC,yBAAyB;QAAAD,QAAA,gBAC/DrC,OAAA;UACE6C,KAAK,EAAEvC,OAAQ;UACfwC,QAAQ,EAAGjC,CAAC,IAAKN,UAAU,CAACM,CAAC,CAACE,MAAM,CAAC8B,KAAK,CAAE;UAC5CE,WAAW,EAAE,GAAG7C,IAAI,CAAC8B,SAAS,4BAA6B;UAC3DM,SAAS,EAAC,6BAA6B;UACvCU,IAAI,EAAC;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EAEDhC,YAAY,iBACXV,OAAA;UAAKsC,SAAS,EAAC,kCAAkC;UAAAD,QAAA,gBAC/CrC,OAAA;YACEiD,IAAI,EAAC,QAAQ;YACbX,SAAS,EAAC,iCAAiC;YAC3CY,OAAO,EAAEd,WAAY;YAAAC,QAAA,EACtB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1C,OAAA;YACE2C,GAAG,EAAEjC,YAAa;YAClBkC,GAAG,EAAC,SAAS;YACbN,SAAS,EAAC;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAED1C,OAAA;UAAKsC,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eAC9CrC,OAAA;YAAOsC,SAAS,EAAC,iCAAiC;YAAAD,QAAA,gBAChDrC,OAAA;cACEiD,IAAI,EAAC,MAAM;cACXE,MAAM,EAAC,SAAS;cAChBL,QAAQ,EAAElC,iBAAkB;cAC5B0B,SAAS,EAAC;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACF1C,OAAA;cAAKsC,SAAS,EAAC,gCAAgC;cAAAD,QAAA,gBAC7CrC,OAAA;gBAAMsC,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1D1C,OAAA;gBAAMsC,SAAS,EAAC,gCAAgC;gBAAAD,QAAA,EAAC;cAEjD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP1C,OAAA;gBAAMsC,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAEpD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN1C,OAAA;UACEiD,IAAI,EAAC,QAAQ;UACbX,SAAS,EAAC,2BAA2B;UACrCc,QAAQ,EAAE,CAAC9C,OAAO,CAACkB,IAAI,CAAC,CAAC,IAAI,CAAChB,aAAc;UAAA6B,QAAA,EAC7C;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ;AAACrC,EAAA,CArHQJ,eAAe;AAAAoD,EAAA,GAAfpD,eAAe;AAuHxB,eAAeA,eAAe;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}