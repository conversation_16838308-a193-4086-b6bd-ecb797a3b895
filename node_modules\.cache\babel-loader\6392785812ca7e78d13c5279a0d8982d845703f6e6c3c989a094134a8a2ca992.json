{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\Post\\\\Posts.js\";\nimport React from \"react\";\nimport { Outlet } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Posts() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"posts-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"posts-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Blog\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"posts-content\",\n      children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = Posts;\nexport default Posts;\nvar _c;\n$RefreshReg$(_c, \"Posts\");", "map": {"version": 3, "names": ["React", "Outlet", "jsxDEV", "_jsxDEV", "Posts", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/Post/Posts.js"], "sourcesContent": ["import React from \"react\";\r\nimport { Outlet } from \"react-router-dom\";\r\n\r\nfunction Posts() {\r\n  return (\r\n    <div className=\"posts-container\">\r\n      <div className=\"posts-header\">\r\n        <h2>Blog</h2>\r\n      </div>\r\n      <div className=\"posts-content\">\r\n        <Outlet />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Posts;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,SAASC,KAAKA,CAAA,EAAG;EACf,oBACED,OAAA;IAAKE,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BH,OAAA;MAAKE,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BH,OAAA;QAAAG,QAAA,EAAI;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BH,OAAA,CAACF,MAAM;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GAXQP,KAAK;AAad,eAAeA,KAAK;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}