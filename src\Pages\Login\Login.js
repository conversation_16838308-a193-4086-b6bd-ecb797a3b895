import React, { useState } from "react";
import { useN<PERSON><PERSON>, <PERSON> } from "react-router-dom";
import "./Login.css";

function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const navigate = useNavigate();

  const handleSubmit = (e) => {
    e.preventDefault();
    // Fake login logic
    if (email && password) {
      const fakeUser = {
        id: 1,
        firstname: "<PERSON>",
        lastname: "<PERSON><PERSON>",
        email: email,
        avatar:
          "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      };

      // Store user in localStorage or context
      localStorage.setItem("user", JSON.stringify(fakeUser));

      // Navigate to home
      navigate("/");
      window.location.reload(); // Reload to update user state
    }
  };

  return (
    <div className="login">
      <div className="login__container">
        <div className="login__header">
          <h1 className="login__title">Đăng nhập</h1>
          <p className="login__subtitle">Chào mừng bạn quay trở lại!</p>
        </div>

        <form onSubmit={handleSubmit} className="login__form">
          <div className="login__field">
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Email"
              className="login__input"
              required
            />
          </div>

          <div className="login__field">
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Mật khẩu"
              className="login__input"
              required
            />
          </div>

          <button type="submit" className="login__submit">
            Đăng nhập
          </button>
        </form>

        <div className="login__footer">
          <p className="login__register-text">
            Chưa có tài khoản?
            <Link to="/register" className="login__register-link">
              Đăng ký ngay
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

export default Login;
