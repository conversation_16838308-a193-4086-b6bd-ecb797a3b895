{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Pages\\\\Home\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport \"./Home.css\";\nimport Header from \"../../Components/Layout/Header/Header\";\nimport Sidebar from \"../../Components/Layout/Sidebar/Sidebar\";\nimport PostList from \"../../Components/Post/PostList/PostList\";\nimport PostDetail from \"../../Components/Post/PostDetail/PostDetail\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Home() {\n  _s();\n  const [user, setUser] = useState(null);\n  const [selectedPost, setSelectedPost] = useState(null);\n  const [showPostDetail, setShowPostDetail] = useState(false);\n  const handleLogin = userData => {\n    setUser(userData);\n  };\n  const handleLogout = () => {\n    setUser(null);\n  };\n  const handleOpenPostDetail = post => {\n    setSelectedPost(post);\n    setShowPostDetail(true);\n  };\n  const handleClosePostDetail = () => {\n    setShowPostDetail(false);\n    setSelectedPost(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      user: user,\n      onLogin: handleLogin,\n      onLogout: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home__content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home__sidebar\",\n        children: user && /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home__main\",\n        children: /*#__PURE__*/_jsxDEV(PostList, {\n          onOpenPostDetail: handleOpenPostDetail\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home__right\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), showPostDetail && selectedPost && /*#__PURE__*/_jsxDEV(PostDetail, {\n      post: selectedPost,\n      onClose: handleClosePostDetail\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_s(Home, \"bMqlMzoMn4RmvWCfhuocuZmFHgk=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "Header", "Sidebar", "PostList", "PostDetail", "jsxDEV", "_jsxDEV", "Home", "_s", "user", "setUser", "selectedPost", "setSelectedPost", "showPostDetail", "setShowPostDetail", "handleLogin", "userData", "handleLogout", "handleOpenPostDetail", "post", "handleClosePostDetail", "className", "children", "onLogin", "onLogout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onOpenPostDetail", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Pages/Home/Home.js"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport \"./Home.css\";\r\nimport Header from \"../../Components/Layout/Header/Header\";\r\nimport Sidebar from \"../../Components/Layout/Sidebar/Sidebar\";\r\nimport PostList from \"../../Components/Post/PostList/PostList\";\r\nimport PostDetail from \"../../Components/Post/PostDetail/PostDetail\";\r\n\r\nfunction Home() {\r\n  const [user, setUser] = useState(null);\r\n  const [selectedPost, setSelectedPost] = useState(null);\r\n  const [showPostDetail, setShowPostDetail] = useState(false);\r\n\r\n  const handleLogin = (userData) => {\r\n    setUser(userData);\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    setUser(null);\r\n  };\r\n\r\n  const handleOpenPostDetail = (post) => {\r\n    setSelectedPost(post);\r\n    setShowPostDetail(true);\r\n  };\r\n\r\n  const handleClosePostDetail = () => {\r\n    setShowPostDetail(false);\r\n    setSelectedPost(null);\r\n  };\r\n\r\n  return (\r\n    <div className=\"home\">\r\n      <Header user={user} onLogin={handleLogin} onLogout={handleLogout} />\r\n\r\n      <div className=\"home__content\">\r\n        <div className=\"home__sidebar\">{user && <Sidebar />}</div>\r\n\r\n        <div className=\"home__main\">\r\n          <PostList onOpenPostDetail={handleOpenPostDetail} />\r\n        </div>\r\n\r\n        <div className=\"home__right\"></div>\r\n      </div>\r\n\r\n      {showPostDetail && selectedPost && (\r\n        <PostDetail post={selectedPost} onClose={handleClosePostDetail} />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,YAAY;AACnB,OAAOC,MAAM,MAAM,uCAAuC;AAC1D,OAAOC,OAAO,MAAM,yCAAyC;AAC7D,OAAOC,QAAQ,MAAM,yCAAyC;AAC9D,OAAOC,UAAU,MAAM,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,SAASC,IAAIA,CAAA,EAAG;EAAAC,EAAA;EACd,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMe,WAAW,GAAIC,QAAQ,IAAK;IAChCN,OAAO,CAACM,QAAQ,CAAC;EACnB,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBP,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMQ,oBAAoB,GAAIC,IAAI,IAAK;IACrCP,eAAe,CAACO,IAAI,CAAC;IACrBL,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMM,qBAAqB,GAAGA,CAAA,KAAM;IAClCN,iBAAiB,CAAC,KAAK,CAAC;IACxBF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACEN,OAAA;IAAKe,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBhB,OAAA,CAACL,MAAM;MAACQ,IAAI,EAAEA,IAAK;MAACc,OAAO,EAAER,WAAY;MAACS,QAAQ,EAAEP;IAAa;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEpEtB,OAAA;MAAKe,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BhB,OAAA;QAAKe,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEb,IAAI,iBAAIH,OAAA,CAACJ,OAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE1DtB,OAAA;QAAKe,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBhB,OAAA,CAACH,QAAQ;UAAC0B,gBAAgB,EAAEX;QAAqB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAENtB,OAAA;QAAKe,SAAS,EAAC;MAAa;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,EAELf,cAAc,IAAIF,YAAY,iBAC7BL,OAAA,CAACF,UAAU;MAACe,IAAI,EAAER,YAAa;MAACmB,OAAO,EAAEV;IAAsB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAClE;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACpB,EAAA,CA1CQD,IAAI;AAAAwB,EAAA,GAAJxB,IAAI;AA4Cb,eAAeA,IAAI;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}