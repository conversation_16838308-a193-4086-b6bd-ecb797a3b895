{"ast": null, "code": "var _jsxFileName = \"D:\\\\Code\\\\FE_Blog\\\\src\\\\Routes\\\\AppLayout.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Routes, Route, useNavigate, Link } from \"react-router-dom\";\nimport \"../styles.css\";\nimport Home from \"../Pages/Home/Home\";\nimport About from \"../Pages/About/About\";\nimport Login from \"../Pages/Login/Login\";\nimport Register from \"../Pages/Register/Register\";\nimport UserProfile from \"../Pages/UserProfile/UserProfile\";\nimport NoMatch from \"../Pages/NotFound/NoMatch\";\nimport Posts from \"../Pages/Post/Posts\";\nimport Post from \"../Pages/Post/Post\";\nimport PostLists from \"../Pages/Post/PostLists\";\nimport Stats from \"../Stats\";\nimport NewPost from \"../Pages/Post/NewPost\";\nimport ProtectedRoute from \"../Routes/ProtectedRoute\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppLayout() {\n  _s();\n  const [user, setUser] = useState(null);\n  const navigate = useNavigate();\n\n  // Kiểm tra user từ localStorage khi component mount\n  useEffect(() => {\n    const storedUser = localStorage.getItem(\"user\");\n    if (storedUser) {\n      setUser(JSON.parse(storedUser));\n    }\n  }, []);\n  function handleLogin(userData) {\n    setUser(userData);\n  }\n  function logOut() {\n    setUser(null);\n    localStorage.removeItem(\"user\");\n    navigate(\"/\");\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Home, {\n          user: user,\n          onLogout: logOut\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/posts\",\n        element: /*#__PURE__*/_jsxDEV(Posts, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 39\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          index: true,\n          element: /*#__PURE__*/_jsxDEV(PostLists, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \":slug\",\n          element: /*#__PURE__*/_jsxDEV(Post, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), \" \", /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/about\",\n        element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {\n          onLogin: handleLogin\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/register\",\n        element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/profile/:userId\",\n        element: /*#__PURE__*/_jsxDEV(UserProfile, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/stats\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          user: user,\n          children: /*#__PURE__*/_jsxDEV(Stats, {\n            user: user\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/newpost\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          user: user,\n          children: /*#__PURE__*/_jsxDEV(NewPost, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(NoMatch, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n_s(AppLayout, \"/48TlfV0adSMEvsbW2gk5sMeopI=\", false, function () {\n  return [useNavigate];\n});\n_c = AppLayout;\nexport default AppLayout;\nvar _c;\n$RefreshReg$(_c, \"AppLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Routes", "Route", "useNavigate", "Link", "Home", "About", "<PERSON><PERSON>", "Register", "UserProfile", "NoMatch", "Posts", "Post", "PostLists", "Stats", "NewPost", "ProtectedRoute", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AppLayout", "_s", "user", "setUser", "navigate", "storedUser", "localStorage", "getItem", "JSON", "parse", "handleLogin", "userData", "logOut", "removeItem", "children", "path", "element", "onLogout", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "onLogin", "_c", "$RefreshReg$"], "sources": ["D:/Code/FE_Blog/src/Routes/AppLayout.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Routes, Route, useNavigate, Link } from \"react-router-dom\";\r\nimport \"../styles.css\";\r\nimport Home from \"../Pages/Home/Home\";\r\nimport About from \"../Pages/About/About\";\r\nimport Login from \"../Pages/Login/Login\";\r\nimport Register from \"../Pages/Register/Register\";\r\nimport UserProfile from \"../Pages/UserProfile/UserProfile\";\r\nimport NoMatch from \"../Pages/NotFound/NoMatch\";\r\nimport Posts from \"../Pages/Post/Posts\";\r\nimport Post from \"../Pages/Post/Post\";\r\nimport PostLists from \"../Pages/Post/PostLists\";\r\nimport Stats from \"../Stats\";\r\nimport NewPost from \"../Pages/Post/NewPost\";\r\nimport ProtectedRoute from \"../Routes/ProtectedRoute\";\r\n\r\nfunction AppLayout() {\r\n  const [user, setUser] = useState(null);\r\n  const navigate = useNavigate();\r\n\r\n  // <PERSON><PERSON><PERSON> tra user từ localStorage khi component mount\r\n  useEffect(() => {\r\n    const storedUser = localStorage.getItem(\"user\");\r\n    if (storedUser) {\r\n      setUser(JSON.parse(storedUser));\r\n    }\r\n  }, []);\r\n\r\n  function handleLogin(userData) {\r\n    setUser(userData);\r\n  }\r\n\r\n  function logOut() {\r\n    setUser(null);\r\n    localStorage.removeItem(\"user\");\r\n    navigate(\"/\");\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Routes>\r\n        <Route path=\"/\" element={<Home user={user} onLogout={logOut} />} />\r\n        <Route path=\"/posts\" element={<Posts />}>\r\n          <Route index element={<PostLists />} />\r\n          <Route path=\":slug\" element={<Post />} />\r\n        </Route>{\" \"}\r\n        <Route path=\"/about\" element={<About />} />\r\n        <Route path=\"/login\" element={<Login onLogin={handleLogin} />} />\r\n        <Route path=\"/register\" element={<Register />} />\r\n        <Route path=\"/profile/:userId\" element={<UserProfile />} />\r\n        <Route\r\n          path=\"/stats\"\r\n          element={\r\n            <ProtectedRoute user={user}>\r\n              <Stats user={user} />\r\n            </ProtectedRoute>\r\n          }\r\n        />\r\n        <Route\r\n          path=\"/newpost\"\r\n          element={\r\n            <ProtectedRoute user={user}>\r\n              <NewPost />\r\n            </ProtectedRoute>\r\n          }\r\n        />\r\n        <Route path=\"*\" element={<NoMatch />} />\r\n      </Routes>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default AppLayout;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACnE,OAAO,eAAe;AACtB,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,OAAO,MAAM,2BAA2B;AAC/C,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,cAAc,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM0B,QAAQ,GAAGtB,WAAW,CAAC,CAAC;;EAE9B;EACAH,SAAS,CAAC,MAAM;IACd,MAAM0B,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,IAAIF,UAAU,EAAE;MACdF,OAAO,CAACK,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,SAASK,WAAWA,CAACC,QAAQ,EAAE;IAC7BR,OAAO,CAACQ,QAAQ,CAAC;EACnB;EAEA,SAASC,MAAMA,CAAA,EAAG;IAChBT,OAAO,CAAC,IAAI,CAAC;IACbG,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;IAC/BT,QAAQ,CAAC,GAAG,CAAC;EACf;EAEA,oBACEP,OAAA,CAAAE,SAAA;IAAAe,QAAA,eACEjB,OAAA,CAACjB,MAAM;MAAAkC,QAAA,gBACLjB,OAAA,CAAChB,KAAK;QAACkC,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEnB,OAAA,CAACb,IAAI;UAACkB,IAAI,EAAEA,IAAK;UAACe,QAAQ,EAAEL;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnExB,OAAA,CAAChB,KAAK;QAACkC,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEnB,OAAA,CAACP,KAAK;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAP,QAAA,gBACtCjB,OAAA,CAAChB,KAAK;UAACyC,KAAK;UAACN,OAAO,eAAEnB,OAAA,CAACL,SAAS;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvCxB,OAAA,CAAChB,KAAK;UAACkC,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEnB,OAAA,CAACN,IAAI;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,EAAC,GAAG,eACZxB,OAAA,CAAChB,KAAK;QAACkC,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEnB,OAAA,CAACZ,KAAK;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CxB,OAAA,CAAChB,KAAK;QAACkC,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEnB,OAAA,CAACX,KAAK;UAACqC,OAAO,EAAEb;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjExB,OAAA,CAAChB,KAAK;QAACkC,IAAI,EAAC,WAAW;QAACC,OAAO,eAAEnB,OAAA,CAACV,QAAQ;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjDxB,OAAA,CAAChB,KAAK;QAACkC,IAAI,EAAC,kBAAkB;QAACC,OAAO,eAAEnB,OAAA,CAACT,WAAW;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DxB,OAAA,CAAChB,KAAK;QACJkC,IAAI,EAAC,QAAQ;QACbC,OAAO,eACLnB,OAAA,CAACF,cAAc;UAACO,IAAI,EAAEA,IAAK;UAAAY,QAAA,eACzBjB,OAAA,CAACJ,KAAK;YAACS,IAAI,EAAEA;UAAK;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFxB,OAAA,CAAChB,KAAK;QACJkC,IAAI,EAAC,UAAU;QACfC,OAAO,eACLnB,OAAA,CAACF,cAAc;UAACO,IAAI,EAAEA,IAAK;UAAAY,QAAA,eACzBjB,OAAA,CAACH,OAAO;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFxB,OAAA,CAAChB,KAAK;QAACkC,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEnB,OAAA,CAACR,OAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC;EAAC,gBACT,CAAC;AAEP;AAACpB,EAAA,CAtDQD,SAAS;EAAA,QAEClB,WAAW;AAAA;AAAA0C,EAAA,GAFrBxB,SAAS;AAwDlB,eAAeA,SAAS;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}